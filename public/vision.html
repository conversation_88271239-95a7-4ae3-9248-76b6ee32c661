<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>願景設計理念 - AstReal</title>
    <meta name="description" content="科技人眼中的占星 —— 一場理性與直覺的交會。了解的願景理念，用程式邏輯還原占星本質。">
    <meta name="keywords" content="占星,星盤,願景,設計理念,科技占星,理性占星">
    
    <!-- Open Graph -->
    <meta property="og:title" content="願景理念 - AstReal">
    <meta property="og:description" content="科技人眼中的占星 —— 一場理性與直覺的交會">
    <meta property="og:type" content="website">
    <meta property="og:url" content="https://astreal-d3f70.web.app/vision.html">
    
    <!-- Favicon -->
    <link rel="icon" type="image/png" href="favicon.png">
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            color: #333;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 40px;
            background: rgba(255, 255, 255, 0.95);
            padding: 40px 30px;
            border-radius: 20px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .header .subtitle {
            font-size: 1.2em;
            color: #666;
            font-style: italic;
            margin-bottom: 20px;
        }

        .header .emoji {
            font-size: 3em;
            margin-bottom: 20px;
        }

        .section {
            background: rgba(255, 255, 255, 0.95);
            margin-bottom: 30px;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
        }

        .section h2 {
            color: #667eea;
            margin-bottom: 20px;
            font-size: 1.5em;
            border-bottom: 2px solid #667eea;
            padding-bottom: 10px;
        }

        .section p {
            margin-bottom: 15px;
            text-align: justify;
        }

        .highlight-box {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
            text-align: center;
        }

        .highlight-box .icon {
            font-size: 1.5em;
            margin-bottom: 10px;
        }

        .work-list {
            list-style: none;
            padding: 0;
        }

        .work-list li {
            background: #f8f9ff;
            margin: 10px 0;
            padding: 15px;
            border-radius: 8px;
            border-left: 4px solid #667eea;
        }

        .work-list li strong {
            color: #667eea;
            display: block;
            margin-bottom: 5px;
        }

        .principles {
            display: grid;
            gap: 20px;
            margin: 20px 0;
        }

        .principle {
            background: #f8f9ff;
            padding: 20px;
            border-radius: 10px;
            border: 1px solid #e0e6ff;
        }

        .principle h3 {
            color: #667eea;
            margin-bottom: 10px;
        }

        .conclusion {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            text-align: center;
            padding: 30px;
            border-radius: 15px;
            margin-top: 40px;
        }

        .conclusion .sparkle {
            font-size: 1.2em;
            margin: 10px 0;
        }

        .nav-links {
            text-align: center;
            margin: 30px 0;
        }

        .nav-links a {
            display: inline-block;
            margin: 0 15px;
            padding: 12px 24px;
            background: rgba(255, 255, 255, 0.9);
            color: #667eea;
            text-decoration: none;
            border-radius: 25px;
            font-weight: 500;
            transition: all 0.3s ease;
            box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
        }

        .nav-links a:hover {
            background: white;
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        }

        @media (max-width: 768px) {
            .container {
                padding: 15px;
            }
            
            .header h1 {
                font-size: 2em;
            }
            
            .section {
                padding: 20px;
            }
            
            .nav-links a {
                display: block;
                margin: 10px 0;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 導航連結 -->
        <div class="nav-links">
            <a href="landing.html">首頁</a>
            <a href="download.html">下載應用</a>
            <a href="privacy-policy.html">隱私權條款</a>
            <a href="terms-of-service.html">服務條款</a>
        </div>

        <!-- 主標題 -->
        <div class="header">
            <div class="emoji">💫</div>
            <h1>科技人眼中的占星</h1>
            <div class="subtitle">一場理性與直覺的交會</div>
        </div>

        <!-- 引言 -->
        <div class="section">
            <p>在資訊爆炸、科學掛帥的現代社會，「占星」這個詞往往被簡化成「今天運勢如何」、「你是什麼星座」，甚至與「玄學」「迷信」畫上等號。作為一名軟體工程師，我也曾對這些標籤感到懷疑。但隨著深入研究與實際觀察，我開始發現，占星其實是一門結構嚴謹、邏輯清晰、歷史悠久的象徵系統。</p>
            
            <div class="highlight-box">
                <div class="icon">💡</div>
                <strong>這不只是一場興趣，更是一項任務：</strong><br>
                我希望透過理性與科學方法，驗證占星作為一種心理映照與人生導航工具，是否真能對人產生實質幫助。
            </div>
        </div>

        <!-- 為什麼是工程師來談占星 -->
        <div class="section">
            <h2>🚀 為什麼是工程師來談占星？</h2>
            
            <div class="principles">
                <div class="principle">
                    <h3>我們懂邏輯</h3>
                    <p>占星圖（Horoscope）就是一種數據圖譜，背後有座標計算、時間演算、行星模型、相位矩陣等複雜邏輯。這些不只是神祕圖形，而是可以被演算法處理、可視化呈現的資訊網路。</p>
                </div>
                
                <div class="principle">
                    <h3>我們追求驗證</h3>
                    <p>對於一個工程師來說，任何技術的可用性，都應該透過實際測試、用戶回饋、數據追蹤來檢驗。占星技術也應不例外。</p>
                </div>
                
                <div class="principle">
                    <h3>我們懂工具可以怎麼幫助人</h3>
                    <p>如果一個 App 能幫助你記帳、一個演算法能幫助你配對，那麼，一套好的占星系統，是否也能幫助人看清方向、理解困境、做出更合適的選擇？</p>
                </div>
            </div>
        </div>

        <!-- 給占星一個理性的可能性 -->
        <div class="section">
            <h2>🧭 給占星一個理性的可能性</h2>
            <p>我相信，不是人們不願相信，而是太多人被錯誤包裝、斷章取義或神化的版本誤導，才對占星產生排斥。</p>
            
            <p><strong>所以我想做的，是這樣一件事：</strong></p>
            <ul class="work-list">
                <li>用程式邏輯，還原占星的本質</li>
                <li>用實際案例，驗證占星的可用性</li>
                <li>用理性的語言，重新介紹這項古老又未被完全理解的技術</li>
            </ul>
        </div>

        <!-- 這不只是神秘學，也是人性學 -->
        <div class="section">
            <h2>🌟 這不只是神秘學，也是人性學</h2>
            <p>占星不是預言，不是宿命論，而是一種觀察與理解人生的語言。它讓我們用更宏觀的角度，思考「為什麼我會在這裡？」「我與他人的連結是什麼？」「現在是該衝還是該守？」</p>
            
            <p><em>我不求說服每個人，但我希望，給願意理解的人，一個清楚的入口；給已經相信的人，一個更精準的工具。</em></p>
        </div>

        <!-- 結語 -->
        <div class="conclusion">
            <p>如果你也曾好奇，卻苦於沒有科學視角的說明；<br>
            如果你對人生充滿問號，卻不想被靈性說詞帶著走；<br>
            那麼，我們也許可以一起，把占星這門古老的技術，帶入一個新的未來。</p>
            
            <div class="sparkle">✨ 理性與直覺的完美結合 ✨</div>
        </div>

        <!-- 底部導航 -->
        <div class="nav-links">
            <a href="landing.html">返回首頁</a>
            <a href="download.html">立即下載</a>
        </div>
    </div>
</body>
</html>

import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../../astreal.dart';
import '../../presentation/pages/birth_data_form_page.dart';
import '../../presentation/pages/chart_page_new.dart';
import '../../presentation/pages/person_selector_page.dart';

/// 人物信息卡片組件，用於顯示當前選中的人物信息並提供選擇/更改人物的功能
class PersonInfoCard extends StatelessWidget {
  /// 當前選中的人物
  final BirthData? selectedPerson;

  /// 人物列表
  final List<BirthData> personList;

  /// 選擇人物時的回調函數
  final Function(BirthData) onPersonSelected;

  /// 編輯人物時的回調函數
  final Function(BirthData)? onEditPerson;

  /// 是否顯示編輯按鈕
  final bool showEditButton;

  /// 卡片標題
  final String title;

  /// 卡片圖標
  final IconData icon;

  /// 圖標顏色
  final Color iconColor;

  /// 對話框標題
  final String dialogTitle;

  /// 對話框按鈕顏色
  final Color dialogButtonColor;

  /// 是否正在加載數據
  final bool isLoading;

  /// 格式化日期時間的函數
  final String Function(DateTime) formatDateTime;

  /// 構造函數
  const PersonInfoCard({
    super.key,
    required this.selectedPerson,
    required this.personList,
    required this.onPersonSelected,
    required this.formatDateTime,
    this.onEditPerson,
    this.showEditButton = true,
    this.title = '個人資訊',
    this.icon = Icons.person,
    this.iconColor = AppColors.solarAmber,
    this.dialogTitle = '選擇人物',
    this.dialogButtonColor = AppColors.royalIndigo,
    this.isLoading = false,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 2,
      color: Colors.white,
      margin: EdgeInsets.zero,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: InkWell(
        onTap: () {
          if (selectedPerson != null) {
            _navigateToNatalChart(context, selectedPerson!);
          } else {
            _showPersonSelectionPage(context);
          }
        },
        borderRadius: BorderRadius.circular(16),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 標題行 - 緊湊設計
              Row(
                children: [
                  // 標題和圖標
                  Icon(icon, color: iconColor, size: 18),
                  const SizedBox(width: 6),
                  Text(
                    title,
                    style: const TextStyle(
                      fontSize: 15,
                      fontWeight: FontWeight.bold,
                      color: AppColors.textDark,
                    ),
                  ),
                  const Spacer(),
                  // 緊湊的操作按鈕
                  if (selectedPerson != null) ...[
                    _buildCompactButton(
                      icon: Icons.visibility,
                      tooltip: '查看星盤',
                      onPressed: () =>
                          _navigateToNatalChart(context, selectedPerson!),
                    ),
                    const SizedBox(width: 4),
                  ],
                  if (showEditButton &&
                      selectedPerson != null &&
                      onEditPerson != null) ...[
                    _buildCompactButton(
                      icon: Icons.edit,
                      tooltip: '編輯資料',
                      onPressed: () => onEditPerson!(selectedPerson!),
                    ),
                    const SizedBox(width: 4),
                  ],
                  _buildCompactButton(
                    icon: Icons.person_search,
                    tooltip: '選擇人物',
                    onPressed: () => _showPersonSelectionPage(context),
                  ),
                ],
              ),
              const SizedBox(height: 8),
              // 內容區域
              if (isLoading)
                const Center(
                  child: Padding(
                    padding: EdgeInsets.symmetric(vertical: 16),
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor:
                          AlwaysStoppedAnimation<Color>(AppColors.royalIndigo),
                    ),
                  ),
                )
              else if (selectedPerson == null)
                _buildEmptyState()
              else
                _buildPersonInfo(selectedPerson!),
            ],
          ),
        ),
      ),
    );
  }

  /// 構建緊湊按鈕
  Widget _buildCompactButton({
    required IconData icon,
    required String tooltip,
    required VoidCallback onPressed,
  }) {
    return Container(
      width: 28,
      height: 28,
      decoration: BoxDecoration(
        color: AppColors.royalIndigo.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(6),
      ),
      child: IconButton(
        icon: Icon(icon, size: 14),
        onPressed: onPressed,
        tooltip: tooltip,
        padding: EdgeInsets.zero,
        style: IconButton.styleFrom(
          foregroundColor: AppColors.royalIndigo,
          minimumSize: const Size(28, 28),
          tapTargetSize: MaterialTapTargetSize.shrinkWrap,
        ),
      ),
    );
  }

  /// 構建空狀態
  Widget _buildEmptyState() {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 16),
      child: const Row(
        children: [
          Icon(
            Icons.person_add_alt_1,
            size: 32,
            color: AppColors.textMedium,
          ),
          SizedBox(width: 12),
          Expanded(
            child: Text(
              '點擊選擇人物以顯示個人資訊',
              style: TextStyle(
                fontSize: 14,
                color: AppColors.textMedium,
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 構建人物信息
  Widget _buildPersonInfo(BirthData person) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 姓名
        Text(
          person.name,
          style: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: AppColors.textDark,
          ),
        ),
        const SizedBox(height: 4),
        // 出生地點
        Row(
          children: [
            const Icon(Icons.location_on,
                size: 14, color: AppColors.textMedium),
            const SizedBox(width: 4),
            Expanded(
              child: Text(
                person.birthPlace,
                style: const TextStyle(
                  fontSize: 13,
                  color: AppColors.textMedium,
                ),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ],
        ),
        const SizedBox(height: 2),
        // 出生日期時間和類別
        Row(
          children: [
            const Icon(Icons.access_time,
                size: 14, color: AppColors.textMedium),
            const SizedBox(width: 4),
            Expanded(
              child: Text(
                _formatBirthDateTime(person),
                style: const TextStyle(
                  fontSize: 13,
                  color: AppColors.textMedium,
                ),
              ),
            ),
            const SizedBox(width: 8),
            _buildCategoryChip(person.category),
          ],
        ),
        // 備註（如果有）
        if (person.notes != null && person.notes!.isNotEmpty) ...[
          const SizedBox(height: 4),
          Row(
            children: [
              const Icon(Icons.note, size: 14, color: AppColors.textMedium),
              const SizedBox(width: 4),
              Expanded(
                child: Text(
                  person.notes!,
                  style: const TextStyle(
                    fontSize: 12,
                    color: AppColors.textMedium,
                  ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ],
          ),
        ],
      ],
    );
  }

  /// 構建類別標籤
  Widget _buildCategoryChip(ChartCategory category) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
      decoration: BoxDecoration(
        color: category.color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: category.color.withValues(alpha: 0.3),
          width: 0.5,
        ),
      ),
      child: Text(
        category.displayName,
        style: TextStyle(
          fontSize: 10,
          fontWeight: FontWeight.w500,
          color: category.color,
        ),
      ),
    );
  }

  /// 顯示人物選擇頁面
  Future<void> _showPersonSelectionPage(BuildContext context) async {
    if (personList.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('沒有可用的出生資料，請先新增出生資料')),
      );
      // 導航到新增出生資料頁面
      await _navigateToAddBirthData(context);
      return;
    }

    final BirthData? result = await PersonSelectorPage.show(
      context: context,
      birthDataList: personList,
      title: dialogTitle,
      buttonColor: dialogButtonColor,
    );

    if (result != null) {
      onPersonSelected(result);
    }
  }

  /// 導航到本命盤頁面
  void _navigateToNatalChart(BuildContext context, BirthData birthData) {
    // 創建 ChartData 對象
    final chartData = ChartData(
      chartType: ChartType.natal,
      primaryPerson: birthData,
    );

    // 導航到星盤頁面
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => ChangeNotifierProvider(
          create: (_) => ChartViewModel.withChartData(
            initialChartData: chartData,
            context: context,
          ),
          child: ChartPageNew(chartData: chartData),
        ),
      ),
    );
  }

  /// 導航到新增出生資料頁面
  Future<void> _navigateToAddBirthData(BuildContext context) async {
    try {
      final result = await Navigator.push<BirthData>(
        context,
        MaterialPageRoute(
          builder: (context) => const BirthDataFormPage(),
        ),
      );

      if (result != null) {
        // 新增成功，選中新建的資料
        onPersonSelected(result);

        if (context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('出生資料已新增並選中'),
              backgroundColor: AppColors.successGreen,
            ),
          );
        }
      }
    } catch (e) {
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('新增出生資料時出錯：$e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  /// 格式化出生時間，包含不確定標記
  String _formatBirthDateTime(BirthData birthData) {
    final dateTimeStr = formatDateTime(birthData.dateTime);
    if (birthData.isTimeUncertain) {
      return '$dateTimeStr (時間不確定)';
    }
    return dateTimeStr;
  }
}

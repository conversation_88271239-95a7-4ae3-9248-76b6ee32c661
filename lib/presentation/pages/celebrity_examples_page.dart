import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:provider/provider.dart';

import '../../astreal.dart';
import '../../data/services/api/astrology_service.dart';
import '../../data/services/api/celebrity_examples_service.dart';
import 'ai_interpretation_selection_page.dart';

/// 名人解讀範例頁面
class CelebrityExamplesPage extends StatefulWidget {
  const CelebrityExamplesPage({super.key});

  @override
  State<CelebrityExamplesPage> createState() => _CelebrityExamplesPageState();
}

class _CelebrityExamplesPageState extends State<CelebrityExamplesPage>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  final CelebrityExamplesService _service = CelebrityExamplesService();
  String _searchQuery = '';
  bool _isRefreshing = false;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 6, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('名人解讀範例'),
        backgroundColor: AppColors.royalIndigo,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: _isRefreshing
                ? const SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                    ),
                  )
                : const Icon(Icons.refresh),
            onPressed: _isRefreshing ? null : _refreshExamples,
            tooltip: '刷新範例',
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          indicatorColor: AppColors.solarAmber,
          tabs: const [
            Tab(text: '熱門'),
            Tab(text: '政治'),
            Tab(text: '娛樂'),
            Tab(text: '學者'),
            Tab(text: '作家'),
            Tab(text: '其他'),
          ],
        ),
      ),
      body: Column(
        children: [
          // 搜尋欄
          _buildSearchBar(),
          
          // 說明文字
          _buildDescription(),
          
          // 內容區域
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: [
                _buildExamplesList(_getFilteredExamples(_service.getPopularExamples())),
                _buildExamplesList(_getFilteredExamples(_service.getExamplesByCategory(CelebrityCategory.politician))),
                _buildExamplesList(_getFilteredExamples(_service.getExamplesByCategory(CelebrityCategory.entertainment))),
                _buildExamplesList(_getFilteredExamples(_service.getExamplesByCategory(CelebrityCategory.scholar))),
                _buildExamplesList(_getFilteredExamples(_service.getExamplesByCategory(CelebrityCategory.writer))),
                _buildExamplesList(_getFilteredExamples(_service.getExamplesByCategory(CelebrityCategory.other))),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// 刷新名人範例
  Future<void> _refreshExamples() async {
    if (_isRefreshing) return;

    setState(() {
      _isRefreshing = true;
    });

    try {
      await _service.refreshExamples();
      if (mounted) {
        setState(() {
          // 觸發重建以顯示新的範例
        });

        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Row(
              children: [
                Icon(Icons.check_circle, color: Colors.white),
                SizedBox(width: 8),
                Text('名人範例已更新'),
              ],
            ),
            backgroundColor: Colors.green,
            duration: Duration(seconds: 2),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                const Icon(Icons.error, color: Colors.white),
                const SizedBox(width: 8),
                Expanded(child: Text('刷新失敗：$e')),
              ],
            ),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 3),
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isRefreshing = false;
        });
      }
    }
  }

  /// 構建搜尋欄
  Widget _buildSearchBar() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: TextField(
        decoration: InputDecoration(
          hintText: '搜尋名人姓名或特點...',
          prefixIcon: const Icon(Icons.search),
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
            borderSide: BorderSide(color: Colors.grey.shade300),
          ),
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
            borderSide: const BorderSide(color: AppColors.royalIndigo),
          ),
        ),
        onChanged: (value) {
          setState(() {
            _searchQuery = value;
          });
        },
      ),
    );
  }

  /// 構建說明文字
  Widget _buildDescription() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: AppColors.royalIndigo.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: AppColors.royalIndigo.withValues(alpha: 0.2)),
      ),
      child: Row(
        children: [
          const Icon(
            Icons.info_outline,
            color: AppColors.royalIndigo,
            size: 20,
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              '選擇名人範例來體驗深入剖析功能，了解不同類型的占星解讀。',
              style: TextStyle(
                fontSize: 13,
                color: Colors.grey.shade700,
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 獲取過濾後的範例
  List<CelebrityExample> _getFilteredExamples(List<CelebrityExample> examples) {
    if (_searchQuery.isEmpty) {
      return examples;
    }
    return _service.searchExamples(_searchQuery).where((example) => examples.contains(example)).toList();
  }

  /// 構建範例列表
  Widget _buildExamplesList(List<CelebrityExample> examples) {
    if (examples.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.search_off,
              size: 64,
              color: Colors.grey.shade400,
            ),
            const SizedBox(height: 16),
            Text(
              _searchQuery.isEmpty ? '暫無範例' : '找不到符合條件的名人',
              style: TextStyle(
                fontSize: 16,
                color: Colors.grey.shade600,
              ),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: examples.length,
      itemBuilder: (context, index) {
        final example = examples[index];
        return Padding(
          padding: const EdgeInsets.only(bottom: 12),
          child: _buildExampleCard(example),
        );
      },
    );
  }

  /// 構建範例卡片
  Widget _buildExampleCard(CelebrityExample example) {
    return StyledCard(
      elevation: 2,
      onTap: () => _navigateToInterpretation(example),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                // 類別圖示
                Container(
                  width: 48,
                  height: 48,
                  decoration: BoxDecoration(
                    color: example.category.color.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Icon(
                    example.category.icon,
                    color: example.category.color,
                    size: 24,
                  ),
                ),
                const SizedBox(width: 16),
                
                // 名人資訊
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Expanded(
                            child: Text(
                              example.name,
                              style: const TextStyle(
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                                color: AppColors.textDark,
                              ),
                            ),
                          ),
                          if (example.isPopular) ...[
                            Container(
                              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                              decoration: BoxDecoration(
                                color: AppColors.solarAmber.withValues(alpha: 0.2),
                                borderRadius: BorderRadius.circular(12),
                              ),
                              child: const Text(
                                '熱門',
                                style: TextStyle(
                                  fontSize: 12,
                                  color: AppColors.solarAmber,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ),
                          ],
                        ],
                      ),
                      const SizedBox(height: 4),
                      Text(
                        example.category.displayName,
                        style: TextStyle(
                          fontSize: 14,
                          color: example.category.color,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                ),
                
                const Icon(
                  Icons.arrow_forward_ios,
                  color: AppColors.royalIndigo,
                  size: 16,
                ),
              ],
            ),
            
            const SizedBox(height: 12),
            
            // 出生資訊
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.grey.shade50,
                borderRadius: BorderRadius.circular(8),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    '出生資料',
                    style: TextStyle(
                      fontSize: 12,
                      fontWeight: FontWeight.bold,
                      color: Colors.grey.shade700,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    '${DateFormat('yyyy/M/d HH:mm').format(example.birthData.dateTime)} • ${example.birthData.birthPlace}',
                    style: TextStyle(
                      fontSize: 13,
                      color: Colors.grey.shade600,
                    ),
                  ),
                ],
              ),
            ),
            
            const SizedBox(height: 12),
            
            // 特點描述
            Text(
              example.description,
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey.shade700,
                height: 1.4,
              ),
            ),
            
            const SizedBox(height: 12),
            
            // 推薦主題
            Wrap(
              spacing: 8,
              runSpacing: 4,
              children: example.recommendedTopics.take(3).map((topic) {
                return Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: AppColors.royalIndigo.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    topic,
                    style: const TextStyle(
                      fontSize: 12,
                      color: AppColors.royalIndigo,
                    ),
                  ),
                );
              }).toList(),
            ),
          ],
        ),
      ),
    );
  }

  /// 導航到解讀頁面
  Future<void> _navigateToInterpretation(CelebrityExample example) async {
    ChartData chartData = ChartData(
      chartType: ChartType.natal,
      primaryPerson: example.birthData,
    );
    chartData = await AstrologyService().calculateChartData(chartData);

    if (mounted) {
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => ChangeNotifierProvider(
            create: (_) => ChartViewModel.withChartData(initialChartData: chartData),
            child: AIInterpretationSelectionPage(chartData: chartData),
          ),
        ),
      );
    }
  }
}

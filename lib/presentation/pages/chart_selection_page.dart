import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../../../shared/utils/geocoding_service.dart';
import '../../astreal.dart';
import '../../data/services/api/astro_calendar_service.dart';
import '../../data/services/api/equinox_solstice_service.dart';
// 暫時註釋掉，等待創建正確的路徑
// import '../main/firdaria_page_route.dart';
import '../../shared/widgets/common/date_time_picker_bottom_sheet.dart';
import '../../shared/widgets/common/responsive_wrapper.dart';
import '../../shared/widgets/recent_persons_section.dart';
import '../viewmodels/files_viewmodel.dart';
import '../viewmodels/recent_charts_viewmodel.dart';
import '../viewmodels/recent_persons_viewmodel.dart';
import 'ai_interpretation_selection_page.dart';
import 'chart_page_new.dart';
import 'person_selector_page.dart';

// 資料需求類
class DataRequirement {
  final String label;
  final bool isCompleted;
  final String? description;

  const DataRequirement({
    required this.label,
    required this.isCompleted,
    this.description,
  });
}

class ChartSelectionPage extends StatefulWidget {
  final BirthData? primaryPerson; // 可以為 null，讓用戶在頁面中選擇
  final List<BirthData>? allPeople;
  final BirthData? secondaryPerson; // 第二個人參數
  final ChartType? initialChartType; // 初始星盤類型
  final bool isChangingChartType; // 是否為切換星盤類型模式
  final DateTime? specificDate; // 特定日期
  final bool fromAnalysisPage; // 是否來自分析頁面

  const ChartSelectionPage({
    super.key,
    this.primaryPerson, // 改為可選參數
    this.allPeople,
    this.secondaryPerson,
    this.initialChartType,
    this.isChangingChartType = false,
    this.specificDate,
    this.fromAnalysisPage = false, // 預設為 false
  });

  @override
  State<ChartSelectionPage> createState() => _ChartSelectionPageState();
}

class _ChartSelectionPageState extends State<ChartSelectionPage> {
  // 選擇的星盤類型
  ChartType _selectedChartType = ChartType.natal;

  // 主要人物（可以為 null，需要用戶選擇）
  BirthData? _primaryPerson;

  // 選擇的第二個人（用於合盤）
  BirthData? _selectedSecondaryPerson;

  // 選擇的特定日期（用於推運和天象盤）
  DateTime _selectedDate = DateTime.now();

  // 選擇的地點（用於天象盤和二分二至盤）
  String _selectedLocation = '台北市';
  double _selectedLatitude = 25.0330;
  double _selectedLongitude = 121.5654;
  final TextEditingController _locationController = TextEditingController();

  // 選擇的年份（用於二分二至盤）
  int _selectedYear = DateTime.now().year;

  // 選擇的季節（用於二分二至盤）
  String _selectedSeason = '春分'; // 春分、夏至、秋分、冬至

  // 是否正在載入位置
  bool _isLoadingLocation = false;

  // 是否需要更新星盤
  bool _needsChartUpdate = false;

  // 上次的資料狀態（用於檢測變動）
  String? _lastDataHash;

  // 日月蝕選擇相關
  String _selectedEclipseFilter = '全部'; // 全部、日蝕、月蝕
  List<AstroEvent> _availableEclipses = [];
  AstroEvent? _selectedEclipse;
  bool _isLoadingEclipses = false;

  // 星盤類型對應的圖標
  final Map<ChartType, IconData> _chartTypeIcons = {
    ChartType.natal: Icons.person,
    ChartType.transit: Icons.access_time,
    ChartType.synastry: Icons.people,
    ChartType.composite: Icons.merge_type,
    ChartType.davison: Icons.compare_arrows,
    ChartType.marks: Icons.connect_without_contact,
    ChartType.secondaryProgression: Icons.timeline,
    ChartType.tertiaryProgression: Icons.show_chart,
    ChartType.solarArcDirection: Icons.wb_sunny,
    ChartType.solarReturn: Icons.replay,
    ChartType.lunarReturn: Icons.nightlight_round,
    // ChartType.electional: Icons.event_available,
    ChartType.horary: Icons.help_outline,
    ChartType.event: Icons.event,
    ChartType.firdaria: Icons.hourglass_full,
    // ChartType.fixedStars: Icons.star,
    // ChartType.harmonic: Icons.waves,
    // ChartType.draconic: Icons.change_circle,
    // ChartType.localSpace: Icons.location_on,
    ChartType.mundane: Icons.public,
    ChartType.synastrySecondary: Icons.compare,
    ChartType.synastryTertiary: Icons.compare_arrows,
    ChartType.compositeSecondary: Icons.merge,
    ChartType.compositeTertiary: Icons.merge_type,
    ChartType.davisonSecondary: Icons.compare,
    ChartType.davisonTertiary: Icons.compare_arrows,
    ChartType.marksSecondary: Icons.connect_without_contact,
    ChartType.marksTertiary: Icons.connecting_airports,
    ChartType.eclipse: Icons.brightness_2,
  };

  @override
  void initState() {
    super.initState();

    // 初始化主要人物（可能為 null）
    _primaryPerson = widget.primaryPerson;

    // 初始化地點控制器
    _locationController.text = _selectedLocation;

    // 如果有初始星盤類型，則設置為預設選擇
    if (widget.initialChartType != null) {
      _selectedChartType = widget.initialChartType!;
    }

    // 如果有第二個人參數，自動設置
    if (widget.secondaryPerson != null) {
      // 設置第二個人
      _selectedSecondaryPerson = widget.secondaryPerson;

      // 如果沒有初始星盤類型，設置預設合盤類型
      if (widget.initialChartType == null) {
        // 預設選擇合盤類型
        _selectedChartType = ChartType.synastry;
      }
    }

    // 如果有特定日期參數，設置選擇的日期
    if (widget.specificDate != null) {
      _selectedDate = widget.specificDate!;
    }

    // 日月蝕盤不在初始化時載入事件，等用戶點擊查看星盤時再載入

    // 加載出生資料以顯示近期選中人物
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadBirthDataForRecentPersons();
    });
  }

  // 加載出生資料以顯示近期選中人物
  void _loadBirthDataForRecentPersons() async {
    final filesViewModel = Provider.of<FilesViewModel>(context, listen: false);
    await filesViewModel.loadBirthData();
  }

  @override
  void dispose() {
    _locationController.dispose();
    super.dispose();
  }

  /// 檢測資料是否有變動
  bool _hasDataChanged() {
    final currentDataHash = _generateDataHash();
    if (_lastDataHash != currentDataHash) {
      _lastDataHash = currentDataHash;
      return true;
    }
    return false;
  }

  /// 生成當前資料的雜湊值
  String _generateDataHash() {
    final buffer = StringBuffer();
    buffer.write(_selectedChartType.toString());
    buffer.write(_primaryPerson?.id ?? 'null');
    buffer.write(_selectedSecondaryPerson?.id ?? 'null');
    buffer.write(_selectedDate.millisecondsSinceEpoch);
    buffer.write(_selectedLocation);
    buffer.write(_selectedLatitude);
    buffer.write(_selectedLongitude);
    buffer.write(_selectedYear);
    buffer.write(_selectedSeason);
    buffer.write(_selectedEclipseFilter);
    buffer.write(_selectedEclipse?.id ?? 'null');
    return buffer.toString().hashCode.toString();
  }

  /// 標記需要更新星盤
  void _markChartNeedsUpdate() {
    if (_hasDataChanged()) {
      setState(() {
        _needsChartUpdate = true;
      });
      debugPrint('資料已變動，標記需要更新星盤');
    }
  }

  /// 重新計算星盤
  Future<void> _recalculateChart() async {
    if (!_needsChartUpdate) return;

    try {
      debugPrint('開始重新計算星盤...');

      // 檢查資料完整性
      final requirements = _getDataRequirements();
      final allCompleted = requirements.every((req) => req.isCompleted);

      if (!allCompleted) {
        debugPrint('資料不完整，跳過星盤計算');
        return;
      }

      // 創建新的 ChartData
      final chartData = await _createChartData();
      if (chartData == null) return;

      // 如果是切換星盤類型模式，通知父組件
      if (widget.isChangingChartType && mounted) {
        // 可以在這裡發送通知或回調
        debugPrint('星盤類型已變更，新類型：${chartData.chartType}');
      }

      setState(() {
        _needsChartUpdate = false;
      });

      debugPrint('星盤重新計算完成');
    } catch (e) {
      debugPrint('重新計算星盤時發生錯誤：$e');
    }
  }

  /// 創建 ChartData 對象
  Future<ChartData?> _createChartData() async {
    try {
      if (_selectedChartType == ChartType.mundane) {
        // 天象盤使用選擇的時間和地點創建虛擬人物
        return ChartData(
          chartType: _selectedChartType,
          primaryPerson: BirthData(
            id: 'mundane_${_selectedDate.millisecondsSinceEpoch}',
            name: '天象盤',
            dateTime: _selectedDate,
            birthPlace: _selectedLocation,
            latitude: _selectedLatitude,
            longitude: _selectedLongitude,
          ),
          specificDate: _selectedDate,
        );
      } else if (_selectedChartType == ChartType.equinoxSolstice) {
        // 二分二至盤使用選擇的年份、季節和地點創建虛擬人物
        final seasonDateTime =
            await _calculateSeasonDateTime(_selectedSeason, _selectedYear);

        return ChartData(
          chartType: _selectedChartType,
          primaryPerson: BirthData(
            id: 'equinox_solstice_${_selectedYear}_${_selectedSeason}_${_selectedLocation.hashCode}',
            name: '$_selectedYear年$_selectedSeason盤',
            dateTime: seasonDateTime,
            birthPlace: _selectedLocation,
            latitude: _selectedLatitude,
            longitude: _selectedLongitude,
          ),
          secondaryPerson: _primaryPerson, // 如果有選擇人物，用於比較
          specificDate: seasonDateTime,
        );
      } else if (_selectedChartType == ChartType.eclipse) {
        // 日月蝕盤邏輯
        if (_selectedEclipse == null) return null;

        return ChartData(
          chartType: _selectedChartType,
          primaryPerson: BirthData(
            id: 'eclipse_${_selectedEclipse!.id}_${_selectedLocation.hashCode}',
            name: '${_selectedEclipse!.title} - $_selectedLocation',
            dateTime: _selectedEclipse!.dateTime,
            birthPlace: _selectedLocation,
            latitude: _selectedLatitude,
            longitude: _selectedLongitude,
          ),
          secondaryPerson: _primaryPerson, // 如果有選擇人物，用於比較
          specificDate: _selectedEclipse!.dateTime,
        );
      } else {
        // 其他星盤類型使用正常邏輯
        if (_primaryPerson == null) return null;

        return ChartData(
          chartType: _selectedChartType,
          primaryPerson: _primaryPerson!,
          secondaryPerson: _selectedSecondaryPerson,
          specificDate:
              _selectedChartType.requiresSpecificDate ? _selectedDate : null,
        );
      }
    } catch (e) {
      debugPrint('創建 ChartData 時發生錯誤：$e');
      return null;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('選擇星盤類型'),
      ),
      body: ResponsivePageWrapper(
        maxWidth: 1000.0, // 星盤選擇頁面需要中等偏大的寬度
        child: SafeArea(
          child: Column(
            children: [
              // 星盤類型選擇區域
              Expanded(
                child: _buildCompactChartsView(),
              ),
            ],
          ),
        ),
      ),
      bottomNavigationBar: _buildBottomBar(),
    );
  }

  // 構建人物信息卡片區域
  Widget _buildPersonsSection() {
    return Column(
      mainAxisSize: MainAxisSize.min, // 確保列只佔用必要的空間
      children: [
        // 近期選中人物區域
        _buildRecentPersonsSection(),

        const SizedBox(height: 8),

        // 主要人物卡片或選擇按鈕
        _primaryPerson != null
            ? _buildCompactPersonCard(
                person: _primaryPerson!,
                isPrimary: true,
                onChangePerson: _changePrimaryPerson,
              )
            : _buildCompactSelectPrimaryPersonCard(),

        // 如果需要第二個人或已經選擇了第二個人，顯示第二個人卡片
        if (_selectedChartType.requiresTwoPersons ||
            _selectedSecondaryPerson != null) ...[
          const SizedBox(height: 8),

          // 如果已經選擇了第二個人，顯示其卡片，否則顯示選擇按鈕
          _selectedSecondaryPerson != null
              ? _buildCompactPersonCard(
                  person: _selectedSecondaryPerson!,
                  isPrimary: false,
                  onChangePerson: _changeSecondaryPerson,
                )
              : _buildCompactSelectSecondaryPersonCard(),

          // 如果已經選擇了兩個人，顯示交換按鈕
          if (_selectedSecondaryPerson != null && _primaryPerson != null) ...[
            const SizedBox(height: 8),
            _buildCompactSwapPersonsButton(),
          ],
        ],
      ],
    );
  }

  // 構建近期選中人物區域
  Widget _buildRecentPersonsSection() {
    return Consumer<FilesViewModel>(
      builder: (context, filesViewModel, child) {
        return RecentPersonsSection(
          allPersons: filesViewModel.birthDataList,
          onPersonSelected: _selectRecentPerson,
          maxCount: 5,
          showClearButton: true,
          hideWhenSearching: false,
          title: '近期選中',
          icon: Icons.history,
          themeColor: AppColors.royalIndigo,
          showSelectedState: true,
          isPersonSelected: (person) =>
              _primaryPerson?.id == person.id ||
              _selectedSecondaryPerson?.id == person.id,
          onPersonLongPressed: _showRecentPersonOptions,
        );
      },
    );
  }

  // 選擇近期人物
  void _selectRecentPerson(BirthData person) {
    // 記錄選中的人物
    try {
      final recentPersonsViewModel =
          Provider.of<RecentPersonsViewModel>(context, listen: false);
      recentPersonsViewModel.recordSelectedPerson(person);
    } catch (e) {
      // 如果記錄失敗，不影響選擇功能
    }

    setState(() {
      if (_primaryPerson == null) {
        // 如果沒有主要人物，設置為主要人物
        _primaryPerson = person;
      } else if (_selectedChartType.requiresTwoPersons &&
          _selectedSecondaryPerson == null &&
          _primaryPerson!.id != person.id) {
        // 如果需要第二個人且還沒選擇，設置為第二個人
        _selectedSecondaryPerson = person;
      } else if (_primaryPerson!.id != person.id) {
        // 否則替換主要人物
        _primaryPerson = person;
      }
    });
  }

  // 顯示近期人物選項
  void _showRecentPersonOptions(BirthData person) {
    showModalBottomSheet(
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (BuildContext context) {
        return SafeArea(
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 16.0),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: <Widget>[
                // 標題列
                Padding(
                  padding: const EdgeInsets.only(
                      left: 16.0, right: 16.0, bottom: 16.0),
                  child: Row(
                    children: [
                      Text(
                        person.name,
                        style: const TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: AppColors.textDark,
                        ),
                      ),
                      const Spacer(),
                      IconButton(
                        icon: const Icon(Icons.close,
                            color: AppColors.textMedium),
                        onPressed: () => Navigator.pop(context),
                      ),
                    ],
                  ),
                ),
                const Divider(height: 1),
                // 選擇為主要人物選項
                ListTile(
                  leading: Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: AppColors.royalIndigo.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child:
                        const Icon(Icons.person, color: AppColors.royalIndigo),
                  ),
                  title: const Text('設為主要人物',
                      style: TextStyle(fontWeight: FontWeight.bold)),
                  subtitle: const Text('將此人物設為主要人物'),
                  onTap: () {
                    Navigator.pop(context);
                    setState(() {
                      _primaryPerson = person;
                    });
                    _selectRecentPerson(person);
                  },
                ),
                // 選擇為次要人物選項（如果需要）
                if (_selectedChartType.requiresTwoPersons)
                  ListTile(
                    leading: Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: Colors.pinkAccent.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: const Icon(Icons.person_outline,
                          color: Colors.pinkAccent),
                    ),
                    title: const Text('設為次要人物',
                        style: TextStyle(fontWeight: FontWeight.bold)),
                    subtitle: const Text('將此人物設為次要人物'),
                    onTap: () {
                      Navigator.pop(context);
                      setState(() {
                        _selectedSecondaryPerson = person;
                      });
                      _selectRecentPerson(person);
                    },
                  ),
                // 從近期記錄中移除選項
                ListTile(
                  leading: Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: AppColors.error.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: const Icon(Icons.history_toggle_off,
                        color: AppColors.error),
                  ),
                  title: const Text('從近期記錄移除',
                      style: TextStyle(fontWeight: FontWeight.bold)),
                  subtitle: const Text('從近期選中列表中移除此人物'),
                  onTap: () {
                    Navigator.pop(context);
                    _removeFromRecentPersons(person);
                  },
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  // 從近期人物記錄中移除
  void _removeFromRecentPersons(BirthData person) {
    try {
      final recentPersonsViewModel =
          Provider.of<RecentPersonsViewModel>(context, listen: false);
      recentPersonsViewModel.removePersonRecord(person);

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('已從近期記錄中移除 ${person.name}'),
          backgroundColor: AppColors.royalIndigo,
          duration: const Duration(seconds: 2),
        ),
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('移除失敗'),
          backgroundColor: AppColors.error,
          duration: Duration(seconds: 2),
        ),
      );
    }
  }

  // 構建線湊的人物卡片
  Widget _buildCompactPersonCard({
    required BirthData person,
    required bool isPrimary,
    required VoidCallback onChangePerson,
  }) {
    return Card(
      elevation: 2,
      margin: EdgeInsets.zero,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: InkWell(
        onTap: onChangePerson,
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
          child: Row(
            children: [
              // 人物標籤
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                decoration: BoxDecoration(
                  color: isPrimary
                      ? AppColors.royalIndigo.withValues(alpha: 0.1)
                      : Colors.pinkAccent.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Text(
                  isPrimary ? '主要' : '次要',
                  style: TextStyle(
                    fontSize: 10,
                    fontWeight: FontWeight.bold,
                    color:
                        isPrimary ? AppColors.royalIndigo : Colors.pinkAccent,
                  ),
                ),
              ),
              const SizedBox(width: 8),

              // 人物信息
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // 人物名稱
                    Text(
                      person.name,
                      style: const TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.bold,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                    const SizedBox(height: 2),
                    // 出生日期和地點
                    Text(
                      '${_formatDateTime(person.dateTime)} | ${person.birthPlace}',
                      style: const TextStyle(
                        fontSize: 12,
                        color: AppColors.textMedium,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ],
                ),
              ),

              // 更改按鈕
              IconButton(
                icon: const Icon(Icons.edit, size: 16),
                padding: EdgeInsets.zero,
                constraints: const BoxConstraints(),
                onPressed: onChangePerson,
              ),
            ],
          ),
        ),
      ),
    );
  }

  // 構建線湊的選擇主要人物卡片
  Widget _buildCompactSelectPrimaryPersonCard() {
    return Card(
      elevation: 2,
      margin: EdgeInsets.zero,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: InkWell(
        onTap: _changePrimaryPerson,
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
          child: Row(
            children: [
              // 人物標籤
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                decoration: BoxDecoration(
                  color: AppColors.royalIndigo.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: const Text(
                  '主要',
                  style: TextStyle(
                    fontSize: 10,
                    fontWeight: FontWeight.bold,
                    color: AppColors.royalIndigo,
                  ),
                ),
              ),
              const SizedBox(width: 8),

              // 選擇按鈕文字
              const Expanded(
                child: Text(
                  '選擇主要人物',
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                    color: AppColors.textMedium,
                  ),
                ),
              ),

              // 選擇按鈕
              const Icon(Icons.person_add,
                  size: 16, color: AppColors.royalIndigo),
            ],
          ),
        ),
      ),
    );
  }

  // 構建線湊的選擇第二個人卡片
  Widget _buildCompactSelectSecondaryPersonCard() {
    return Card(
      elevation: 2,
      margin: EdgeInsets.zero,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: InkWell(
        onTap: _selectSecondaryPerson,
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
          child: Row(
            children: [
              // 人物標籤
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                decoration: BoxDecoration(
                  color: Colors.pinkAccent.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: const Text(
                  '次要',
                  style: TextStyle(
                    fontSize: 10,
                    fontWeight: FontWeight.bold,
                    color: Colors.pinkAccent,
                  ),
                ),
              ),
              const SizedBox(width: 8),

              // 選擇按鈕文字
              const Expanded(
                child: Text(
                  '選擇第二個人',
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                    color: AppColors.textMedium,
                  ),
                ),
              ),

              // 選擇按鈕
              const Icon(Icons.person_add, size: 16, color: Colors.pinkAccent),
            ],
          ),
        ),
      ),
    );
  }

  // 構建線湊的交換人物按鈕
  Widget _buildCompactSwapPersonsButton() {
    return Card(
      elevation: 1,
      margin: EdgeInsets.zero,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: InkWell(
        onTap: _swapPersons,
        borderRadius: BorderRadius.circular(12),
        child: const Padding(
          padding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(Icons.swap_horiz, color: AppColors.royalIndigo, size: 16),
              SizedBox(width: 8),
              Text(
                '交換主次要人物',
                style: TextStyle(
                  fontSize: 12,
                  fontWeight: FontWeight.bold,
                  color: AppColors.royalIndigo,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  // 構建底部欄
  Widget _buildBottomBar() {
    return Container(
      constraints: BoxConstraints(
        maxHeight: MediaQuery.of(context).size.height * 0.8, // 限制最大高度為螢幕高度的60%
      ),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 5,
            offset: const Offset(0, -1),
          ),
        ],
      ),
      child: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 資料需求摘要卡片
            _buildDataRequirementsSummary(),

            const SizedBox(height: 12),

            if (_selectedChartType.requiresPrimaryPerson) ...[
              // 人物信息卡片區域 - 使用可滾動的容器
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 0, vertical: 0),
                constraints: BoxConstraints(
                  maxHeight: MediaQuery.of(context).size.height *
                      0.4, // 限制最大高度為螢幕高度的25%
                ),
                child: SingleChildScrollView(
                  child: _buildPersonsSection(),
                ),
              ),
              const SizedBox(height: 12),
            ],

            // 日期選擇器
            if (_selectedChartType.requiresSpecificDate) ...[
              _buildDateSelector(),
              const SizedBox(height: 12),
            ],

            // 年份選擇器（二分二至盤專用）
            if (_selectedChartType.requiresYearSelection) ...[
              _buildYearSelector(),
              const SizedBox(height: 12),
            ],

            // 季節選擇器（二分二至盤專用）
            if (_selectedChartType == ChartType.equinoxSolstice) ...[
              _buildSeasonSelector(),
              const SizedBox(height: 12),
            ],

            // 地點選擇器（天象盤和二分二至盤專用）
            if (_selectedChartType.requiresLocationSelection) ...[
              _buildLocationSelector(),
              const SizedBox(height: 12),
            ],

            // 日月蝕篩選器（日月蝕盤專用）
            if (_selectedChartType == ChartType.eclipse) ...[
              _buildEclipseFilterSelector(),
              const SizedBox(height: 12),
            ],

            // 日月蝕事件選擇器（日月蝕盤專用）
            if (_selectedChartType == ChartType.eclipse) ...[
              _buildEclipseEventSelector(),
              const SizedBox(height: 12),
            ],

            const SizedBox(height: 4),

            // 查看星盤按鈕
            _buildViewChartButton(),

            const SizedBox(height: 12),
          ],
        ),
      ),
    );
  }

  // 構建資料需求摘要卡片
  Widget _buildDataRequirementsSummary() {
    final requirements = _getDataRequirements();
    if (requirements.isEmpty) return const SizedBox.shrink();

    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: AppColors.royalIndigo.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: AppColors.royalIndigo.withValues(alpha: 0.2),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          Row(
            children: [
              const Icon(
                Icons.info_outline,
                size: 16,
                color: AppColors.royalIndigo,
              ),
              const SizedBox(width: 6),
              Text(
                '${_selectedChartType.name} 所需資料',
                style: const TextStyle(
                  fontSize: 13,
                  fontWeight: FontWeight.bold,
                  color: AppColors.royalIndigo,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Wrap(
            spacing: 6,
            runSpacing: 4,
            children: requirements
                .map((requirement) => _buildRequirementChip(requirement))
                .toList(),
          ),
        ],
      ),
    );
  }

  // 構建需求標籤
  Widget _buildRequirementChip(DataRequirement requirement) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: requirement.isCompleted
            ? Colors.green.withValues(alpha: 0.1)
            : Colors.orange.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: requirement.isCompleted
              ? Colors.green.withValues(alpha: 0.3)
              : Colors.orange.withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            requirement.isCompleted
                ? Icons.check_circle
                : Icons.radio_button_unchecked,
            size: 12,
            color: requirement.isCompleted ? Colors.green : Colors.orange,
          ),
          const SizedBox(width: 4),
          Text(
            requirement.label,
            style: TextStyle(
              fontSize: 11,
              fontWeight: FontWeight.w500,
              color: requirement.isCompleted
                  ? Colors.green.shade700
                  : Colors.orange.shade700,
            ),
          ),
        ],
      ),
    );
  }

  // 構建查看星盤按鈕
  Widget _buildViewChartButton() {
    final requirements = _getDataRequirements();
    final allCompleted = requirements.every((req) => req.isCompleted);
    final missingCount = requirements.where((req) => !req.isCompleted).length;

    return SizedBox(
      width: double.infinity,
      child: ElevatedButton(
        onPressed: allCompleted ? _validateAndViewChart : null,
        style: ElevatedButton.styleFrom(
          backgroundColor:
              allCompleted ? AppColors.royalIndigo : Colors.grey.shade300,
          foregroundColor: allCompleted ? Colors.white : Colors.grey.shade600,
          padding: const EdgeInsets.symmetric(vertical: 12),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          elevation: allCompleted ? 2 : 0,
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            if (!allCompleted) ...[
              const Icon(Icons.warning_amber_rounded, size: 16),
              const SizedBox(width: 6),
            ],
            Text(
              allCompleted
                  ? '查看${_selectedChartType.name}'
                  : '還需完成 $missingCount 項設定',
              style: TextStyle(
                fontSize: allCompleted ? 16 : 14,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
      ),
    );
  }

  // 獲取資料需求列表
  List<DataRequirement> _getDataRequirements() {
    final List<DataRequirement> requirements = [];

    // 檢查是否需要主要人物（天象盤、二分二至盤和日月蝕盤除外）
    if (_selectedChartType != ChartType.mundane &&
        _selectedChartType != ChartType.equinoxSolstice &&
        _selectedChartType != ChartType.eclipse) {
      requirements.add(DataRequirement(
        label: '主要人物',
        isCompleted: _primaryPerson != null,
        description: '選擇要分析的主要人物',
      ));
    }

    // 檢查是否需要第二個人
    if (_selectedChartType.requiresTwoPersons) {
      requirements.add(DataRequirement(
        label: '次要人物',
        isCompleted: _selectedSecondaryPerson != null,
        description: '選擇要比較的第二個人物',
      ));
    }

    // 檢查是否需要特定日期
    if (_selectedChartType.requiresSpecificDate) {
      requirements.add(const DataRequirement(
        label: '日期時間',
        isCompleted: true, // 預設已有當前時間
        description: '設定要分析的日期和時間',
      ));
    }

    // 檢查是否需要年份選擇（二分二至盤）
    if (_selectedChartType == ChartType.equinoxSolstice) {
      requirements.add(const DataRequirement(
        label: '年份',
        isCompleted: true, // 預設已有當前年份
        description: '選擇要分析的年份',
      ));

      requirements.add(DataRequirement(
        label: '季節',
        isCompleted: _selectedSeason.isNotEmpty,
        description: '選擇春分、夏至、秋分或冬至',
      ));
    }

    // 檢查是否需要日月蝕選擇（日月蝕盤）
    if (_selectedChartType == ChartType.eclipse) {
      requirements.add(const DataRequirement(
        label: '年份',
        isCompleted: true, // 預設已有當前年份
        description: '選擇要分析的年份',
      ));

      requirements.add(DataRequirement(
        label: '日月蝕事件',
        isCompleted: _availableEclipses.isEmpty
            ? true
            : _selectedEclipse != null, // 如果還沒載入事件，視為已完成以顯示按鈕
        description:
            _availableEclipses.isEmpty ? '點擊查看星盤載入事件' : '選擇要分析的日蝕或月蝕事件',
      ));

      requirements.add(DataRequirement(
        label: '觀測地點',
        isCompleted: _selectedLocation.isNotEmpty &&
            _selectedLatitude != 0.0 &&
            _selectedLongitude != 0.0,
        description: '設定觀測日月蝕的地理位置',
      ));
    }

    // 檢查是否需要地點選擇
    if (_selectedChartType.requiresLocationSelection) {
      requirements.add(DataRequirement(
        label: '地點',
        isCompleted: _selectedLocation.isNotEmpty &&
            _selectedLatitude != 0.0 &&
            _selectedLongitude != 0.0,
        description: '設定要分析的地理位置',
      ));
    }

    return requirements;
  }

  // 構建選擇器標題
  Widget _buildSelectorTitle(String title, IconData icon) {
    return Row(
      children: [
        Icon(
          icon,
          size: 16,
          color: AppColors.royalIndigo,
        ),
        const SizedBox(width: 6),
        Text(
          title,
          style: const TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.bold,
            color: AppColors.textDark,
          ),
        ),
      ],
    );
  }

  // 構建日期時間選擇器
  Widget _buildDateSelector() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: [
        _buildSelectorTitle('選擇日期時間', Icons.calendar_today),
        const SizedBox(height: 6),
        Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: AppColors.textLight),
          ),
          child: ListTile(
            dense: true,
            contentPadding:
                const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
            title: Text(
              _formatDateTime(_selectedDate),
              style: const TextStyle(fontSize: 13),
            ),
            trailing: const Icon(Icons.access_time, size: 18),
            onTap: () async {
              final selectedDateTime = await DateTimePickerBottomSheet.show(
                context: context,
                initialDateTime: _selectedDate,
                title: '選擇日期時間',
                minDate: DateTime(1800, 1, 1),
                maxDate: DateTime(2100, 12, 31),
                defaultDateTime: DateTime.now(), // 設置預設時間為當前時間
              );

              if (selectedDateTime != null && mounted) {
                setState(() {
                  _selectedDate = selectedDateTime;
                });
                // 標記需要更新星盤
                _markChartNeedsUpdate();
                // 延遲重新計算星盤
                Future.delayed(const Duration(milliseconds: 100), _recalculateChart);
              }
            },
          ),
        ),
      ],
    );
  }

  // 構建年份選擇器
  Widget _buildYearSelector() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: [
        _buildSelectorTitle('選擇年份', Icons.date_range),
        const SizedBox(height: 6),
        Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: AppColors.textLight),
          ),
          child: ListTile(
            dense: true,
            contentPadding:
                const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
            title: Text(
              '$_selectedYear年',
              style: const TextStyle(fontSize: 13),
            ),
            trailing: const Icon(Icons.date_range, size: 18),
            onTap: () => _showYearPicker(),
          ),
        ),
      ],
    );
  }

  // 構建季節選擇器
  Widget _buildSeasonSelector() {
    const seasons = [
      {'name': '春分', 'description': '春季開始，晝夜平分', 'icon': Icons.local_florist},
      {'name': '夏至', 'description': '夏季高峰，白晝最長', 'icon': Icons.wb_sunny},
      {'name': '秋分', 'description': '秋季開始，晝夜平分', 'icon': Icons.eco},
      {'name': '冬至', 'description': '冬季深度，黑夜最長', 'icon': Icons.ac_unit},
    ];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: [
        _buildSelectorTitle('選擇季節', Icons.wb_sunny),
        const SizedBox(height: 6),
        Container(
          constraints: const BoxConstraints(
            maxHeight: 200, // 限制季節選擇器的最大高度
          ),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: AppColors.textLight),
          ),
          child: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: seasons.map((season) {
                final isSelected = _selectedSeason == season['name'];
                return InkWell(
                  onTap: () {
                    setState(() {
                      _selectedSeason = season['name'] as String;
                    });
                    // 標記需要更新星盤
                    _markChartNeedsUpdate();
                    // 延遲重新計算星盤
                    Future.delayed(const Duration(milliseconds: 100), _recalculateChart);
                  },
                  child: Container(
                    padding:
                        const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                    decoration: BoxDecoration(
                      color: isSelected
                          ? AppColors.royalIndigo.withValues(alpha: 0.1)
                          : null,
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Row(
                      children: [
                        Icon(
                          season['icon'] as IconData,
                          color: isSelected
                              ? AppColors.royalIndigo
                              : AppColors.textMedium,
                          size: 18,
                        ),
                        const SizedBox(width: 10),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Text(
                                season['name'] as String,
                                style: TextStyle(
                                  fontSize: 13,
                                  fontWeight: FontWeight.bold,
                                  color: isSelected
                                      ? AppColors.royalIndigo
                                      : AppColors.textDark,
                                ),
                              ),
                              Text(
                                season['description'] as String,
                                style: TextStyle(
                                  fontSize: 11,
                                  color: isSelected
                                      ? AppColors.royalIndigo
                                      : AppColors.textMedium,
                                ),
                              ),
                            ],
                          ),
                        ),
                        if (isSelected)
                          const Icon(
                            Icons.check_circle,
                            color: AppColors.royalIndigo,
                            size: 18,
                          ),
                      ],
                    ),
                  ),
                );
              }).toList(),
            ),
          ),
        ),
      ],
    );
  }

  // 構建地點選擇器
  Widget _buildLocationSelector() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Expanded(
              child: _buildSelectorTitle('選擇地點', Icons.location_on),
            ),
            TextButton.icon(
              onPressed: _isLoadingLocation ? null : _getCurrentLocation,
              icon: _isLoadingLocation
                  ? const SizedBox(
                      width: 14,
                      height: 14,
                      child: CircularProgressIndicator(strokeWidth: 2),
                    )
                  : const Icon(Icons.my_location, size: 14),
              label: Text(
                _isLoadingLocation ? '定位中...' : '當前位置',
                style: const TextStyle(fontSize: 12),
              ),
              style: TextButton.styleFrom(
                foregroundColor: AppColors.royalIndigo,
                padding: const EdgeInsets.symmetric(horizontal: 6),
                minimumSize: Size.zero,
                tapTargetSize: MaterialTapTargetSize.shrinkWrap,
              ),
            ),
          ],
        ),
        const SizedBox(height: 6),
        TextFormField(
          controller: _locationController,
          style: const TextStyle(fontSize: 13),
          decoration: InputDecoration(
            hintText: '請輸入地點（如：台北市）',
            hintStyle: const TextStyle(fontSize: 13),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
            ),
            contentPadding:
                const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
            prefixIcon: const Icon(Icons.location_on_outlined, size: 18),
            suffixIcon: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                // 清除按鈕
                IconButton(
                  icon: const Icon(Icons.clear, size: 18),
                  onPressed: () {
                    setState(() {
                      _locationController.clear();
                      _selectedLocation = '';
                      _selectedLatitude = 0.0;
                      _selectedLongitude = 0.0;
                    });
                    // 標記需要更新星盤
                    _markChartNeedsUpdate();
                    // 延遲重新計算星盤
                    Future.delayed(const Duration(milliseconds: 100), _recalculateChart);
                  },
                  tooltip: '清除',
                  padding: EdgeInsets.zero,
                  constraints: const BoxConstraints(),
                ),
                // 搜尋按鈕
                IconButton(
                  icon: _isLoadingLocation
                      ? const SizedBox(
                          width: 18,
                          height: 18,
                          child: CircularProgressIndicator(strokeWidth: 2),
                        )
                      : const Icon(Icons.search, size: 18),
                  onPressed: _isLoadingLocation ? null : _searchLocation,
                  tooltip: '搜尋地點',
                  padding: EdgeInsets.zero,
                  constraints: const BoxConstraints(),
                ),
              ],
            ),
          ),
          onChanged: (value) {
            setState(() {
              _selectedLocation = value;
            });
            // 標記需要更新星盤
            _markChartNeedsUpdate();
            // 延遲重新計算星盤
            Future.delayed(const Duration(milliseconds: 500), _recalculateChart);
          },
        ),
        if (_selectedLatitude != 0.0 && _selectedLongitude != 0.0) ...[
          const SizedBox(height: 3),
          Text(
            '經緯度: ${_selectedLatitude.toStringAsFixed(4)}, ${_selectedLongitude.toStringAsFixed(4)}',
            style: TextStyle(
              fontSize: 11,
              color: Colors.grey[600],
            ),
          ),
        ],
      ],
    );
  }

  // 根據季節和年份計算具體日期時間（使用 EquinoxSolsticeService）
  Future<DateTime> _calculateSeasonDateTime(String season, int year) async {
    final equinoxSolsticeService = EquinoxSolsticeService();

    try {
      // 獲取該年份的所有季節時間
      final seasons = await equinoxSolsticeService.calculateSeasonTimes(
        year,
        latitude: _selectedLatitude,
        longitude: _selectedLongitude,
      );

      // 根據選擇的季節找到對應的精確時間
      for (final seasonData in seasons) {
        if (seasonData.seasonType.displayName == season) {
          return seasonData.dateTime;
        }
      }

      // 如果沒有找到，使用備用方法
      return _getApproximateSeasonDateTime(season, year);
    } catch (e) {
      // 如果精確計算失敗，使用備用方法
      return _getApproximateSeasonDateTime(season, year);
    }
  }

  // 備用的近似日期計算方法（與 EquinoxSolsticeService 保持一致）
  DateTime _getApproximateSeasonDateTime(String season, int year) {
    // 使用與 EquinoxSolsticeService._getApproximateSeasonDate 相同的邏輯
    switch (season) {
      case '春分':
        return DateTime(year, 3, 20, 12, 0); // 春分約在3月20日
      case '夏至':
        return DateTime(year, 6, 21, 12, 0); // 夏至約在6月21日
      case '秋分':
        return DateTime(year, 9, 23, 12, 0); // 秋分約在9月23日
      case '冬至':
        return DateTime(
            year, 12, 22, 12, 0); // 冬至約在12月22日（與 EquinoxSolsticeService 一致）
      default:
        return DateTime(year, 3, 20, 12, 0); // 預設春分
    }
  }

  // 獲取季節的英文名稱
  String _getSeasonEnglishName(String season) {
    switch (season) {
      case '春分':
        return 'Spring Equinox';
      case '夏至':
        return 'Summer Solstice';
      case '秋分':
        return 'Autumn Equinox';
      case '冬至':
        return 'Winter Solstice';
      default:
        return 'Spring Equinox';
    }
  }

  // 顯示年份選擇器
  Future<void> _showYearPicker() async {
    final currentYear = DateTime.now().year;
    final selectedYear = await showDialog<int>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('選擇年份'),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        content: SizedBox(
          width: double.maxFinite,
          height: 300,
          child: YearPicker(
            firstDate: DateTime(currentYear - 50),
            lastDate: DateTime(currentYear + 50),
            selectedDate: DateTime(_selectedYear),
            onChanged: (DateTime dateTime) {
              Navigator.pop(context, dateTime.year);
            },
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('取消'),
          ),
        ],
      ),
    );

    if (selectedYear != null && selectedYear != _selectedYear) {
      setState(() {
        _selectedYear = selectedYear;
      });

      // 標記需要更新星盤
      _markChartNeedsUpdate();
      // 延遲重新計算星盤
      Future.delayed(const Duration(milliseconds: 100), _recalculateChart);

      // 日月蝕盤不在年份變更時載入事件，等用戶點擊查看星盤時再載入
    }
  }

  // 獲取當前位置
  Future<void> _getCurrentLocation() async {
    setState(() {
      _isLoadingLocation = true;
    });

    try {
      // 獲取當前位置的經緯度
      final coordinates = await GeocodingService.getCurrentLocation();

      if (coordinates == null) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('無法獲取當前位置，請確保已開啟位置服務和權限')),
          );
        }
        setState(() {
          _isLoadingLocation = false;
        });
        return;
      }

      // 根據經緯度獲取地址
      final address = await GeocodingService.getAddressFromCoordinates(
        coordinates['latitude']!,
        coordinates['longitude']!,
      );

      if (address != null && mounted) {
        setState(() {
          _selectedLocation = address;
          _selectedLatitude = coordinates['latitude']!;
          _selectedLongitude = coordinates['longitude']!;
          _locationController.text = address;
          _isLoadingLocation = false;
        });

        // 標記需要更新星盤
        _markChartNeedsUpdate();
        // 延遲重新計算星盤
        Future.delayed(const Duration(milliseconds: 100), _recalculateChart);

        // 顯示成功訊息
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('已獲取當前位置: $address'),
            backgroundColor: Colors.green,
          ),
        );
      } else {
        // 如果無法獲取地址，但有經緯度，則直接使用經緯度字符串
        if (mounted) {
          final locationStr =
              '緯度: ${coordinates['latitude']!.toStringAsFixed(6)}, 經度: ${coordinates['longitude']!.toStringAsFixed(6)}';
          setState(() {
            _selectedLocation = locationStr;
            _selectedLatitude = coordinates['latitude']!;
            _selectedLongitude = coordinates['longitude']!;
            _locationController.text = locationStr;
            _isLoadingLocation = false;
          });

          // 標記需要更新星盤
          _markChartNeedsUpdate();
          // 延遲重新計算星盤
          Future.delayed(const Duration(milliseconds: 100), _recalculateChart);

          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('無法獲取地址名稱，已使用經緯度: $locationStr'),
              backgroundColor: Colors.orange,
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoadingLocation = false;
        });
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('獲取位置時出錯: $e')),
        );
      }
    }
  }

  // 搜尋地點
  Future<void> _searchLocation() async {
    if (_locationController.text.trim().isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('請輸入地點名稱')),
      );
      return;
    }

    setState(() {
      _isLoadingLocation = true;
    });

    try {
      // 使用 GeocodingService 將地點名稱轉換為經緯度
      Map<String, double> coordinates =
          await GeocodingService.getCoordinatesFromAddress(
        _locationController.text.trim(),
      );

      if (coordinates['latitude'] == null || coordinates['longitude'] == null) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('無法找到該地點，請檢查地點名稱')),
          );
        }
        setState(() {
          _isLoadingLocation = false;
        });
        return;
      }

      setState(() {
        _selectedLocation = _locationController.text.trim();
        _selectedLatitude = coordinates['latitude']!;
        _selectedLongitude = coordinates['longitude']!;
        _isLoadingLocation = false;
      });

      // 標記需要更新星盤
      _markChartNeedsUpdate();
      // 延遲重新計算星盤
      Future.delayed(const Duration(milliseconds: 100), _recalculateChart);

      // 顯示成功訊息
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('已找到地點: $_selectedLocation'),
          backgroundColor: Colors.green,
        ),
      );
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoadingLocation = false;
        });
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('搜尋地點時出錯: $e')),
        );
      }
    }
  }

  // 驗證並查看星盤
  Future<void> _validateAndViewChart() async {
    // 天象盤、二分二至盤和日月蝕盤不需要選擇人物，其他星盤需要
    if (_selectedChartType != ChartType.mundane &&
        _selectedChartType != ChartType.equinoxSolstice &&
        _selectedChartType != ChartType.eclipse &&
        _primaryPerson == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('請先選擇主要人物')),
      );
      return;
    }

    // 如果是日月蝕盤，先載入事件
    if (_selectedChartType == ChartType.eclipse) {
      if (_availableEclipses.isEmpty) {
        // 顯示載入提示
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('正在載入日月蝕事件...')),
        );

        // 載入事件
        await _loadEclipseEvents();

        // 如果載入後仍然沒有事件，顯示錯誤
        if (_availableEclipses.isEmpty) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
                content: Text(
                    '該年份沒有${_selectedEclipseFilter == '全部' ? '日月蝕' : _selectedEclipseFilter}事件')),
          );
          return;
        }

        // 顯示載入完成的結果，包含可見性統計
        final visibleCount =
            _availableEclipses.where((e) => e.isVisible).length;
        final totalCount = _availableEclipses.length;
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
                '找到 $totalCount 個${_selectedEclipseFilter == '全部' ? '日月蝕' : _selectedEclipseFilter}事件，其中 $visibleCount 個在當前地點可見'),
            duration: const Duration(seconds: 4),
          ),
        );
        return; // 讓用戶選擇事件
      }

      // 檢查是否選擇了事件
      if (_selectedEclipse == null) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('請選擇日月蝕事件')),
        );
        return;
      }

      // 檢查是否選擇了觀測地點
      if (_selectedLocation.isEmpty ||
          _selectedLatitude == 0.0 ||
          _selectedLongitude == 0.0) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('請選擇觀測地點')),
        );
        return;
      }
    }

    // 檢查是否需要第二個人但未選擇
    if (_selectedChartType.requiresTwoPersons &&
        _selectedSecondaryPerson == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('請選擇第二個人')),
      );
      return;
    }

    // 創建 ChartData 對象
    ChartData chartData;

    if (_selectedChartType == ChartType.mundane) {
      // 天象盤使用選擇的時間和地點創建虛擬人物
      chartData = ChartData(
        chartType: _selectedChartType,
        primaryPerson: BirthData(
          id: 'mundane_${_selectedDate.millisecondsSinceEpoch}',
          name: '天象盤',
          dateTime: _selectedDate,
          birthPlace: _selectedLocation,
          latitude: _selectedLatitude,
          longitude: _selectedLongitude,
        ),
        specificDate: _selectedDate,
      );
    } else if (_selectedChartType == ChartType.equinoxSolstice) {
      // 二分二至盤使用選擇的年份、季節和地點創建虛擬人物
      final seasonDateTime =
          await _calculateSeasonDateTime(_selectedSeason, _selectedYear);

      chartData = ChartData(
        chartType: _selectedChartType,
        primaryPerson: BirthData(
          id: 'equinox_solstice_${_selectedYear}_${_selectedSeason}_${_selectedLocation.hashCode}',
          name: '$_selectedYear年$_selectedSeason盤',
          dateTime: seasonDateTime,
          birthPlace: _selectedLocation,
          latitude: _selectedLatitude,
          longitude: _selectedLongitude,
        ),
        secondaryPerson: _primaryPerson, // 如果有選擇人物，用於比較
        specificDate: seasonDateTime,
      );
    } else if (_selectedChartType == ChartType.eclipse) {
      // 日月蝕盤邏輯
      chartData = ChartData(
        chartType: _selectedChartType,
        primaryPerson: BirthData(
          id: 'eclipse_${_selectedEclipse!.id}_${_selectedLocation.hashCode}',
          name: '${_selectedEclipse!.title} - $_selectedLocation',
          dateTime: _selectedEclipse!.dateTime,
          birthPlace: _selectedLocation,
          latitude: _selectedLatitude,
          longitude: _selectedLongitude,
        ),
        secondaryPerson: _primaryPerson, // 如果有選擇人物，用於比較
        specificDate: _selectedEclipse!.dateTime,
      );
    } else {
      // 其他星盤類型使用正常邏輯
      chartData = ChartData(
        chartType: _selectedChartType,
        primaryPerson: _primaryPerson!, // 已經驗證不為 null
        secondaryPerson: _selectedSecondaryPerson,
        specificDate:
            _selectedChartType.requiresSpecificDate ? _selectedDate : null,
      );
    }

    // 如果是切換星盤類型模式，返回 ChartData 而不導航到新頁面
    if (widget.isChangingChartType) {
      Navigator.pop(context, chartData);
    } else if (widget.fromAnalysisPage) {
      // 如果來自分析頁面，直接導航到 AI 解讀選擇頁面
      _navigateToAIInterpretation(chartData);
    } else {
      // 導航到星盤頁面
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => ChangeNotifierProvider(
            create: (_) =>
                ChartViewModel.withChartData(initialChartData: chartData),
            child: ChartPageNew(chartData: chartData),
          ),
        ),
      );
    }
  }

  /// 導航到 AI 解讀選擇頁面
  Future<void> _navigateToAIInterpretation(ChartData chartData) async {
    chartData = await ChartViewModel.calculateChartData(chartData);
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => AIInterpretationSelectionPage(
          chartData: chartData,
        ),
      ),
    );
  }

  // 更改主要人物
  void _changePrimaryPerson() async {
    final filesViewModel = Provider.of<FilesViewModel>(context, listen: false);
    await filesViewModel.loadBirthData();

    if (context.mounted) {
      // 排除第二個人的ID（如果有）
      final excludeIds = _selectedSecondaryPerson != null
          ? [_selectedSecondaryPerson!.id]
          : null;

      final BirthData? selectedPerson = await PersonSelectorPage.show(
        context: context,
        birthDataList: filesViewModel.birthDataList,
        title: '選擇主要人物',
        buttonColor: AppColors.royalIndigo,
        excludeIds: excludeIds,
      );

      if (selectedPerson != null && mounted) {
        setState(() {
          _primaryPerson = selectedPerson;
        });
        // 標記需要更新星盤
        _markChartNeedsUpdate();
        // 延遲重新計算星盤
        Future.delayed(const Duration(milliseconds: 100), _recalculateChart);
      }
    }
  }

  // 更改次要人物
  void _changeSecondaryPerson() async {
    final filesViewModel = Provider.of<FilesViewModel>(context, listen: false);
    await filesViewModel.loadBirthData();

    if (context.mounted) {
      // 排除主要人物的ID（如果有）
      final excludeIds = _primaryPerson != null ? [_primaryPerson!.id] : null;

      final BirthData? selectedPerson = await PersonSelectorPage.show(
        context: context,
        birthDataList: filesViewModel.birthDataList,
        title: '選擇次要人物',
        buttonColor: Colors.pinkAccent,
        excludeIds: excludeIds,
      );

      if (selectedPerson != null && mounted) {
        setState(() {
          _selectedSecondaryPerson = selectedPerson;
        });
        // 標記需要更新星盤
        _markChartNeedsUpdate();
        // 延遲重新計算星盤
        Future.delayed(const Duration(milliseconds: 100), _recalculateChart);
      }
    }
  }

  // 選擇第二個人
  void _selectSecondaryPerson() async {
    final filesViewModel = Provider.of<FilesViewModel>(context, listen: false);
    await filesViewModel.loadBirthData();

    if (context.mounted) {
      // 排除主要人物的ID（如果有）
      final excludeIds = _primaryPerson != null ? [_primaryPerson!.id] : null;

      final BirthData? selectedPerson = await PersonSelectorPage.show(
        context: context,
        birthDataList: filesViewModel.birthDataList,
        title: '選擇次要人物',
        buttonColor: Colors.pinkAccent,
        excludeIds: excludeIds,
      );

      if (selectedPerson != null && mounted) {
        setState(() {
          _selectedSecondaryPerson = selectedPerson;
        });
        // 標記需要更新星盤
        _markChartNeedsUpdate();
        // 延遲重新計算星盤
        Future.delayed(const Duration(milliseconds: 100), _recalculateChart);
      }
    }
  }

  // 交換主次要人物
  void _swapPersons() {
    if (_selectedSecondaryPerson == null || _primaryPerson == null) return;

    setState(() {
      final temp = _primaryPerson;
      _primaryPerson = _selectedSecondaryPerson!;
      _selectedSecondaryPerson = temp;
    });

    // 標記需要更新星盤
    _markChartNeedsUpdate();
    // 延遲重新計算星盤
    Future.delayed(const Duration(milliseconds: 100), _recalculateChart);
  }

  // 構建按分類顯示的星盤類型視圖
  Widget _buildCompactChartsView() {
    return Consumer<RecentChartsViewModel>(
      builder: (context, recentChartsViewModel, child) {
        return SingleChildScrollView(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 個人星盤
              _buildCategorySection(
                '個人星盤',
                [ChartType.natal, ChartType.mundane],
                AppColors.royalIndigo,
                recentChartsViewModel,
              ),

              const SizedBox(height: 16),

              // 預測類星盤
              _buildCategorySection(
                '預測類星盤',
                ChartType.values
                    .where((type) => type.isPredictiveChart)
                    .toList(),
                Colors.purple,
                recentChartsViewModel,
              ),

              const SizedBox(height: 16),

              // 返照盤類
              _buildCategorySection(
                '返照盤類',
                ChartType.values.where((type) => type.isReturnChart).toList(),
                Colors.orange,
                recentChartsViewModel,
              ),

              const SizedBox(height: 16),

              // 合盤類
              _buildCategorySection(
                '合盤類',
                ChartType.values
                    .where((type) => type.isRelationshipChart)
                    .toList(),
                Colors.pink,
                recentChartsViewModel,
              ),

              // const SizedBox(height: 16),
              //
              // // 事件占星
              // _buildCategorySection(
              //   '事件占星',
              //   ChartType.values.where((type) => type.isEventChart).toList(),
              //   Colors.green,
              //   recentChartsViewModel,
              // ),

              const SizedBox(height: 16),

              // 特殊星盤
              _buildCategorySection(
                '合盤類預測',
                [
                  ...ChartType.values
                      .where((type) => type.isRelationshipPredictiveChart),
                ],
                Colors.green,
                recentChartsViewModel,
              ),

              const SizedBox(height: 16),

              // 特殊星盤
              _buildCategorySection(
                '特殊星盤',
                [
                  ...ChartType.values.where((type) => type.isSpecialChart),
                ],
                Colors.teal,
                recentChartsViewModel,
              ),
            ],
          ),
        );
      },
    );
  }

  // 構建分類區域
  Widget _buildCategorySection(
    String categoryTitle,
    List<ChartType> chartTypes,
    Color categoryColor,
    RecentChartsViewModel recentChartsViewModel,
  ) {
    if (chartTypes.isEmpty) return const SizedBox.shrink();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 分類標題
        Padding(
          padding: const EdgeInsets.only(left: 4, bottom: 8),
          child: Text(
            categoryTitle,
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: categoryColor,
            ),
          ),
        ),

        // 星盤類型 Wrap 布局
        Wrap(
          spacing: 8,
          runSpacing: 8,
          children: chartTypes.map((chartType) {
            final isSelected = _selectedChartType == chartType;
            final isFavorite = recentChartsViewModel.favoriteCharts
                .any((record) => record.chartType == chartType);

            return _buildCompactChartTypeCard(
              chartType,
              isSelected,
              categoryColor,
              isFavorite,
              recentChartsViewModel,
            );
          }).toList(),
        ),
      ],
    );
  }

  // 構建線湊的星盤類型芯片（參考芯片設計）
  Widget _buildCompactChartTypeCard(
    ChartType chartType,
    bool isSelected,
    Color categoryColor,
    bool isFavorite,
    RecentChartsViewModel viewModel,
  ) {
    return Container(
      margin: const EdgeInsets.all(2), // 小邊距，適合網格布局
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(20), // 圓角水波紋
          onTap: () {
            setState(() {
              _selectedChartType = chartType;

              // 如果切換到不需要第二個人的星盤類型，清除選擇的第二個人
              if (!chartType.requiresTwoPersons) {
                _selectedSecondaryPerson = null;
              }

              // 如果選擇日月蝕盤，重置相關狀態並載入事件
              if (chartType == ChartType.eclipse) {
                _selectedEclipse = null;
                _availableEclipses.clear();
                _selectedEclipseFilter = '全部';
              }
            });

            // 標記需要更新星盤
            _markChartNeedsUpdate();
            // 延遲重新計算星盤
            Future.delayed(const Duration(milliseconds: 100), _recalculateChart);

            // 日月蝕盤不在選擇時載入事件，等用戶點擊查看星盤時再載入
          },
          onLongPress: () async {
            // 長按切換收藏狀態
            // 檢查是否選擇了主要人物（天象盤、二分二至盤和日月蝕盤除外）
            if (chartType != ChartType.mundane &&
                chartType != ChartType.equinoxSolstice &&
                chartType != ChartType.eclipse &&
                _primaryPerson == null) {
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('請先選擇主要人物')),
              );
              return;
            }

            // 檢查日月蝕盤是否選擇了事件
            if (chartType == ChartType.eclipse && _selectedEclipse == null) {
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('請選擇日月蝕事件')),
              );
              return;
            }

            // 先創建 ChartData 對象
            ChartData chartData;

            if (chartType == ChartType.mundane) {
              // 天象盤邏輯
              chartData = ChartData(
                chartType: chartType,
                primaryPerson: BirthData(
                  id: 'mundane_${_selectedDate.millisecondsSinceEpoch}',
                  name: '天象盤',
                  dateTime: _selectedDate,
                  birthPlace: _selectedLocation,
                  latitude: _selectedLatitude,
                  longitude: _selectedLongitude,
                ),
                specificDate: _selectedDate,
              );
            } else if (chartType == ChartType.equinoxSolstice) {
              // 二分二至盤邏輯
              final seasonDateTime = await _calculateSeasonDateTime(
                  _selectedSeason, _selectedYear);
              chartData = ChartData(
                chartType: chartType,
                primaryPerson: BirthData(
                  id: 'equinox_solstice_${_selectedYear}_${_selectedSeason}_${_selectedLocation.hashCode}',
                  name: '$_selectedYear年$_selectedSeason盤',
                  dateTime: seasonDateTime,
                  birthPlace: _selectedLocation,
                  latitude: _selectedLatitude,
                  longitude: _selectedLongitude,
                ),
                secondaryPerson: _primaryPerson,
                specificDate: seasonDateTime,
              );
            } else if (chartType == ChartType.eclipse) {
              // 日月蝕盤邏輯
              chartData = ChartData(
                chartType: chartType,
                primaryPerson: BirthData(
                  id: 'eclipse_${_selectedEclipse!.id}_${_selectedLocation.hashCode}',
                  name: '${_selectedEclipse!.title} - $_selectedLocation',
                  dateTime: _selectedEclipse!.dateTime,
                  birthPlace: _selectedLocation,
                  latitude: _selectedLatitude,
                  longitude: _selectedLongitude,
                ),
                secondaryPerson: _primaryPerson,
                specificDate: _selectedEclipse!.dateTime,
              );
            } else {
              // 其他星盤類型
              chartData = ChartData(
                chartType: chartType,
                primaryPerson: _primaryPerson!,
                secondaryPerson: _selectedSecondaryPerson,
                specificDate: _selectedChartType.requiresSpecificDate
                    ? _selectedDate
                    : null,
              );
            }

            // 將星盤添加到最近使用的記錄中
            viewModel.addOrUpdateRecentChart(chartData);

            // 顯示提示
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(content: Text('已添加${chartType.name}到最近使用的記錄中')),
            );
          },
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 6),
            // 芯片內邊距
            decoration: BoxDecoration(
              color: Colors.transparent, // 移除背景顏色
              borderRadius: BorderRadius.circular(20), // 圓角設計
              border: Border.all(
                color: isSelected
                    ? categoryColor
                    : categoryColor.withValues(alpha: 0.3),
                width: isSelected ? 2 : 1,
              ),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                // 圓形圖示容器
                Container(
                  width: 20,
                  height: 20,
                  decoration: BoxDecoration(
                    color: categoryColor.withValues(alpha: 0.2),
                    shape: BoxShape.circle,
                  ),
                  child: Icon(
                    _chartTypeIcons[chartType] ?? Icons.star,
                    color: categoryColor,
                    size: 12, // 圖示大小
                  ),
                ),
                const SizedBox(width: 4), // 圖示與文字間距
                // 星盤名稱與徽章
                Row(
                  mainAxisSize: MainAxisSize.min,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    // 星盤名稱
                    Flexible(
                      child: Text(
                        chartType.name,
                        textAlign: TextAlign.center,
                        style: TextStyle(
                          fontSize: 12, // 縮小字體
                          fontWeight:
                              isSelected ? FontWeight.bold : FontWeight.w500,
                          color:
                              isSelected ? categoryColor : AppColors.textDark,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                    // 收藏徽章（內聯顯示）
                    if (isFavorite) ...[
                      const SizedBox(width: 2),
                      Icon(
                        Icons.star,
                        color: Colors.amber,
                        size: 10, // 徽章圖示大小
                      ),
                    ],
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  // 格式化日期時間
  String _formatDateTime(DateTime dateTime) {
    return '${dateTime.year}-${dateTime.month.toString().padLeft(2, '0')}-${dateTime.day.toString().padLeft(2, '0')} '
        '${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}';
  }

  // 構建日月蝕篩選器
  Widget _buildEclipseFilterSelector() {
    const filters = ['全部', '日蝕', '月蝕'];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: [
        _buildSelectorTitle('篩選類型', Icons.filter_list),
        const SizedBox(height: 6),
        Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: AppColors.textLight),
          ),
          child: Row(
            children: filters.map((filter) {
              final isSelected = _selectedEclipseFilter == filter;
              return Expanded(
                child: InkWell(
                  onTap: () {
                    setState(() {
                      _selectedEclipseFilter = filter;
                      _selectedEclipse = null; // 重置選擇的日月蝕事件
                      _availableEclipses.clear(); // 清空現有事件列表
                    });
                    // 標記需要更新星盤
                    _markChartNeedsUpdate();
                    // 不在篩選器變更時載入事件，等用戶點擊查看星盤時再載入
                  },
                  child: Container(
                    padding: const EdgeInsets.symmetric(vertical: 8),
                    decoration: BoxDecoration(
                      color: isSelected
                          ? AppColors.royalIndigo.withValues(alpha: 0.1)
                          : null,
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      filter,
                      textAlign: TextAlign.center,
                      style: TextStyle(
                        fontSize: 13,
                        fontWeight: FontWeight.bold,
                        color: isSelected
                            ? AppColors.royalIndigo
                            : AppColors.textMedium,
                      ),
                    ),
                  ),
                ),
              );
            }).toList(),
          ),
        ),
      ],
    );
  }

  // 構建日月蝕事件選擇器
  Widget _buildEclipseEventSelector() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            _buildSelectorTitle('選擇日月蝕事件', Icons.brightness_2),
            if (_isLoadingEclipses)
              const SizedBox(
                width: 16,
                height: 16,
                child: CircularProgressIndicator(strokeWidth: 2),
              )
            else if (_availableEclipses.isNotEmpty)
              _buildEclipseStatistics(),
          ],
        ),
        const SizedBox(height: 6),
        Center(
          child: Container(
            width: double.infinity,
            constraints: const BoxConstraints(
              maxHeight: 200, // 限制事件選擇器的最大高度
            ),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: AppColors.textLight),
            ),
            child: _availableEclipses.isEmpty
                ? Container(
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Text(
                          _isLoadingEclipses
                              ? '載入中...'
                              : _availableEclipses.isEmpty
                                  ? '點擊「查看星盤」載入${_selectedEclipseFilter == '全部' ? '日月蝕' : _selectedEclipseFilter}事件'
                                  : '該年份沒有${_selectedEclipseFilter == '全部' ? '日月蝕' : _selectedEclipseFilter}事件',
                          textAlign: TextAlign.center,
                          style: const TextStyle(
                            fontSize: 13,
                            color: AppColors.textMedium,
                          ),
                        ),
                        if (!_isLoadingEclipses && _availableEclipses.isEmpty)
                          const SizedBox(height: 8),
                        if (!_isLoadingEclipses && _availableEclipses.isEmpty)
                          Text(
                            '可見性將根據選擇的觀測地點計算',
                            textAlign: TextAlign.center,
                            style: TextStyle(
                              fontSize: 11,
                              color:
                                  AppColors.textMedium.withValues(alpha: 0.7),
                              fontStyle: FontStyle.italic,
                            ),
                          ),
                      ],
                    ),
                  )
                : SingleChildScrollView(
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: _availableEclipses.map((eclipse) {
                        final isSelected = _selectedEclipse?.id == eclipse.id;
                        return InkWell(
                          onTap: () {
                            setState(() {
                              _selectedEclipse = eclipse;
                            });
                            // 標記需要更新星盤
                            _markChartNeedsUpdate();
                            // 延遲重新計算星盤
                            Future.delayed(const Duration(milliseconds: 100), _recalculateChart);
                          },
                          child: Container(
                            padding: const EdgeInsets.symmetric(
                                horizontal: 12, vertical: 8),
                            decoration: BoxDecoration(
                              color: isSelected
                                  ? AppColors.royalIndigo.withValues(alpha: 0.1)
                                  : null,
                              borderRadius: BorderRadius.circular(12),
                            ),
                            child: Row(
                              children: [
                                // 日月蝕類型圖標
                                Icon(
                                  _getEclipseIcon(eclipse),
                                  color: isSelected
                                      ? AppColors.royalIndigo
                                      : AppColors.textMedium,
                                  size: 18,
                                ),
                                const SizedBox(width: 10),
                                Expanded(
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    mainAxisSize: MainAxisSize.min,
                                    children: [
                                      // 事件標題
                                      Text(
                                        eclipse.title,
                                        style: TextStyle(
                                          fontSize: 13,
                                          fontWeight: FontWeight.bold,
                                          color: isSelected
                                              ? AppColors.royalIndigo
                                              : AppColors.textDark,
                                        ),
                                      ),
                                      // 日期時間
                                      Text(
                                        _formatDateTime(eclipse.dateTime),
                                        style: TextStyle(
                                          fontSize: 11,
                                          color: isSelected
                                              ? AppColors.royalIndigo
                                              : AppColors.textMedium,
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                                // 可見性指示器
                                _buildVisibilityIndicator(eclipse, isSelected),
                                const SizedBox(width: 8),
                                if (isSelected)
                                  const Icon(
                                    Icons.check_circle,
                                    color: AppColors.royalIndigo,
                                    size: 18,
                                  ),
                              ],
                            ),
                          ),
                        );
                      }).toList(),
                    ),
                  ),
          ),
        ),
      ],
    );
  }

  // 獲取日月蝕圖標
  IconData _getEclipseIcon(AstroEvent eclipse) {
    if (eclipse.title.contains('日')) {
      return Icons.wb_sunny; // 日蝕使用太陽圖標
    } else if (eclipse.title.contains('月')) {
      return Icons.nightlight_round; // 月蝕使用月亮圖標
    } else {
      return Icons.brightness_2; // 預設圖標
    }
  }

  // 構建日月蝕統計信息
  Widget _buildEclipseStatistics() {
    final visibleCount = _availableEclipses.where((e) => e.isVisible).length;
    final totalCount = _availableEclipses.length;

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: AppColors.royalIndigo.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: AppColors.royalIndigo.withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          const Icon(
            Icons.visibility,
            size: 14,
            color: AppColors.royalIndigo,
          ),
          const SizedBox(width: 4),
          Text(
            '$visibleCount/$totalCount 可見',
            style: const TextStyle(
              fontSize: 11,
              fontWeight: FontWeight.w600,
              color: AppColors.royalIndigo,
            ),
          ),
        ],
      ),
    );
  }

  // 構建可見性指示器
  Widget _buildVisibilityIndicator(AstroEvent eclipse, bool isSelected) {
    final isVisible = eclipse.isVisible;
    final color = isSelected ? AppColors.royalIndigo : AppColors.textMedium;

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
      decoration: BoxDecoration(
        color: isVisible
            ? (isSelected
                ? Colors.green.withValues(alpha: 0.2)
                : Colors.green.withValues(alpha: 0.1))
            : (isSelected
                ? Colors.orange.withValues(alpha: 0.2)
                : Colors.orange.withValues(alpha: 0.1)),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: isVisible
              ? (isSelected
                  ? Colors.green
                  : Colors.green.withValues(alpha: 0.5))
              : (isSelected
                  ? Colors.orange
                  : Colors.orange.withValues(alpha: 0.5)),
          width: 1,
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            isVisible ? Icons.visibility : Icons.visibility_off,
            size: 12,
            color: isVisible
                ? (isSelected ? Colors.green.shade700 : Colors.green.shade600)
                : (isSelected
                    ? Colors.orange.shade700
                    : Colors.orange.shade600),
          ),
          const SizedBox(width: 4),
          Text(
            isVisible ? '可見' : '不可見',
            style: TextStyle(
              fontSize: 10,
              fontWeight: FontWeight.w600,
              color: isVisible
                  ? (isSelected ? Colors.green.shade700 : Colors.green.shade600)
                  : (isSelected
                      ? Colors.orange.shade700
                      : Colors.orange.shade600),
            ),
          ),
        ],
      ),
    );
  }

  // 載入日月蝕事件
  Future<void> _loadEclipseEvents() async {
    setState(() {
      _isLoadingEclipses = true;
      _availableEclipses.clear();
    });

    try {
      final astroCalendarService = AstroCalendarService();
      final startDate = DateTime(_selectedYear, 1, 1);
      final endDate = DateTime(_selectedYear, 12, 31);

      // 獲取該年份的所有天象事件
      final events = <AstroEvent>[];

      // 逐月獲取事件以避免性能問題
      final monthlyEvents = await astroCalendarService.getYearEclipseEvents(
        _selectedYear,
      );
      events.addAll(monthlyEvents);

      // 篩選日月蝕事件
      final eclipseEvents = events.where((event) {
        if (event.type != AstroEventType.eclipse) return false;

        // 根據篩選器進一步篩選
        if (_selectedEclipseFilter == '全部') {
          return true;
        } else if (_selectedEclipseFilter == '日蝕') {
          // 檢查是否為日蝕（通過標題或其他屬性判斷）
          return event.title.contains('日') && !event.title.contains('月');
        } else if (_selectedEclipseFilter == '月蝕') {
          // 檢查是否為月蝕
          return event.title.contains('月') && !event.title.contains('日');
        }
        return false;
      }).toList();

      // 按可見性和日期排序（可見的事件排在前面）
      eclipseEvents.sort((a, b) {
        // 首先按可見性排序（可見的在前）
        if (a.isVisible != b.isVisible) {
          return b.isVisible ? 1 : -1;
        }
        // 然後按日期排序
        return a.dateTime.compareTo(b.dateTime);
      });

      setState(() {
        _availableEclipses = eclipseEvents;
        _isLoadingEclipses = false;
      });
    } catch (e) {
      setState(() {
        _isLoadingEclipses = false;
      });
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('載入日月蝕事件時出錯: $e')),
        );
      }
    }
  }
}

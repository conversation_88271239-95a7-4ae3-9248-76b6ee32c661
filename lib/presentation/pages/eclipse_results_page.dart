import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../../astreal.dart';
import '../../data/services/api/astrology_service.dart';
import 'chart_page_new.dart';

class EclipseResultsPage extends StatefulWidget {
  final List<AstroEvent> eclipseResults;
  final int selectedYear;
  final String selectedLocation;
  final double selectedLatitude;
  final double selectedLongitude;
  final String selectedFilter;
  final BirthData? natalPerson;

  const EclipseResultsPage({
    super.key,
    required this.eclipseResults,
    required this.selectedYear,
    required this.selectedLocation,
    required this.selectedLatitude,
    required this.selectedLongitude,
    required this.selectedFilter,
    this.natalPerson,
  });

  @override
  State<EclipseResultsPage> createState() => _EclipseResultsPageState();
}

class _EclipseResultsPageState extends State<EclipseResultsPage> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('${widget.selectedYear}年 日月蝕結果'),
        backgroundColor: AppColors.indigoSurface,
        foregroundColor: Colors.white,
        actions: [
          // 篩選信息顯示
          Padding(
            padding: const EdgeInsets.only(right: 16),
            child: Center(
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                decoration: BoxDecoration(
                  color: Colors.white.withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(16),
                ),
                child: Text(
                  widget.selectedFilter,
                  style: const TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
      body: SafeArea(
        child: Column(
          children: [
            // 查詢信息卡片
            _buildQueryInfoCard(),
            
            // 結果列表
            Expanded(
              child: _buildResultsList(),
            ),
          ],
        ),
      ),
    );
  }

  /// 構建查詢信息卡片
  Widget _buildQueryInfoCard() {
    return Container(
      margin: const EdgeInsets.all(16),
      child: StyledCard(
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Row(
                children: [
                  Icon(Icons.info_outline, color: AppColors.indigoSurface),
                  SizedBox(width: 8),
                  Text(
                    '查詢條件',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 12),
              _buildInfoRow('年份', '${widget.selectedYear}年'),
              _buildInfoRow('地點', widget.selectedLocation),
              _buildInfoRow('座標', 
                '${widget.selectedLatitude.toStringAsFixed(4)}°, ${widget.selectedLongitude.toStringAsFixed(4)}°'),
              _buildInfoRow('類型', widget.selectedFilter),
              _buildInfoRow('結果', '${widget.eclipseResults.length} 個事件'),
            ],
          ),
        ),
      ),
    );
  }

  /// 構建信息行
  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 2),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 60,
            child: Text(
              '$label：',
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey.shade600,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: const TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w400,
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 構建結果列表
  Widget _buildResultsList() {
    if (widget.eclipseResults.isEmpty) {
      return _buildEmptyResults();
    }

    return ListView.builder(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      itemCount: widget.eclipseResults.length,
      itemBuilder: (context, index) {
        final eclipse = widget.eclipseResults[index];
        return _buildEclipseCard(eclipse, index);
      },
    );
  }

  /// 構建空結果提示
  Widget _buildEmptyResults() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.search_off,
            size: 64,
            color: Colors.grey.shade400,
          ),
          const SizedBox(height: 16),
          Text(
            '未找到符合條件的日月蝕事件',
            style: TextStyle(
              fontSize: 16,
              color: Colors.grey.shade600,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            '請嘗試調整年份或篩選條件',
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey.shade500,
            ),
          ),
        ],
      ),
    );
  }

  /// 構建日月蝕事件卡片
  Widget _buildEclipseCard(AstroEvent eclipse, int index) {
    final isLunar = eclipse.title.contains('月蝕') || eclipse.title.contains('Lunar');
    final isSolar = eclipse.title.contains('日蝕') || eclipse.title.contains('Solar');
    
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: InkWell(
        onTap: () => _navigateToEclipseChart(eclipse),
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  // 序號標籤
                  Container(
                    width: 32,
                    height: 32,
                    decoration: BoxDecoration(
                      color: AppColors.indigoSurface.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(16),
                    ),
                    child: Center(
                      child: Text(
                        '${index + 1}',
                        style: const TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.bold,
                          color: AppColors.indigoSurface,
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(width: 12),
                  
                  // 蝕相類型圖標
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: isLunar 
                          ? Colors.indigo.withValues(alpha: 0.1)
                          : Colors.orange.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Icon(
                      isLunar ? Icons.brightness_2 : Icons.wb_sunny,
                      color: isLunar ? Colors.indigo : Colors.orange,
                      size: 20,
                    ),
                  ),
                  const SizedBox(width: 12),
                  
                  // 蝕相信息
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          eclipse.title,
                          style: const TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          _formatDateTime(eclipse.dateTime),
                          style: TextStyle(
                            fontSize: 14,
                            color: Colors.grey.shade600,
                          ),
                        ),
                      ],
                    ),
                  ),
                  
                  // 箭頭圖標
                  Icon(
                    Icons.arrow_forward_ios,
                    size: 16,
                    color: Colors.grey.shade400,
                  ),
                ],
              ),
              
              if (eclipse.description.isNotEmpty) ...[
                const SizedBox(height: 12),
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Colors.grey.shade50,
                    borderRadius: BorderRadius.circular(6),
                  ),
                  child: Text(
                    eclipse.description,
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.grey.shade700,
                    ),
                  ),
                ),
              ],
              
              // 操作提示
              const SizedBox(height: 8),
              Row(
                children: [
                  Icon(
                    Icons.touch_app,
                    size: 14,
                    color: Colors.grey.shade500,
                  ),
                  const SizedBox(width: 4),
                  Text(
                    '點擊查看星盤',
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.grey.shade500,
                      fontStyle: FontStyle.italic,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// 導航到日月蝕盤頁面
  void _navigateToEclipseChart(AstroEvent eclipse) async {
    try {
      // 顯示載入指示器
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => const Center(
          child: CircularProgressIndicator(),
        ),
      );

      // 創建日月蝕盤的星盤數據
      final chartData = await _createEclipseChartData(eclipse);

      // 關閉載入指示器
      if (mounted) {
        Navigator.of(context).pop();
      }

      if (chartData != null && mounted) {
        // 直接導航到星盤頁面
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => ChangeNotifierProvider(
              create: (_) => ChartViewModel.withChartData(
                initialChartData: chartData,
              ),
              child: ChartPageNew(
                chartData: chartData,
                chartType: ChartType.eclipse,
              ),
            ),
          ),
        );
      } else {
        // 顯示錯誤信息
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('無法創建日月蝕盤，請稍後再試'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (e) {
      // 關閉載入指示器
      if (mounted) {
        Navigator.of(context).pop();
      }

      // 顯示錯誤信息
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('創建日月蝕盤失敗: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  /// 創建日月蝕盤的星盤數據
  Future<ChartData?> _createEclipseChartData(AstroEvent eclipse) async {
    try {
      final astrologyService = AstrologyService();

      // 創建日月蝕盤的基本數據結構
      final eclipseChartData = ChartData(
        chartType: ChartType.eclipse,
        primaryPerson: BirthData(
          id: 'eclipse_${eclipse.id}',
          name: eclipse.title,
          dateTime: eclipse.dateTime,
          latitude: widget.selectedLatitude,
          longitude: widget.selectedLongitude,
          birthPlace: widget.selectedLocation,
        ),
        secondaryPerson: widget.natalPerson, // 可選的本命盤人物
        specificDate: eclipse.dateTime,
      );

      // 使用 AstrologyService 計算星盤數據
      final calculatedChartData = await astrologyService.calculateChartData(
        eclipseChartData,
        latitude: widget.selectedLatitude,
        longitude: widget.selectedLongitude,
      );

      return calculatedChartData;
    } catch (e) {
      print('創建日月蝕盤數據失敗: $e');
      return null;
    }
  }

  /// 格式化日期時間
  String _formatDateTime(DateTime dateTime) {
    return '${dateTime.year}年${dateTime.month}月${dateTime.day}日 '
           '${dateTime.hour.toString().padLeft(2, '0')}:'
           '${dateTime.minute.toString().padLeft(2, '0')}';
  }
}


import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../../astreal.dart';
import '../../data/services/api/financial_analysis_service.dart';
import 'chart_page_new.dart';

/// 個人財務星盤分析頁面
class FinancialAnalysisPage extends StatefulWidget {
  final BirthData person;

  const FinancialAnalysisPage({
    super.key,
    required this.person,
  });

  @override
  State<FinancialAnalysisPage> createState() => _FinancialAnalysisPageState();
}

class _FinancialAnalysisPageState extends State<FinancialAnalysisPage> {
  final FinancialAnalysisService _analysisService = FinancialAnalysisService();
  FinancialAnalysis? _analysis;
  bool _isLoading = true;
  String? _errorMessage;

  @override
  void initState() {
    super.initState();
    _loadAnalysis();
  }

  Future<void> _loadAnalysis() async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      final analysis = await _analysisService.analyzeFinancialProfile(widget.person);
      setState(() {
        _analysis = analysis;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _errorMessage = '分析失敗：$e';
        _isLoading = false;
      });
    }
  }

  /// 查看星盤功能
  void _viewChart() {
    // 創建 ChartData 對象，專注於財務相關的星盤
    final chartData = ChartData(
      chartType: ChartType.natal,
      primaryPerson: widget.person,
    );

    // 導航到星盤頁面
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => ChangeNotifierProvider(
          create: (_) => ChartViewModel.withChartData(
            initialChartData: chartData,
            context: context,
          ),
          child: ChartPageNew(chartData: chartData),
        ),
      ),
    );
  }

  /// AI深度分析功能
  void _showAIAnalysis() {
    if (_analysis == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('請先完成基礎分析'),
          backgroundColor: AppColors.error,
        ),
      );
      return;
    }

    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => _buildAIAnalysisBottomSheet(),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('${widget.person.name} - 財務星盤'),
        actions: [
          // 查看星盤按鈕
          IconButton(
            icon: const Icon(Icons.auto_awesome, color: Colors.white),
            onPressed: _viewChart,
            tooltip: '查看星盤',
          ),
          // AI分析按鈕
          IconButton(
            icon: const Icon(Icons.psychology, color: Colors.white),
            onPressed: _showAIAnalysis,
            tooltip: 'AI深度分析',
          ),
          // 重新分析按鈕
          IconButton(
            icon: const Icon(Icons.refresh, color: Colors.white),
            onPressed: _loadAnalysis,
            tooltip: '重新分析',
          ),
        ],
      ),
      body: _buildBody(),
    );
  }

  Widget _buildBody() {
    if (_isLoading) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(),
            SizedBox(height: 16),
            Text('正在分析財務星盤...'),
          ],
        ),
      );
    }

    if (_errorMessage != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64,
              color: Colors.grey[400],
            ),
            const SizedBox(height: 16),
            Text(
              _errorMessage!,
              style: TextStyle(color: Colors.grey[600]),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: _loadAnalysis,
              child: const Text('重試'),
            ),
          ],
        ),
      );
    }

    if (_analysis == null) {
      return const Center(child: Text('暫無分析數據'));
    }

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 整體摘要
          _buildOverallSummary(),
          const SizedBox(height: 16),

          // 財務風格分析
          _buildFinancialStyle(),
          const SizedBox(height: 16),

          // 宮位分析
          _buildHouseAnalysis(),
          const SizedBox(height: 16),

          // 行星影響
          _buildPlanetInfluences(),
          const SizedBox(height: 16),

          // 優勢與挑戰
          _buildStrengthsAndChallenges(),
        ],
      ),
    );
  }

  /// 構建整體摘要
  Widget _buildOverallSummary() {
    return StyledCard(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.summarize,
                  color: AppColors.royalIndigo,
                  size: 20,
                ),
                const SizedBox(width: 8),
                const Text(
                  '整體財務分析',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: AppColors.royalIndigo,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Text(
              _analysis!.overallSummary,
              style: const TextStyle(fontSize: 14, height: 1.5),
            ),
          ],
        ),
      ),
    );
  }

  /// 構建財務風格分析
  Widget _buildFinancialStyle() {
    final style = _analysis!.financialStyle;
    
    return StyledCard(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.psychology,
                  color: AppColors.solarAmber,
                  size: 20,
                ),
                const SizedBox(width: 8),
                const Text(
                  '財務風格分析',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: AppColors.royalIndigo,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            
            _buildStyleItem('風險承受度', _getRiskToleranceText(style.riskTolerance)),
            _buildStyleItem('收入方式', style.incomeStyle),
            _buildStyleItem('金錢吸引力', style.moneyAttraction),
            _buildStyleItem('投資風格', style.investmentStyle),
            _buildStyleItem('穩定性水平', style.stabilityLevel),
            
            const SizedBox(height: 16),
            const Text(
              '宮位影響力',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: AppColors.royalIndigo,
              ),
            ),
            const SizedBox(height: 8),
            
            ...style.houseInfluences.entries.map((entry) =>
              _buildInfluenceBar(entry.key, entry.value)
            ),
          ],
        ),
      ),
    );
  }

  /// 構建風格項目
  Widget _buildStyleItem(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 80,
            child: Text(
              label,
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey[600],
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              value,
              style: const TextStyle(fontSize: 14),
            ),
          ),
        ],
      ),
    );
  }

  /// 構建影響力進度條
  Widget _buildInfluenceBar(String house, double influence) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                house,
                style: const TextStyle(fontSize: 12),
              ),
              Text(
                '${influence.toInt()}%',
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.grey[600],
                ),
              ),
            ],
          ),
          const SizedBox(height: 4),
          LinearProgressIndicator(
            value: influence / 100,
            backgroundColor: Colors.grey[200],
            valueColor: AlwaysStoppedAnimation<Color>(AppColors.solarAmber),
          ),
        ],
      ),
    );
  }

  /// 構建宮位分析
  Widget _buildHouseAnalysis() {
    return StyledCard(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.home_work,
                  color: AppColors.indigoLight,
                  size: 20,
                ),
                const SizedBox(width: 8),
                const Text(
                  '財務宮位分析',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: AppColors.royalIndigo,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            
            ..._analysis!.houseAnalyses.map((house) => _buildHouseCard(house)),
          ],
        ),
      ),
    );
  }

  /// 構建宮位卡片
  Widget _buildHouseCard(HouseFinancialAnalysis house) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey[300]!),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: AppColors.indigoLight.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(4),
                ),
                child: Text(
                  house.houseName,
                  style: const TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.bold,
                    color: AppColors.indigoLight,
                  ),
                ),
              ),
              const Spacer(),
              Text(
                '影響力: ${house.influence.toInt()}%',
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.grey[600],
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            house.description,
            style: TextStyle(
              fontSize: 12,
              color: Colors.grey[600],
            ),
          ),
          if (house.planets.isNotEmpty) ...[
            const SizedBox(height: 8),
            Wrap(
              spacing: 4,
              children: house.planets.map((planet) => Chip(
                label: Text(
                  planet,
                  style: const TextStyle(fontSize: 10),
                ),
                backgroundColor: AppColors.solarAmber.withValues(alpha: 0.1),
                materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
              )).toList(),
            ),
          ],
          const SizedBox(height: 8),
          Text(
            house.interpretation,
            style: const TextStyle(fontSize: 13),
          ),
        ],
      ),
    );
  }

  /// 構建行星影響
  Widget _buildPlanetInfluences() {
    return StyledCard(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.public,
                  color: AppColors.solarAmber,
                  size: 20,
                ),
                const SizedBox(width: 8),
                const Text(
                  '行星財務影響',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: AppColors.royalIndigo,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            
            ..._analysis!.planetInfluences.map((planet) => _buildPlanetCard(planet)),
          ],
        ),
      ),
    );
  }

  /// 構建行星卡片
  Widget _buildPlanetCard(PlanetFinancialInfluence planet) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey[300]!),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Text(
                planet.planetName,
                style: const TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                  color: AppColors.royalIndigo,
                ),
              ),
              const SizedBox(width: 8),
              Text(
                '${planet.sign} 第${planet.house}宮',
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.grey[600],
                ),
              ),
              const Spacer(),
              Text(
                '強度: ${planet.strength.toInt()}%',
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.grey[600],
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            planet.influence,
            style: const TextStyle(fontSize: 13),
          ),
          if (planet.aspects.isNotEmpty) ...[
            const SizedBox(height: 8),
            Wrap(
              spacing: 4,
              children: planet.aspects.map((aspect) => Chip(
                label: Text(
                  aspect,
                  style: const TextStyle(fontSize: 10),
                ),
                backgroundColor: AppColors.indigoLight.withValues(alpha: 0.1),
                materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
              )).toList(),
            ),
          ],
        ],
      ),
    );
  }

  /// 構建優勢與挑戰
  Widget _buildStrengthsAndChallenges() {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Expanded(
          child: StyledCard(
            elevation: 2,
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(
                        Icons.trending_up,
                        color: Colors.green,
                        size: 20,
                      ),
                      const SizedBox(width: 8),
                      const Text(
                        '財務優勢',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: AppColors.royalIndigo,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 12),
                  ..._analysis!.keyStrengths.map((strength) => Padding(
                    padding: const EdgeInsets.only(bottom: 8),
                    child: Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Icon(
                          Icons.check_circle,
                          color: Colors.green,
                          size: 16,
                        ),
                        const SizedBox(width: 8),
                        Expanded(
                          child: Text(
                            strength,
                            style: const TextStyle(fontSize: 12),
                          ),
                        ),
                      ],
                    ),
                  )),
                ],
              ),
            ),
          ),
        ),
        const SizedBox(width: 8),
        Expanded(
          child: StyledCard(
            elevation: 2,
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(
                        Icons.warning,
                        color: Colors.orange,
                        size: 20,
                      ),
                      const SizedBox(width: 8),
                      const Text(
                        '注意事項',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: AppColors.royalIndigo,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 12),
                  ..._analysis!.potentialChallenges.map((challenge) => Padding(
                    padding: const EdgeInsets.only(bottom: 8),
                    child: Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Icon(
                          Icons.info,
                          color: Colors.orange,
                          size: 16,
                        ),
                        const SizedBox(width: 8),
                        Expanded(
                          child: Text(
                            challenge,
                            style: const TextStyle(fontSize: 12),
                          ),
                        ),
                      ],
                    ),
                  )),
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }

  String _getRiskToleranceText(RiskTolerance tolerance) {
    switch (tolerance) {
      case RiskTolerance.conservative:
        return '保守型';
      case RiskTolerance.moderate:
        return '穩健型';
      case RiskTolerance.aggressive:
        return '積極型';
    }
  }

  /// 構建 AI 分析底部彈出組件
  Widget _buildAIAnalysisBottomSheet() {
    return Container(
      height: MediaQuery.of(context).size.height * 0.8,
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(20),
          topRight: Radius.circular(20),
        ),
      ),
      child: Column(
        children: [
          // 頂部拖拽指示器和標題
          _buildAIAnalysisHeader(),

          // AI 分析內容
          Expanded(
            child: _buildAIAnalysisContent(),
          ),
        ],
      ),
    );
  }

  /// 構建 AI 分析標題區域
  Widget _buildAIAnalysisHeader() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(20),
          topRight: Radius.circular(20),
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 5,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          // 拖拽指示器
          Container(
            width: 40,
            height: 4,
            decoration: BoxDecoration(
              color: Colors.grey.shade300,
              borderRadius: BorderRadius.circular(2),
            ),
          ),
          const SizedBox(height: 16),
          // 標題和關閉按鈕
          Row(
            children: [
              const Icon(
                Icons.psychology,
                color: AppColors.royalIndigo,
                size: 24,
              ),
              const SizedBox(width: 8),
              const Expanded(
                child: Text(
                  '深度財務分析',
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: AppColors.textDark,
                  ),
                ),
              ),
              IconButton(
                onPressed: () => Navigator.of(context).pop(),
                icon: const Icon(Icons.close),
                style: IconButton.styleFrom(
                  foregroundColor: AppColors.textMedium,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// 構建 AI 分析內容
  Widget _buildAIAnalysisContent() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // AI 分析說明
          _buildAIAnalysisIntro(),
          const SizedBox(height: 20),

          // 個性化投資建議
          _buildPersonalizedAdvice(),
          const SizedBox(height: 20),

          // 風險評估
          _buildRiskAssessment(),
          const SizedBox(height: 20),

          // 時機分析
          _buildTimingAnalysis(),
          const SizedBox(height: 20),

          // 長期規劃建議
          _buildLongTermPlanning(),
        ],
      ),
    );
  }

  /// 構建 AI 分析介紹
  Widget _buildAIAnalysisIntro() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            AppColors.royalIndigo.withValues(alpha: 0.1),
            AppColors.solarAmber.withValues(alpha: 0.1),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: AppColors.royalIndigo.withValues(alpha: 0.2),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.auto_awesome,
                color: AppColors.royalIndigo,
                size: 20,
              ),
              const SizedBox(width: 8),
              const Text(
                '智能分析',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: AppColors.royalIndigo,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Text(
            '基於您的星盤配置，為您提供個性化的財務建議。這些建議結合了占星學原理和現代金融理論，幫助您更好地理解自己的財務潛力和投資方向。',
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey.shade700,
              height: 1.5,
            ),
          ),
        ],
      ),
    );
  }

  /// 構建個性化投資建議
  Widget _buildPersonalizedAdvice() {
    return _buildAISection(
      title: '個性化投資建議',
      icon: Icons.trending_up,
      color: AppColors.solarAmber,
      content: _generatePersonalizedAdvice(),
    );
  }

  /// 構建風險評估
  Widget _buildRiskAssessment() {
    return _buildAISection(
      title: '風險評估分析',
      icon: Icons.security,
      color: Colors.orange,
      content: _generateRiskAssessment(),
    );
  }

  /// 構建時機分析
  Widget _buildTimingAnalysis() {
    return _buildAISection(
      title: '投資時機分析',
      icon: Icons.schedule,
      color: AppColors.indigoLight,
      content: _generateTimingAnalysis(),
    );
  }

  /// 構建長期規劃建議
  Widget _buildLongTermPlanning() {
    return _buildAISection(
      title: '長期財務規劃',
      icon: Icons.timeline,
      color: AppColors.royalIndigo,
      content: _generateLongTermPlanning(),
    );
  }

  /// 構建 AI 分析區塊
  Widget _buildAISection({
    required String title,
    required IconData icon,
    required Color color,
    required List<String> content,
  }) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey.shade200),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(icon, color: color, size: 20),
              const SizedBox(width: 8),
              Text(
                title,
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: color,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          ...content.map((item) => Padding(
            padding: const EdgeInsets.only(bottom: 8),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Container(
                  width: 6,
                  height: 6,
                  margin: const EdgeInsets.only(top: 6, right: 8),
                  decoration: BoxDecoration(
                    color: color,
                    shape: BoxShape.circle,
                  ),
                ),
                Expanded(
                  child: Text(
                    item,
                    style: const TextStyle(
                      fontSize: 14,
                      height: 1.5,
                    ),
                  ),
                ),
              ],
            ),
          )),
        ],
      ),
    );
  }

  /// 生成個性化投資建議
  List<String> _generatePersonalizedAdvice() {
    if (_analysis == null) return [];

    final advice = <String>[];
    final style = _analysis!.financialStyle;

    // 根據風險承受度給建議
    switch (style.riskTolerance) {
      case RiskTolerance.conservative:
        advice.add('建議以穩健型投資為主，如定期存款、政府債券和藍籌股');
        advice.add('可考慮分散投資於不同的低風險資產類別');
        break;
      case RiskTolerance.moderate:
        advice.add('適合平衡型投資組合，結合股票和債券');
        advice.add('可考慮指數基金和平衡型基金作為核心配置');
        break;
      case RiskTolerance.aggressive:
        advice.add('可以考慮較高風險的成長型股票和新興市場');
        advice.add('適合主動型投資策略和創新型金融產品');
        break;
    }

    // 根據宮位影響給建議
    final houseInfluences = style.houseInfluences;
    if (houseInfluences['第二宮'] != null && houseInfluences['第二宮']! > 60) {
      advice.add('第二宮影響強烈，建議重視個人資產累積和現金流管理');
    }
    if (houseInfluences['第八宮'] != null && houseInfluences['第八宮']! > 60) {
      advice.add('第八宮影響顯著，適合投資他人資源或合夥投資項目');
    }

    return advice;
  }

  /// 生成風險評估
  List<String> _generateRiskAssessment() {
    if (_analysis == null) return [];

    final risks = <String>[];
    final challenges = _analysis!.potentialChallenges;

    risks.add('根據您的星盤配置，主要風險因素包括：');

    for (final challenge in challenges.take(3)) {
      risks.add(challenge);
    }

    risks.add('建議定期檢視投資組合，避免過度集中於單一資產類別');
    risks.add('保持適當的緊急備用金，應對突發財務需求');

    return risks;
  }

  /// 生成時機分析
  List<String> _generateTimingAnalysis() {
    final timing = <String>[];
    final now = DateTime.now();

    timing.add('基於當前行星運行週期的投資時機建議：');
    timing.add('短期內（1-3個月）：適合觀察市場趨勢，謹慎進場');
    timing.add('中期（3-12個月）：可考慮逐步建立核心投資部位');
    timing.add('長期（1-3年）：適合制定長期投資策略和退休規劃');

    // 根據月份給出季節性建議
    final month = now.month;
    if (month >= 3 && month <= 5) {
      timing.add('春季期間：適合新投資計劃的啟動和資產配置調整');
    } else if (month >= 6 && month <= 8) {
      timing.add('夏季期間：關注成長型投資機會，但需注意市場波動');
    } else if (month >= 9 && month <= 11) {
      timing.add('秋季期間：適合收穫獲利和重新平衡投資組合');
    } else {
      timing.add('冬季期間：適合保守策略和來年投資規劃');
    }

    return timing;
  }

  /// 生成長期規劃建議
  List<String> _generateLongTermPlanning() {
    final planning = <String>[];

    planning.add('基於您的財務星盤，建議制定以下長期規劃：');
    planning.add('建立多元化的投資組合，包含不同資產類別和地區');
    planning.add('設定明確的財務目標和時間表，如退休規劃、子女教育基金等');
    planning.add('定期檢視和調整投資策略，適應人生不同階段的需求');
    planning.add('考慮稅務優化策略，合法減少投資稅負');
    planning.add('建立完善的風險管理機制，包括保險規劃和緊急基金');
    planning.add('持續學習投資知識，提升財務管理能力');

    return planning;
  }
}

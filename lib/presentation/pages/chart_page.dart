import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../../astreal.dart';
import '../../shared/utils/chart_pdf_generator.dart';
import '../../shared/widgets/chart_elements_widget.dart';
import '../../shared/widgets/classical_astrology_widget.dart';
import '../../shared/widgets/common/responsive_wrapper.dart';
import '../viewmodels/recent_charts_viewmodel.dart';
import '../widgets/dialogs/consultation_analysis_dialog.dart';
import '../widgets/dialogs/copy_options_dialog.dart';
import '../widgets/dialogs/theme_copy_dialog.dart';
import 'ai_interpretation_selection_page.dart';
import 'chart_selection_page.dart';
import 'main/settings_page.dart';

class ChartPage extends StatefulWidget {
  final ChartData? chartData;
  final ChartType? chartType;

  const ChartPage({super.key, this.chartData, this.chartType});

  @override
  _ChartPageState createState() => _ChartPageState();
}

class _ChartPageState extends State<ChartPage>
    with TickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _initTabController();
  }

  void _initTabController() {
    // 初始化為 6 個標籤頁（不包含法達盤）
    _tabController = TabController(length: 6, vsync: this);
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();

    final chartViewModel = Provider.of<ChartViewModel>(context, listen: false);
    final settingsViewModel = Provider.of<SettingsViewModel>(context, listen: false);
    final recentChartsViewModel = Provider.of<RecentChartsViewModel>(context, listen: false);

    // 設置全局的 SettingsViewModel
    chartViewModel.setSettingsViewModel(settingsViewModel);

    // 如果提供了 chartType，設置星盤類型
    if (widget.chartType != null) {
      chartViewModel.setChartType(widget.chartType!, context: context);
    }

    // 記錄到最近使用的星盤
    recentChartsViewModel.addOrUpdateRecentChart(chartViewModel.chartData);

    // 檢查並更新 TabController
    _checkAndUpdateTabController(chartViewModel);
  }

  /// 檢查並更新 TabController（如果需要）
  void _checkAndUpdateTabController(ChartViewModel viewModel) {
    final currentTabCount = _tabController.length;
    final newTabCount = viewModel.chartType == ChartType.firdaria ? 7 : 6;

    if (currentTabCount != newTabCount) {
      final currentIndex = _tabController.index;

      // 處置舊的 TabController
      _tabController.dispose();

      // 創建新的 TabController
      _tabController = TabController(length: newTabCount, vsync: this);

      // 嘗試保持當前選中的標籤頁，但要確保索引有效
      final safeIndex = currentIndex < newTabCount ? currentIndex : 0;
      _tabController.index = safeIndex;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<ChartViewModel>(
      builder: (context, viewModel, _) {
        // 檢查並更新 TabController
        _checkAndUpdateTabController(viewModel);

        return Scaffold(
          appBar: AppBar(
            leading: IconButton(
              icon: const Icon(Icons.arrow_back, color: Colors.white),
              onPressed: () => Navigator.of(context).pop(),
            ),
            title: Text(viewModel.getChartTitle()),
            backgroundColor: AppColors.royalIndigo,
            foregroundColor: Colors.white,
            actions: [
              // 功能選單按鈕
              PopupMenuButton<String>(
                icon: const Icon(Icons.more_vert, color: Colors.white),
                tooltip: '功能選單',
                onSelected: (String action) async {
                  switch (action) {
                    case 'settings':
                      _openSettings();
                      break;
                    case 'chart_type':
                      _switchChartType();
                      break;
                    case 'copy_info':
                      _copyChartInfo();
                      break;
                    case 'copy_theme_info':
                      _copyThemeInfo();
                      break;
                    case 'generate_pdf':
                      _generatePdf();
                      break;
                    case 'send_email':
                      _sendEmail();
                      break;
                    case 'consultation_analysis':
                      _startConsultationAnalysis();
                      break;
                    case 'save_image':
                      _saveChartImage();
                      break;
                    case 'share_image':
                      _shareChartImage();
                      break;
                    case 'ai_interpretation':
                      _startAIInterpretation();
                      break;
                  }
                },
                itemBuilder: (BuildContext context) => [
                  // 設定
                  const PopupMenuItem<String>(
                    value: 'settings',
                    child: Row(
                      children: [
                        Icon(Icons.settings, color: AppColors.royalIndigo),
                        SizedBox(width: 12),
                        Text('設定'),
                      ],
                    ),
                  ),
                  // 切換星盤類型
                  const PopupMenuItem<String>(
                    value: 'chart_type',
                    child: Row(
                      children: [
                        Icon(Icons.swap_horiz, color: AppColors.royalIndigo),
                        SizedBox(width: 12),
                        Text('切換星盤類型'),
                      ],
                    ),
                  ),
                  // Debug 功能
                  if (kDebugMode) ...[
                    // 複製星盤資訊
                    const PopupMenuItem<String>(
                      value: 'copy_info',
                      child: Row(
                        children: [
                          Icon(Icons.content_copy, color: AppColors.royalIndigo),
                          SizedBox(width: 12),
                          Text('複製星盤資訊'),
                        ],
                      ),
                    ),
                    // 複製主題星盤資訊
                    const PopupMenuItem<String>(
                      value: 'copy_theme_info',
                      child: Row(
                        children: [
                          Icon(Icons.topic, color: AppColors.royalIndigo),
                          SizedBox(width: 12),
                          Text('主題星盤資訊'),
                        ],
                      ),
                    ),
                    // 生成 PDF
                    const PopupMenuItem<String>(
                      value: 'generate_pdf',
                      child: Row(
                        children: [
                          Icon(Icons.picture_as_pdf, color: AppColors.royalIndigo),
                          SizedBox(width: 12),
                          Text('生成 PDF'),
                        ],
                      ),
                    ),
                    // 發送郵件
                    const PopupMenuItem<String>(
                      value: 'send_email',
                      child: Row(
                        children: [
                          Icon(Icons.email, color: AppColors.royalIndigo),
                          SizedBox(width: 12),
                          Text('發送郵件'),
                        ],
                      ),
                    ),
                    // 占星諮詢分析
                    const PopupMenuItem<String>(
                      value: 'consultation_analysis',
                      child: Row(
                        children: [
                          Icon(Icons.analytics, color: AppColors.royalIndigo),
                          SizedBox(width: 12),
                          Text('占星諮詢分析'),
                        ],
                      ),
                    ),
                  ],
                  // 儲存星盤圖片
                  const PopupMenuItem<String>(
                    value: 'save_image',
                    child: Row(
                      children: [
                        Icon(Icons.save_alt, color: AppColors.royalIndigo),
                        SizedBox(width: 12),
                        Text('儲存星盤圖片'),
                      ],
                    ),
                  ),
                  // 分享星盤圖片
                  const PopupMenuItem<String>(
                    value: 'share_image',
                    child: Row(
                      children: [
                        Icon(Icons.share, color: AppColors.royalIndigo),
                        SizedBox(width: 12),
                        Text('分享星盤圖片'),
                      ],
                    ),
                  ),
                  // AI 解讀
                  const PopupMenuItem<String>(
                    value: 'ai_interpretation',
                    child: Row(
                      children: [
                        Icon(Icons.psychology, color: AppColors.royalIndigo),
                        SizedBox(width: 12),
                        Text('解讀'),
                      ],
                    ),
                  ),
                ],
              ),
            ],
            bottom: TabBar(
              controller: _tabController,
              isScrollable: true,
              labelColor: Colors.white,
              unselectedLabelColor: Colors.white70,
              indicatorColor: AppColors.solarAmber,
              tabs: [
                const Tab(
                  icon: Icon(Icons.circle_outlined, size: 18),
                  text: '星盤圖',
                ),
                // 法達盤標籤頁，只在法達盤時顯示
                if (viewModel.chartType == ChartType.firdaria)
                  const Tab(
                    icon: Icon(Icons.hourglass_full, size: 18),
                    text: '法達盤',
                  ),
                const Tab(
                  icon: Icon(Icons.list, size: 18),
                  text: '行星位置',
                ),
                const Tab(
                  icon: Icon(Icons.home_outlined, size: 18),
                  text: '宮位',
                ),
                const Tab(
                  icon: Icon(Icons.grid_on, size: 18),
                  text: '相位表',
                ),
                const Tab(
                  icon: Icon(Icons.pie_chart, size: 18),
                  text: '統計',
                ),
                const Tab(
                  icon: Icon(Icons.auto_awesome, size: 18),
                  text: '古典占星',
                ),
              ],
            ),
          ),
          body: viewModel.isLoading
              ? const Center(child: CircularProgressIndicator())
              : ResponsivePageWrapper(
                  maxWidth: 1200.0, // 星盤頁面需要較大寬度
                  child: TabBarView(
                    controller: _tabController,
                    children: [
                      ChartViewWidget(viewModel: viewModel),
                      // 法達盤內容，只在法達盤時顯示
                      if (viewModel.chartType == ChartType.firdaria)
                        FirdariaWidget(viewModel: viewModel),
                      PlanetListWidget(viewModel: viewModel),
                      HousesWidget(viewModel: viewModel),
                      AspectTableWidget(viewModel: viewModel),
                      ChartElementsWidget(viewModel: viewModel),
                      ClassicalAstrologyWidget(viewModel: viewModel),
                    ],
                  ),
                ),
          // AI 解讀浮動按鈕
          floatingActionButton: FloatingActionButton.extended(
            onPressed: () => _navigateToAIInterpretation(viewModel.chartData),
            backgroundColor: AppColors.solarAmber,
            foregroundColor: Colors.white,
            icon: const Icon(Icons.psychology),
            label: const Text('解讀'),
          ),
          floatingActionButtonLocation: FloatingActionButtonLocation.endFloat,
        );
      },
    );
  }

  /// 開始 AI 解讀
  void _startAIInterpretation() {
    final chartViewModel = Provider.of<ChartViewModel>(context, listen: false);
    _navigateToAIInterpretation(chartViewModel.chartData);
  }

  /// 複製星盤資訊
  void _copyChartInfo() async {
    final chartViewModel = Provider.of<ChartViewModel>(context, listen: false);

    if (!chartViewModel.isCopying) {
      // 顯示複製選項對話框
      final options = await showCopyOptionsDialog(context);

      // 如果用戶選擇了選項（沒有取消）
      if (options != null && mounted) {
        // 將用戶選擇的選項傳遞給 copyChartInfo 方法
        final success = await chartViewModel.copyChartInfo(options: options);
        if (mounted && success) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('星盤資訊已複製到剪貼板'),
              duration: Duration(seconds: 2),
            ),
          );
        }
      }
    }
  }

  /// 複製主題星盤資訊
  void _copyThemeInfo() async {
    final chartViewModel = Provider.of<ChartViewModel>(context, listen: false);

    if (!chartViewModel.isCopying) {
      // 顯示主題複製對話框
      final result = await showThemeCopyDialog(context);

      // 如果用戶選擇了主題（沒有取消）
      if (result != null && mounted) {
        final success = await chartViewModel.copyThemeChartInfo(
          themeKey: result['theme'],
          themeInfo: result['themeInfo'],
          options: result['copyOptions'],
        );
        if (mounted && success) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('${result['themeInfo'].title}星盤資訊已複製到剪貼板'),
              duration: const Duration(seconds: 2),
            ),
          );
        }
      }
    }
  }

  /// 生成 PDF
  void _generatePdf() async {
    final chartViewModel = Provider.of<ChartViewModel>(context, listen: false);

    if (!chartViewModel.isGeneratingPdf) {
      try {
        // 顯示 PDF 選項對話框
        final action = await _showPdfOptionsDialog();
        if (action != null && mounted) {
          try {
            // 顯示生成中的提示
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('正在生成 PDF，請稍候...'),
                duration: Duration(seconds: 2),
              ),
            );

            final pdfBytes = await chartViewModel.generatePdf();
            if (pdfBytes != null && mounted) {
              // 清除之前的 SnackBar
              ScaffoldMessenger.of(context).hideCurrentSnackBar();

              switch (action) {
                case 'preview':
                  await ChartPdfGenerator.previewPdf(
                    context: context,
                    pdfBytes: pdfBytes,
                    title: '${chartViewModel.primaryPerson.name}的星盤數據',
                  );
                  break;
                case 'share':
                  await ChartPdfGenerator.savePdfAndShare(
                    context: context,
                    pdfBytes: pdfBytes,
                    fileName: '${chartViewModel.primaryPerson.name}_星盤數據.pdf',
                  );
                  break;
              }
            } else if (mounted) {
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('無法生成 PDF，請確保星盤數據已計算完成'),
                  duration: Duration(seconds: 5),
                ),
              );
            }
          } catch (e) {
            if (mounted) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text('生成 PDF 失敗: ${e.toString().replaceAll('Exception: ', '')}'),
                  duration: const Duration(seconds: 5),
                ),
              );
            }
          }
        }
      } catch (dialogError) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('顯示 PDF 選項對話框失敗: $dialogError'),
              duration: const Duration(seconds: 3),
            ),
          );
        }
      }
    } else if (mounted) {
      // 如果已經在生成 PDF，顯示提示
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('正在生成 PDF，請稍候...'),
          duration: Duration(seconds: 2),
        ),
      );
    }
  }

  /// 顯示 PDF 選項對話框
  Future<String?> _showPdfOptionsDialog() async {
    return showDialog<String>(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('PDF 選項'),
          content: const Text('請選擇要執行的操作：'),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('取消'),
            ),
            TextButton(
              onPressed: () => Navigator.of(context).pop('preview'),
              child: const Text('預覽'),
            ),
            ElevatedButton(
              onPressed: () => Navigator.of(context).pop('share'),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.royalIndigo,
                foregroundColor: Colors.white,
              ),
              child: const Text('分享'),
            ),
          ],
        );
      },
    );
  }

  /// 發送郵件
  void _sendEmail() async {
    final chartViewModel = Provider.of<ChartViewModel>(context, listen: false);

    if (!chartViewModel.isSendingEmail) {
      final result = await _showEmailInputDialog();
      if (result != null) {
        final success = await chartViewModel.sendEmail(
          email: result['email'],
          useHtml: result['useHtml'],
        );
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(success
                  ? '星盤數據已發送至 ${result['email']}'
                  : '發送失敗，請稍後再試'),
              duration: const Duration(seconds: 3),
            ),
          );
        }
      }
    }
  }

  /// 顯示郵件輸入對話框
  Future<Map<String, dynamic>?> _showEmailInputDialog() async {
    final emailController = TextEditingController();
    bool useHtml = true;

    return showDialog<Map<String, dynamic>>(
      context: context,
      builder: (BuildContext context) {
        return StatefulBuilder(
          builder: (context, setState) {
            return AlertDialog(
              title: const Text('發送郵件'),
              content: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  TextField(
                    controller: emailController,
                    decoration: const InputDecoration(
                      labelText: '收件人郵箱',
                      hintText: '請輸入郵箱地址',
                    ),
                    keyboardType: TextInputType.emailAddress,
                  ),
                  const SizedBox(height: 16),
                  CheckboxListTile(
                    title: const Text('使用 HTML 格式'),
                    value: useHtml,
                    onChanged: (value) {
                      setState(() {
                        useHtml = value ?? true;
                      });
                    },
                  ),
                ],
              ),
              actions: [
                TextButton(
                  onPressed: () => Navigator.of(context).pop(),
                  child: const Text('取消'),
                ),
                ElevatedButton(
                  onPressed: () {
                    if (emailController.text.isNotEmpty) {
                      Navigator.of(context).pop({
                        'email': emailController.text,
                        'useHtml': useHtml,
                      });
                    }
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppColors.royalIndigo,
                    foregroundColor: Colors.white,
                  ),
                  child: const Text('發送'),
                ),
              ],
            );
          },
        );
      },
    );
  }

  /// 開始占星諮詢分析
  void _startConsultationAnalysis() async {
    final chartViewModel = Provider.of<ChartViewModel>(context, listen: false);

    if (!chartViewModel.isCopying) {
      // 顯示占星諮詢分析對話框
      final result = await showConsultationAnalysisDialog(context);

      // 如果用戶選擇了主題（沒有取消）
      if (result != null && mounted) {
        final success = await chartViewModel.copyConsultationAnalysis(
          themeKey: result['theme'],
          themeInfo: result['themeInfo'],
        );
        if (mounted && success) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('${result['themeInfo'].title}諮詢分析已複製到剪貼板'),
              duration: const Duration(seconds: 2),
            ),
          );
        } else if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('複製失敗，請重試'),
              duration: Duration(seconds: 2),
            ),
          );
        }
      }
    }
  }

  /// 切換星盤類型
  void _switchChartType() async {
    final chartViewModel = Provider.of<ChartViewModel>(context, listen: false);

    try {
      logger.d('開始切換星盤類型');

      final result = await Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => ChartSelectionPage(
            primaryPerson: chartViewModel.primaryPerson,
            secondaryPerson: chartViewModel.secondaryPerson,
            initialChartType: chartViewModel.chartType,
            isChangingChartType: true,
          ),
        ),
      );

      if (result is ChartData && mounted) {
        logger.d('收到新的 ChartData，開始更新星盤：${result.chartType.name}');
        await chartViewModel.updateChartData(result);
        logger.d('星盤更新完成');
      }
    } catch (e) {
      logger.e('切換星盤類型時出錯: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('切換失敗: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  /// 儲存星盤圖片
  void _saveChartImage() async {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('儲存功能開發中...'),
        backgroundColor: Colors.orange,
      ),
    );
  }

  /// 分享星盤圖片
  void _shareChartImage() async {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('分享功能開發中...'),
        backgroundColor: Colors.orange,
      ),
    );
  }

  /// 開啟設定
  void _openSettings() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const SettingsPage(),
      ),
    );
  }

  /// 導航到 AI 解讀頁面
  void _navigateToAIInterpretation(ChartData chartData) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => AIInterpretationSelectionPage(
          chartData: chartData,
        ),
      ),
    );
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }
}
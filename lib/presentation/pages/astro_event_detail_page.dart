import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../../astreal.dart';
import 'chart_page_new.dart';

/// 星象事件詳情頁面
class AstroEventDetailPage extends StatelessWidget {
  final AstroEvent event;
  final BirthData? natalPerson;
  final double latitude;
  final double longitude;

  const AstroEventDetailPage({
    super.key,
    required this.event,
    this.natalPerson,
    this.latitude = 25.0,
    this.longitude = 121.0,
  });

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(event.title),
        backgroundColor: AppColors.royalIndigo,
        foregroundColor: Colors.white,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 事件基本信息卡片
            _buildEventInfoCard(context),

            // 事件詳細描述卡片
            _buildEventDescriptionCard(),

            // 星盤查看選項卡片
            _buildChartOptionsCard(context),

            // 如果有本命盤人物，顯示個人化影響
            if (natalPerson != null) ...[
              _buildPersonalImpactCard(),
            ],

            // 相關建議卡片
            _buildAdviceCard(),
          ],
        ),
      ),
    );
  }

  /// 構建事件基本信息卡片
  Widget _buildEventInfoCard(BuildContext context) {
    return StyledCard(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 事件標題和圖標
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: event.color,
                    shape: BoxShape.circle,
                  ),
                  child: Icon(
                    event.icon,
                    color: Colors.white,
                    size: 24,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        event.title,
                        style: const TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      Text(
                        event.typeDisplayName,
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.grey[600],
                        ),
                      ),
                    ],
                  ),
                ),
                // 重要度指示器
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: event.importanceColor,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      const Icon(
                        Icons.star,
                        color: Colors.white,
                        size: 16,
                      ),
                      const SizedBox(width: 4),
                      Text(
                        '${event.importance}',
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 12,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),

            // 時間信息
            _buildInfoRow(
              Icons.access_time,
              '發生時間',
              _formatDateTime(event.dateTime),
            ),
            const SizedBox(height: 0),

            // 事件類型
            _buildInfoRow(
              Icons.category,
              '事件類型',
              event.typeDisplayName,
            ),

            // 如果有額外數據，顯示相關信息
            if (event.additionalData != null) ...[
              const SizedBox(height: 0),
              ..._buildAdditionalInfo(context),
            ],
          ],
        ),
      ),
    );
  }

  /// 構建事件描述卡片
  Widget _buildEventDescriptionCard() {
    return StyledCard(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              '事件描述',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: AppColors.royalIndigo,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              event.description,
              style: const TextStyle(fontSize: 14),
            ),
            const SizedBox(height: 12),
            Text(
              _getEventExplanation(),
              style: TextStyle(
                fontSize: 13,
                color: Colors.grey[600],
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 構建星盤查看選項卡片
  Widget _buildChartOptionsCard(BuildContext context) {
    return StyledCard(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              '查看星盤',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: AppColors.royalIndigo,
              ),
            ),
            const SizedBox(height: 12),

            // 查看事件時刻星盤
            _buildChartOptionButton(
              context,
              icon: Icons.public,
              title: '事件時刻星盤',
              subtitle: '查看事件發生時的天象配置',
              onTap: () => _navigateToEventChart(context),
            ),

            // 如果有本命盤人物，顯示行運盤選項
            if (natalPerson != null) ...[
              const SizedBox(height: 8),
              _buildChartOptionButton(
                context,
                icon: Icons.person,
                title: '個人行運盤',
                subtitle: '查看事件對${natalPerson!.name}的影響',
                onTap: () => _navigateToTransitChart(context),
              ),
            ],
          ],
        ),
      ),
    );
  }

  /// 構建個人化影響卡片
  Widget _buildPersonalImpactCard() {
    return StyledCard(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '對${natalPerson!.name}的影響',
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: AppColors.royalIndigo,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              _getPersonalImpact(),
              style: const TextStyle(fontSize: 14),
            ),
          ],
        ),
      ),
    );
  }

  /// 構建建議卡片
  Widget _buildAdviceCard() {
    return StyledCard(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              '相關建議',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: AppColors.royalIndigo,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              _getAdvice(),
              style: const TextStyle(fontSize: 14),
            ),
          ],
        ),
      ),
    );
  }

  /// 構建信息行
  Widget _buildInfoRow(IconData icon, String label, String value) {
    return Row(
      children: [
        Icon(icon, size: 16, color: Colors.grey[600]),
        const SizedBox(width: 8),
        Text(
          '$label: ',
          style: TextStyle(
            fontSize: 14,
            color: Colors.grey[600],
          ),
        ),
        Expanded(
          child: Text(
            value,
            style: const TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
      ],
    );
  }

  /// 構建星盤選項按鈕
  Widget _buildChartOptionButton(
    BuildContext context, {
    required IconData icon,
    required String title,
    required String subtitle,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(8),
      child: Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          border: Border.all(color: Colors.grey[300]!),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Row(
          children: [
            Icon(icon, color: AppColors.royalIndigo),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: const TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  Text(
                    subtitle,
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.grey[600],
                    ),
                  ),
                ],
              ),
            ),
            Icon(Icons.arrow_forward_ios, size: 16, color: Colors.grey[400]),
          ],
        ),
      ),
    );
  }

  /// 構建額外信息
  List<Widget> _buildAdditionalInfo(BuildContext context) {
    final additionalData = event.additionalData!;
    final widgets = <Widget>[];

    // 月相信息
    if (additionalData.containsKey('moonPhase')) {
      final moonPhase = additionalData['moonPhase'] as MoonPhaseType;
      widgets.add(_buildInfoRow(
        Icons.brightness_4,
        '月相類型',
        AstroEvent.getMoonPhaseDisplayName(moonPhase),
      ));
    }

    // 節氣信息
    if (additionalData.containsKey('seasonType')) {
      widgets.add(_buildInfoRow(
        Icons.wb_sunny,
        '節氣類型',
        '二分二至節氣',
      ));
    }

    // 相位信息
    if (additionalData.containsKey('planet1') && additionalData.containsKey('planet2')) {
      widgets.add(_buildInfoRow(
        Icons.timeline,
        '相位行星',
        '${additionalData['planet1']} - ${additionalData['planet2']}',
      ));
    }

    // 日月蝕信息
    if (additionalData.containsKey('eclipseType')) {
      final eclipseType = additionalData['eclipseType'] as EclipseType;
      widgets.add(_buildInfoRow(
        AstroEvent.getEclipseIcon(eclipseType),
        '蝕相類型',
        AstroEvent.getEclipseDisplayName(eclipseType),
      ));

      // 可見性信息
      if (additionalData.containsKey('isVisible')) {
        final isVisible = additionalData['isVisible'] as bool;
        widgets.add(_buildInfoRow(
          isVisible ? Icons.visibility : Icons.visibility_off,
          '可見性',
          isVisible ? '在此地點可見' : '在此地點不可見',
        ));
      }

      // 蝕分信息
      if (additionalData.containsKey('magnitude')) {
        final magnitude = additionalData['magnitude'] as double;
        widgets.add(_buildInfoRow(
          Icons.circle_outlined,
          '蝕分',
          '${(magnitude * 100).toStringAsFixed(1)}%',
        ));
      }

      // 詳細時間階段信息（新增）
      if (additionalData.containsKey('detailedInfo')) {
        final detailedInfo = additionalData['detailedInfo'];
        if (detailedInfo is SolarEclipseDetails) {
          widgets.addAll(_buildSolarEclipsePhaseInfo(context, detailedInfo));
        } else if (detailedInfo is LunarEclipseDetails) {
          widgets.addAll(_buildLunarEclipsePhaseInfo(context, detailedInfo));
        }
      }

      // 持續時間信息（新增）
      if (additionalData.containsKey('totalityDuration')) {
        final totalityDuration = additionalData['totalityDuration'] as Duration?;
        if (totalityDuration != null) {
          final hours = totalityDuration.inHours;
          final minutes = totalityDuration.inMinutes % 60;
          final seconds = totalityDuration.inSeconds % 60;
          widgets.add(_buildInfoRow(
            Icons.timer,
            '全食持續時間',
            '$hours時$minutes分$seconds秒',
          ));
        }
      }
    }

    // 換座信息
    if (additionalData.containsKey('fromSign') && additionalData.containsKey('toSign')) {
      widgets.add(_buildInfoRow(
        Icons.swap_horiz,
        '星座變化',
        '${additionalData['fromSign']} → ${additionalData['toSign']}',
      ));
    }

    return widgets;
  }

  /// 構建日食階段信息
  List<Widget> _buildSolarEclipsePhaseInfo(BuildContext context, SolarEclipseDetails detailedInfo) {
    final widgets = <Widget>[];

    // 添加全球時間線表格
    widgets.add(_buildEclipseTimelineCard(detailedInfo));

    // 添加統計信息卡片
    widgets.add(_buildEclipseStatisticsCard(context, detailedInfo));

    return widgets;
  }

  /// 構建月食階段信息
  List<Widget> _buildLunarEclipsePhaseInfo(BuildContext context, LunarEclipseDetails detailedInfo) {
    final widgets = <Widget>[];

    // 添加全球時間線表格
    widgets.add(_buildLunarEclipseTimelineCard(detailedInfo));

    // 添加統計信息卡片
    widgets.add(_buildLunarEclipseStatisticsCard(context, detailedInfo));

    return widgets;
  }

  /// 構建日食時間線卡片
  Widget _buildEclipseTimelineCard(SolarEclipseDetails detailedInfo) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: StyledCard(
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                '全球日食發生時間－時間線',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: AppColors.royalIndigo,
                ),
              ),
              const SizedBox(height: 8),
              const Text(
                '如果天空晴朗，地球夜晚的任何地方都可以看到月食。在某些地方可以看到整個月食，而在其他地方，月亮會在月食期間升起或落下。',
                style: TextStyle(fontSize: 12, color: Colors.grey),
              ),
              const SizedBox(height: 12),
              _buildTimelineTable(_createSolarEclipseTimeline(detailedInfo)),
            ],
          ),
        ),
      ),
    );
  }

  /// 獲取階段圖標
  IconData _getPhaseIcon(SolarEclipsePhase phase) {
    switch (phase) {
      case SolarEclipsePhase.maximum:
        return Icons.brightness_1; // 食甚
      case SolarEclipsePhase.localNoon:
        return Icons.wb_sunny; // 當地正午
      case SolarEclipsePhase.eclipseBegin:
        return Icons.play_arrow; // 初虧
      case SolarEclipsePhase.eclipseEnd:
        return Icons.stop; // 復圓
      case SolarEclipsePhase.totalityBegin:
        return Icons.brightness_2; // 食既
      case SolarEclipsePhase.totalityEnd:
        return Icons.brightness_4; // 生光
      case SolarEclipsePhase.centerLineBegin:
        return Icons.timeline; // 中心線開始
      case SolarEclipsePhase.centerLineEnd:
        return Icons.timeline; // 中心線結束
      case SolarEclipsePhase.annularToTotal:
        return Icons.transform; // 環食變全食
      case SolarEclipsePhase.totalToAnnular:
        return Icons.transform; // 全食變環食
    }
  }

  /// 構建時間線表格
  Widget _buildTimelineTable(List<EclipseTimelineEntry> timeline) {
    return Container(
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey[300]!),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        children: [
          // 表格標題
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: AppColors.royalIndigo.withValues(alpha: 0.1),
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(8),
                topRight: Radius.circular(8),
              ),
            ),
            child: const Row(
              children: [
                Expanded(flex: 3, child: Text('日食階段', style: TextStyle(fontWeight: FontWeight.bold))),
                Expanded(flex: 3, child: Text('全球UTC時間', style: TextStyle(fontWeight: FontWeight.bold))),
                Expanded(flex: 3, child: Text('台北當地時間*', style: TextStyle(fontWeight: FontWeight.bold))),
                Expanded(flex: 2, child: Text('台北可見', style: TextStyle(fontWeight: FontWeight.bold))),
              ],
            ),
          ),
          // 表格內容
          ...timeline.asMap().entries.map((entry) {
            final index = entry.key;
            final item = entry.value;
            return Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: index % 2 == 0 ? Colors.grey[50] : Colors.white,
                border: Border(
                  bottom: BorderSide(
                    color: Colors.grey[200]!,
                    width: 0.5,
                  ),
                ),
              ),
              child: Row(
                children: [
                  Expanded(flex: 3, child: Text(item.phaseName, style: const TextStyle(fontSize: 12))),
                  Expanded(flex: 3, child: Text(_formatDateTimeShort(item.utcTime), style: const TextStyle(fontSize: 12))),
                  Expanded(flex: 3, child: Text(_formatDateTimeShort(item.localTime), style: const TextStyle(fontSize: 12))),
                  Expanded(flex: 2, child: Text(item.getVisibilityText(), style: const TextStyle(fontSize: 12))),
                ],
              ),
            );
          }).toList(),
        ],
      ),
    );
  }

  /// 構建統計信息卡片
  Widget _buildEclipseStatisticsCard(BuildContext context, SolarEclipseDetails detailedInfo) {
    final statistics = _createEclipseStatistics(detailedInfo);

    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: StyledCard(
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  const Text(
                    '關於這次日食的簡短訊息',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: AppColors.royalIndigo,
                    ),
                  ),
                  _buildDetailButton(context),
                ],
              ),
              const SizedBox(height: 12),
              _buildStatisticsTable(statistics),
            ],
          ),
        ),
      ),
    );
  }

  /// 構建統計信息表格
  Widget _buildStatisticsTable(EclipseStatistics statistics) {
    final data = [
      {'label': '規模', 'value': statistics.magnitude.toStringAsFixed(3), 'comment': '地球本影覆蓋的月球直徑比'},
      {'label': '遮蔽', 'value': '${statistics.obscuration.toStringAsFixed(1)}%', 'comment': '地球本影覆蓋月球面積的百分比'},
      if (statistics.penumbralMagnitude > 0)
        {'label': '半影星等', 'value': statistics.penumbralMagnitude.toStringAsFixed(3), 'comment': '地球半影覆蓋的月球直徑比例'},
      {'label': '總時長', 'value': _formatDuration(statistics.totalDuration), 'comment': '所有食相開始和結束之間的時期'},
      if (statistics.totalityDuration != null)
        {'label': '全食持續時間', 'value': _formatDuration(statistics.totalityDuration!), 'comment': '總階段開始與結束之間的時間'},
      if (statistics.partialDuration != null)
        {'label': '部分階段的持續時間', 'value': _formatDuration(statistics.partialDuration!), 'comment': '兩個部分階段的合併時間'},
      if (statistics.penumbralDuration != null)
        {'label': '半影期的持續時間', 'value': _formatDuration(statistics.penumbralDuration!), 'comment': '兩個半影期的合併時間'},
    ];

    return Container(
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey[300]!),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        children: [
          // 表格標題
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: AppColors.royalIndigo.withValues(alpha: 0.1),
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(8),
                topRight: Radius.circular(8),
              ),
            ),
            child: const Row(
              children: [
                Expanded(flex: 2, child: Text('數據', style: TextStyle(fontWeight: FontWeight.bold))),
                Expanded(flex: 2, child: Text('價值', style: TextStyle(fontWeight: FontWeight.bold))),
                Expanded(flex: 4, child: Text('評論', style: TextStyle(fontWeight: FontWeight.bold))),
              ],
            ),
          ),
          // 表格內容
          ...data.asMap().entries.map((entry) {
            final index = entry.key;
            final item = entry.value;
            return Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: index % 2 == 0 ? Colors.grey[50] : Colors.white,
                border: Border(
                  bottom: BorderSide(
                    color: Colors.grey[200]!,
                    width: 0.5,
                  ),
                ),
              ),
              child: Row(
                children: [
                  Expanded(flex: 2, child: Text(item['label']!, style: const TextStyle(fontSize: 12))),
                  Expanded(flex: 2, child: Text(item['value']!, style: const TextStyle(fontSize: 12, fontWeight: FontWeight.w500))),
                  Expanded(flex: 4, child: Text(item['comment']!, style: const TextStyle(fontSize: 11, color: Colors.grey))),
                ],
              ),
            );
          }).toList(),
        ],
      ),
    );
  }

  /// 格式化日期時間
  String _formatDateTime(DateTime dateTime) {
    return '${dateTime.year}年${dateTime.month}月${dateTime.day}日 '
        '${dateTime.hour.toString().padLeft(2, '0')}:'
        '${dateTime.minute.toString().padLeft(2, '0')}:'
        '${dateTime.second.toString().padLeft(2, '0')}';
  }

  /// 格式化日期時間（簡短版）
  String _formatDateTimeShort(DateTime dateTime) {
    return '${dateTime.month}月${dateTime.day}日 '
        '${dateTime.hour.toString().padLeft(2, '0')}:'
        '${dateTime.minute.toString().padLeft(2, '0')}:'
        '${dateTime.second.toString().padLeft(2, '0')}';
  }

  /// 格式化持續時間
  String _formatDuration(Duration duration) {
    final hours = duration.inHours;
    final minutes = duration.inMinutes % 60;
    if (hours > 0) {
      return '${hours}小時${minutes}分鐘';
    } else {
      return '${minutes}分鐘';
    }
  }

  /// 創建日食時間線
  List<EclipseTimelineEntry> _createSolarEclipseTimeline(SolarEclipseDetails detailedInfo) {
    final timeline = <EclipseTimelineEntry>[];
    final phases = detailedInfo.getAllPhases();

    // 定義階段順序和可見性
    final phaseOrder = [
      SolarEclipsePhase.eclipseBegin,
      SolarEclipsePhase.totalityBegin,
      SolarEclipsePhase.maximum,
      SolarEclipsePhase.totalityEnd,
      SolarEclipsePhase.eclipseEnd,
    ];

    for (final phase in phaseOrder) {
      final dateTime = phases[phase];
      if (dateTime != null) {
        final phaseName = SolarEclipseDetails.getPhaseName(phase);
        final visibility = _determineSolarEclipseVisibility(phase, dateTime);

        timeline.add(EclipseTimelineEntry(
          phaseName: phaseName,
          utcTime: dateTime.toUtc(),
          localTime: dateTime, // 假設已經是當地時間
          visibility: visibility,
        ));
      }
    }

    return timeline;
  }

  /// 創建月食時間線
  List<EclipseTimelineEntry> _createLunarEclipseTimeline(LunarEclipseDetails detailedInfo) {
    final timeline = <EclipseTimelineEntry>[];
    final phases = detailedInfo.getAllPhases();

    // 定義階段順序
    final phaseOrder = [
      LunarEclipsePhase.penumbralBegin,
      LunarEclipsePhase.partialBegin,
      LunarEclipsePhase.totalBegin,
      LunarEclipsePhase.maximum,
      LunarEclipsePhase.totalEnd,
      LunarEclipsePhase.partialEnd,
      LunarEclipsePhase.penumbralEnd,
    ];

    for (final phase in phaseOrder) {
      final dateTime = phases[phase];
      if (dateTime != null) {
        final phaseName = LunarEclipseDetails.getLunarPhaseName(phase);
        final visibility = _determineLunarEclipseVisibility(phase, dateTime);

        timeline.add(EclipseTimelineEntry(
          phaseName: phaseName,
          utcTime: dateTime.toUtc(),
          localTime: dateTime, // 假設已經是當地時間
          visibility: visibility,
        ));
      }
    }

    return timeline;
  }

  /// 判斷日食階段可見性
  VisibilityStatus _determineSolarEclipseVisibility(SolarEclipsePhase phase, DateTime dateTime) {
    // 這裡可以根據實際的天文計算來判斷可見性
    // 暫時使用簡化的邏輯
    final hour = dateTime.hour;

    switch (phase) {
      case SolarEclipsePhase.eclipseBegin:
        return hour < 6 || hour > 18 ? VisibilityStatus.belowHorizon : VisibilityStatus.maybeVisible;
      case SolarEclipsePhase.maximum:
      case SolarEclipsePhase.totalityBegin:
      case SolarEclipsePhase.totalityEnd:
        return hour >= 6 && hour <= 18 ? VisibilityStatus.visible : VisibilityStatus.belowHorizon;
      case SolarEclipsePhase.eclipseEnd:
        return hour >= 6 && hour <= 20 ? VisibilityStatus.visible : VisibilityStatus.belowHorizon;
      default:
        return VisibilityStatus.visible;
    }
  }

  /// 判斷月食階段可見性
  VisibilityStatus _determineLunarEclipseVisibility(LunarEclipsePhase phase, DateTime dateTime) {
    // 月食在夜晚可見，這裡使用簡化的邏輯
    final hour = dateTime.hour;

    if (hour >= 18 || hour <= 6) {
      return VisibilityStatus.visible;
    } else if (hour >= 16 && hour < 18) {
      return VisibilityStatus.maybeVisible;
    } else {
      return VisibilityStatus.belowHorizon;
    }
  }

  /// 創建日食統計數據
  EclipseStatistics _createEclipseStatistics(SolarEclipseDetails detailedInfo) {
    // 計算遮蔽百分比（基於食分）
    final obscuration = detailedInfo.magnitude >= 1.0
        ? 100.0
        : (detailedInfo.magnitude * 100);

    return EclipseStatistics(
      magnitude: detailedInfo.magnitude,
      obscuration: obscuration,
      totalDuration: detailedInfo.totalDuration ?? const Duration(hours: 3),
      totalityDuration: detailedInfo.totalityDuration,
      partialDuration: _calculatePartialDuration(detailedInfo),
    );
  }

  /// 創建月食統計數據
  EclipseStatistics _createLunarEclipseStatistics(LunarEclipseDetails detailedInfo) {
    final obscuration = detailedInfo.magnitude >= 1.0
        ? 100.0
        : (detailedInfo.magnitude * 100);

    return EclipseStatistics(
      magnitude: detailedInfo.magnitude,
      obscuration: obscuration,
      penumbralMagnitude: detailedInfo.penumbralMagnitude,
      totalDuration: detailedInfo.totalDuration ?? const Duration(hours: 5),
      totalityDuration: detailedInfo.totalityDuration,
      partialDuration: detailedInfo.partialDuration,
      penumbralDuration: detailedInfo.penumbralDuration,
    );
  }

  /// 計算偏食持續時間
  Duration? _calculatePartialDuration(SolarEclipseDetails detailedInfo) {
    if (detailedInfo.eclipseBeginTime != null &&
        detailedInfo.eclipseEndTime != null &&
        detailedInfo.totalityBeginTime != null &&
        detailedInfo.totalityEndTime != null) {

      final beforeTotality = detailedInfo.totalityBeginTime!.difference(detailedInfo.eclipseBeginTime!);
      final afterTotality = detailedInfo.eclipseEndTime!.difference(detailedInfo.totalityEndTime!);

      return beforeTotality + afterTotality;
    }
    return null;
  }

  /// 構建月食時間線卡片
  Widget _buildLunarEclipseTimelineCard(LunarEclipseDetails detailedInfo) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: StyledCard(
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                '全球月食發生時間－時間線',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: AppColors.royalIndigo,
                ),
              ),
              const SizedBox(height: 8),
              const Text(
                '如果天空晴朗，地球夜晚的任何地方都可以看到月食。在某些地方可以看到整個月食，而在其他地方，月亮會在月食期間升起或落下。',
                style: TextStyle(fontSize: 12, color: Colors.grey),
              ),
              const SizedBox(height: 12),
              _buildTimelineTable(_createLunarEclipseTimeline(detailedInfo)),
            ],
          ),
        ),
      ),
    );
  }

  /// 構建月食統計信息卡片
  Widget _buildLunarEclipseStatisticsCard(BuildContext context, LunarEclipseDetails detailedInfo) {
    final statistics = _createLunarEclipseStatistics(detailedInfo);

    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: StyledCard(
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  const Text(
                    '關於這次月食的簡短訊息',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: AppColors.royalIndigo,
                    ),
                  ),
                  _buildDetailButton(context),
                ],
              ),
              const SizedBox(height: 12),
              _buildStatisticsTable(statistics),
            ],
          ),
        ),
      ),
    );
  }

  /// 獲取事件解釋
  String _getEventExplanation() {
    switch (event.type) {
      case AstroEventType.moonPhase:
        return '月相變化代表情緒和直覺能量的轉換，影響人們的內在感受和潛意識活動。';
      case AstroEventType.seasonChange:
        return '節氣變化標誌著太陽能量的重要轉折點，對自然界和人類活動都有深遠影響。';
      case AstroEventType.planetAspect:
        return '行星相位代表不同天體能量之間的互動關係，影響個人和集體的運勢發展。';
      case AstroEventType.planetSignChange:
        return '行星換座表示行星能量進入新的表達方式，為相關領域帶來新的影響和機會。';
      case AstroEventType.planetRetrograde:
        return '行星逆行期間，該行星所代表的領域可能出現回顧、重新評估或延遲的情況。';
      case AstroEventType.eclipse:
        return '日月蝕是強力的轉化時期，往往帶來重要的開始或結束，影響深遠。';
    }
  }

  /// 獲取個人化影響
  String _getPersonalImpact() {
    if (natalPerson == null) return '';

    switch (event.type) {
      case AstroEventType.moonPhase:
        return '月相變化可能影響您的情緒狀態和直覺感受，建議關注內在需求和感受變化。';
      case AstroEventType.seasonChange:
        return '節氣變化為您帶來新的能量週期，適合調整生活節奏和目標方向。';
      case AstroEventType.planetAspect:
        return '行星相位可能在相關生活領域帶來新的機會或挑戰，建議保持開放和靈活的態度。';
      case AstroEventType.planetSignChange:
        return '行星換座可能為您的相關生活領域帶來新的發展方向和表達方式。';
      case AstroEventType.planetRetrograde:
        return '逆行期間建議回顧和整理相關領域的事務，避免重大決策。';
      case AstroEventType.eclipse:
        return '蝕相期間可能為您的人生帶來重要轉折，建議保持覺察和準備迎接變化。';
    }
  }

  /// 獲取建議
  String _getAdvice() {
    switch (event.type) {
      case AstroEventType.moonPhase:
        return '建議：關注情緒變化，進行冥想或反思活動，注意休息和情緒調節。';
      case AstroEventType.seasonChange:
        return '建議：調整作息時間，配合自然節律，制定新的計劃和目標。';
      case AstroEventType.planetAspect:
        return '建議：保持開放心態，善用行星能量的互動，在相關領域積極行動。';
      case AstroEventType.planetSignChange:
        return '建議：關注新的發展機會，調整策略和方法，適應新的能量表達。';
      case AstroEventType.planetRetrograde:
        return '建議：回顧過往經驗，整理相關事務，避免簽署重要合約或做重大決定。';
      case AstroEventType.eclipse:
        return '建議：保持覺察和彈性，準備迎接重要變化，關注新的開始或結束。';
    }
  }

  /// 導航到事件時刻星盤
  void _navigateToEventChart(BuildContext context) {
    // 創建事件時刻的虛擬人物數據
    final eventPerson = BirthData(
      id: 'event_${event.id}',
      name: event.title,
      dateTime: event.dateTime,
      latitude: latitude,
      longitude: longitude,
      birthPlace: '事件發生地',
    );

    // 創建星盤數據
    final chartData = ChartData(
      chartType: ChartType.event,
      primaryPerson: eventPerson,
    );

    // 導航到星盤頁面
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => ChangeNotifierProvider(
          create: (_) => ChartViewModel.withChartData(initialChartData: chartData),
          child: ChartPageNew(chartData: chartData),
        ),
      ),
    );
  }

  /// 導航到行運盤
  void _navigateToTransitChart(BuildContext context) {
    if (natalPerson == null) return;

    // 創建行運盤數據
    final chartData = ChartData(
      chartType: ChartType.transit,
      primaryPerson: natalPerson!,
      specificDate: event.dateTime,
    );

    // 導航到星盤頁面
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => ChangeNotifierProvider(
          create: (_) => ChartViewModel.withChartData(initialChartData: chartData),
          child: ChartPageNew(chartData: chartData),
        ),
      ),
    );
  }

  /// 構建詳情按鈕
  Widget _buildDetailButton(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: AppColors.royalIndigo.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: AppColors.royalIndigo.withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(8),
          onTap: () => _navigateToDetailPage(context),
          child: const Padding(
            padding: EdgeInsets.symmetric(horizontal: 12, vertical: 6),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  Icons.info_outline,
                  size: 16,
                  color: AppColors.royalIndigo,
                ),
                SizedBox(width: 4),
                Text(
                  '查看詳情',
                  style: TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.w600,
                    color: AppColors.royalIndigo,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// 導航到詳情頁面
  void _navigateToDetailPage(BuildContext context) {
    // 創建一個新的 AstroEventDetailPage 實例，顯示更詳細的信息
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => AstroEventDetailPage(
          event: event,
          natalPerson: natalPerson,
          latitude: latitude,
          longitude: longitude,
        ),
      ),
    );
  }
}

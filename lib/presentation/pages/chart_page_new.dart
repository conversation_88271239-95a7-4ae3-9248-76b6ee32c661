import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../../../data/models/astrology/chart_data.dart';
import '../../../data/models/astrology/chart_type.dart';
import '../../core/utils/logger_utils.dart';
import '../../features/chart/core/chart_display_config.dart';
import '../../features/chart/core/chart_display_controller.dart';
import '../../features/chart/core/chart_display_widget.dart';
import '../../features/chart/models/chart_display_settings.dart';
import '../../features/chart/models/chart_display_theme.dart';
import '../../presentation/viewmodels/chart_viewmodel.dart';
import '../../shared/utils/chart_pdf_generator.dart';
import '../../shared/widgets/common/responsive_wrapper.dart';
import '../../shared/widgets/web_aware_app_bar.dart';
import '../../shared/widgets/web_aware_pop_scope.dart';
import '../themes/app_theme.dart';
import '../viewmodels/recent_charts_viewmodel.dart';
import '../viewmodels/settings_viewmodel.dart';
import '../widgets/dialogs/copy_options_dialog.dart';
import '../widgets/dialogs/theme_copy_dialog.dart';
import 'ai_interpretation_result_page.dart';
import 'ai_interpretation_selection_page.dart';
import 'chart_selection_page.dart';
import 'main/settings_page.dart';

class ChartPageNew extends StatefulWidget {
  final ChartData? chartData;
  final ChartType? chartType;

  const ChartPageNew({super.key, this.chartData, this.chartType});

  @override
  _ChartPageNewState createState() => _ChartPageNewState();
}

class _ChartPageNewState extends State<ChartPageNew> {
  late ChartDisplayController _chartDisplayController;

  @override
  void initState() {
    super.initState();
    _initChartDisplayController();
  }

  void _initChartDisplayController() {
    // 獲取 ChartViewModel 來取得星盤數據
    final chartViewModel = Provider.of<ChartViewModel>(context, listen: false);

    // 創建星盤顯示配置
    final config = _createChartDisplayConfig(chartViewModel.chartData);

    // 初始化控制器
    _chartDisplayController = ChartDisplayController(config: config);
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();

    final chartViewModel = Provider.of<ChartViewModel>(context, listen: false);
    final settingsViewModel =
        Provider.of<SettingsViewModel>(context, listen: false);
    final recentChartsViewModel =
        Provider.of<RecentChartsViewModel>(context, listen: false);

    // 設置全局的 SettingsViewModel
    chartViewModel.setSettingsViewModel(settingsViewModel);

    // 如果提供了 chartType，設置星盤類型
    if (widget.chartType != null) {
      chartViewModel.setChartType(widget.chartType!, context: context);
    }

    // 記錄到最近使用的星盤
    recentChartsViewModel.addOrUpdateRecentChart(chartViewModel.chartData);

    // 更新星盤顯示控制器的數據
    _updateChartDisplayController(chartViewModel.chartData);
  }

  /// 更新星盤顯示控制器
  void _updateChartDisplayController(ChartData chartData) {
    final newConfig = _createChartDisplayConfig(chartData);
    _chartDisplayController.updateConfig(newConfig);
  }

  /// 創建星盤顯示配置
  ChartDisplayConfig _createChartDisplayConfig(ChartData chartData) {
    return ChartDisplayConfig(
      chartData: chartData,
      theme: _createAppTheme(),
      settings: _createChartDisplaySettings(chartData),
      showToolbar: true,
      showTabs: true,
      showInfoPanel: true,
      enableZoom: true,
      enableDrag: true,
      enablePlanetTap: true,
      customActions: _createCustomActions(),
      onPlanetTap: _handlePlanetTap,
      onChartTypeChanged: _handleChartTypeChanged,
    );
  }

  /// 創建符合 App 主題的星盤主題
  ChartDisplayTheme _createAppTheme() {
    return ChartDisplayTheme(
      primaryColor: AppColors.royalIndigo,
      secondaryColor: AppColors.solarAmber,
      backgroundColor: AppColors.classicWhite,
      surfaceColor: Colors.white,
      textColor: AppColors.textDark,
      secondaryTextColor: AppColors.textMedium,
      borderColor: AppColors.indigoLight,
      dividerColor: AppColors.indigoLight.withValues(alpha: 0.5),
      unselectedLabelColor: Colors.white70,
      errorColor: Colors.red,
      successColor: Colors.green,
      warningColor: Colors.orange,
      titleTextStyle: TextStyle(
        fontSize: 20,
        fontWeight: FontWeight.bold,
        color: AppColors.textDark,
      ),
      subtitleTextStyle: TextStyle(
        fontSize: 16,
        fontWeight: FontWeight.w600,
        color: AppColors.textDark,
      ),
      bodyTextStyle: TextStyle(
        fontSize: 14,
        fontWeight: FontWeight.normal,
        color: AppColors.textMedium,
      ),
      labelTextStyle: TextStyle(
        fontSize: 12,
        fontWeight: FontWeight.w500,
        color: AppColors.textMedium,
      ),
      borderRadius: 12.0,
      shadows: [
        BoxShadow(
          color: Colors.black.withValues(alpha: 0.1),
          blurRadius: 8,
          offset: const Offset(0, 2),
        ),
      ],
      padding: const EdgeInsets.all(16.0),
      margin: const EdgeInsets.all(8.0),
    );
  }

  /// 創建星盤顯示設定
  ChartDisplaySettings _createChartDisplaySettings(ChartData chartData) {
    final enabledTabs = <ChartDisplayTabType>[
      ChartDisplayTabType.chart,
    ];

    // 根據星盤類型添加法達盤標籤頁
    if (chartData.chartType == ChartType.firdaria) {
      enabledTabs.add(ChartDisplayTabType.firdaria);
    }

    // 添加其他標籤頁
    enabledTabs.addAll([
      ChartDisplayTabType.planets,
      ChartDisplayTabType.houses,
      ChartDisplayTabType.aspects,
      ChartDisplayTabType.elements,
      ChartDisplayTabType.classical,
    ]);

    return ChartDisplaySettings(
      enabledTabs: enabledTabs,
      chartCanvas: ChartCanvasSettings.defaultSettings(),
      planetList: PlanetListSettings.defaultSettings(),
      houses: HouseSettings.defaultSettings(),
      aspectTable: AspectTableSettings.defaultSettings(),
      elements: ChartElementsSettings.defaultSettings(),
      classical: ClassicalAstrologySettings.defaultSettings(),
      firdaria: FirdariaSettings.defaultSettings(),
      export: ExportSettings.defaultSettings(),
    );
  }

  /// 創建自定義工具列動作
  List<ChartToolbarAction> _createCustomActions() {
    return [
      ChartToolbarAction(
        id: 'settings',
        title: '設定',
        icon: Icons.settings,
        onPressed: _openSettings,
        tooltip: '開啟設定',
      ),
      ChartToolbarAction(
        id: 'chart_type',
        title: '切換星盤',
        icon: Icons.swap_horiz,
        onPressed: _switchChartType,
        tooltip: '切換星盤類型',
      ),

      if (kDebugMode) ...[
        ChartToolbarAction(
          id: 'copy_info',
          title: '複製星盤資訊',
          icon: Icons.content_copy,
          onPressed: _copyChartInfo,
          tooltip: '複製星盤資訊',
        ),
        ChartToolbarAction(
          id: 'copy_theme_info',
          title: '主題星盤資訊',
          icon: Icons.topic,
          onPressed: _copyThemeInfo,
          tooltip: '複製主題星盤資訊',
        ),
        ChartToolbarAction(
          id: 'generate_pdf',
          title: '生成 PDF',
          icon: Icons.picture_as_pdf,
          onPressed: _generatePdf,
          tooltip: '生成 PDF',
        ),
        ChartToolbarAction(
          id: 'send_email',
          title: '發送郵件',
          icon: Icons.email,
          onPressed: _sendEmail,
          tooltip: '發送郵件',
        ),
        // ChartToolbarAction(
        //   id: 'consultation',
        //   title: '占星諮詢',
        //   icon: Icons.analytics,
        //   onPressed: _startConsultationAnalysis,
        //   tooltip: '占星諮詢分析',
        // ),
        ChartToolbarAction(
          id: 'save_image',
          title: '儲存圖片',
          icon: Icons.save_alt,
          onPressed: _saveChartImage,
          tooltip: '儲存星盤圖片',
        ),
        ChartToolbarAction(
          id: 'share_image',
          title: '分享',
          icon: Icons.share,
          onPressed: _shareChartImage,
          tooltip: '分享星盤圖片',
        ),
        ChartToolbarAction(
          id: 'ai_interpretation',
          title: '解讀',
          icon: Icons.psychology,
          onPressed: _startAIInterpretation,
          tooltip: '開始解讀',
        ),
      ],
    ];
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<ChartViewModel>(
      builder: (context, viewModel, _) {
        // 更新星盤顯示控制器的數據
        _updateChartDisplayController(viewModel.chartData);

        return WebAwarePopScope(
          routeName: '/chart',
          child: Scaffold(
            appBar: WebAwareAppBarHelper.simple(
              title: viewModel.getChartTitle(),
            backgroundColor: AppColors.royalIndigo,
            // foregroundColor: Colors.white,
            actions: [
              PopupMenuButton<String>(
                icon: const Icon(Icons.more_vert),
                tooltip: '工具選單',
                onSelected: _handleMenuAction,
                itemBuilder: (context) => _buildMenuItems(),
              ),
            ],
          ),
          body: viewModel.isLoading
              ? const Center(child: CircularProgressIndicator())
              : ResponsivePageWrapper(
                  maxWidth: 1200.0, // 星盤頁面需要較大寬度
                  child: ChartDisplayWidget(
                    config: _chartDisplayController.config,
                    controller: _chartDisplayController,
                  ),
                ),
          // AI 解讀浮動按鈕
          floatingActionButton: FloatingActionButton.extended(
            onPressed: () => _navigateToAIInterpretation(viewModel.chartData),
            backgroundColor: AppColors.solarAmber,
            foregroundColor: Colors.white,
            icon: const Icon(Icons.psychology),
            label: const Text('解讀'),
          ),
          floatingActionButtonLocation: FloatingActionButtonLocation.endFloat,
          ),
        );
      },
    );
  }

  /// 處理選單動作
  void _handleMenuAction(String action) {
    final actionId = action.substring(7); // 移除 'custom_' 前綴
    final customAction = _chartDisplayController
        .getCustomActions()
        .where((a) => a.id == actionId)
        .firstOrNull;
    if (customAction != null && customAction.enabled) {
      customAction.onPressed();
    }
  }

  /// 構建選單項目
  List<PopupMenuEntry<String>> _buildMenuItems() {
    final items = <PopupMenuEntry<String>>[];
    // 自定義動作
    final customActions = _chartDisplayController.getCustomActions();
    if (customActions.isNotEmpty) {
      for (final customAction in customActions) {
        items.add(
          PopupMenuItem<String>(
            value: 'custom_${customAction.id}',
            enabled: customAction.enabled,
            child: Row(
              children: [
                Icon(customAction.icon, size: 20, color: AppColors.royalIndigo),
                const SizedBox(width: 12),
                Text(customAction.title),
              ],
            ),
          ),
        );
      }
    }

    return items;
  }

  /// 處理行星點擊事件
  void _handlePlanetTap(dynamic planet) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('${planet.name} 詳細資訊'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildInfoRow('位置', '${planet.longitude.toStringAsFixed(2)}°'),
            _buildInfoRow('星座', planet.sign),
            _buildInfoRow('宮位', '第${planet.house}宮'),
            if (planet.symbol != null) _buildInfoRow('符號', planet.symbol),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('關閉'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              _startPlanetInterpretation(planet);
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.royalIndigo,
              foregroundColor: Colors.white,
            ),
            child: const Text('解讀'),
          ),
        ],
      ),
    );
  }

  /// 構建資訊行
  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 60,
            child: Text(
              '$label:',
              style: const TextStyle(fontWeight: FontWeight.w500),
            ),
          ),
          Expanded(
            child: Text(value),
          ),
        ],
      ),
    );
  }

  /// 處理星盤類型變更
  void _handleChartTypeChanged(dynamic chartType) async {
    final chartViewModel = Provider.of<ChartViewModel>(context, listen: false);

    try {
      await chartViewModel.setChartType(chartType, context: context);

      // 檢查切換是否真的成功了
      if (mounted) {
        if (chartViewModel.chartType == chartType) {
          // 切換成功，顯示成功訊息
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('已切換到: ${chartType.displayName}'),
              backgroundColor: AppColors.royalIndigo,
              duration: const Duration(seconds: 2),
            ),
          );
        } else {
          // 切換未成功（可能用戶取消了），顯示取消訊息
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('已取消切換到: ${chartType.displayName}'),
              backgroundColor: Colors.grey.shade600,
              duration: const Duration(seconds: 2),
            ),
          );
        }
      }
    } catch (e) {
      // 顯示錯誤訊息
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('切換失敗: $e'),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 3),
          ),
        );
      }
    }
  }

  /// 開始 AI 解讀
  void _startAIInterpretation() {
    final chartViewModel = Provider.of<ChartViewModel>(context, listen: false);
    _navigateToAIInterpretation(chartViewModel.chartData);
  }

  /// 開始行星 AI 解讀
  void _startPlanetInterpretation(dynamic planet) {
    final chartViewModel = Provider.of<ChartViewModel>(context, listen: false);

    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => AIInterpretationResultPage(
          chartData: chartViewModel.chartData,
          interpretationTitle: '${planet.name} 解讀',
          subtitle: '行星詳細分析',
          suggestedQuestions: const [],
          keyPoint: planet.name,
        ),
      ),
    );
  }

  /// 切換星盤類型
  void _switchChartType() async {
    final chartViewModel = Provider.of<ChartViewModel>(context, listen: false);

    try {
      logger.d('開始切換星盤類型');

      final result = await Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => ChartSelectionPage(
            primaryPerson: chartViewModel.primaryPerson,
            secondaryPerson: chartViewModel.secondaryPerson,
            initialChartType: chartViewModel.chartType,
            isChangingChartType: true,
          ),
        ),
      );

      if (result is ChartData && mounted) {
        logger.d('收到新的 ChartData，開始更新星盤：${result.chartType.name}');
        await chartViewModel.updateChartData(result);
        logger.d('星盤更新完成');
      }
    } catch (e) {
      logger.e('切換星盤類型時出錯: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('切換失敗: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  /// 儲存星盤圖片
  void _saveChartImage() async {
    try {
      // 暫時顯示功能開發中的訊息
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('儲存功能開發中...'),
          backgroundColor: Colors.orange,
        ),
      );

      // TODO: 實現星盤圖片儲存功能
      // 需要整合 RepaintBoundary 和 ChartImageService
    } catch (e) {
      logger.e('儲存星盤圖片時出錯: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('儲存失敗: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  /// 分享星盤圖片
  void _shareChartImage() async {
    try {
      // 暫時顯示功能開發中的訊息
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('分享功能開發中...'),
          backgroundColor: Colors.orange,
        ),
      );

      // TODO: 實現星盤圖片分享功能
      // 需要整合 RepaintBoundary 和 ChartImageService
    } catch (e) {
      logger.e('分享星盤圖片時出錯: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('分享失敗: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  /// 複製星盤資訊
  void _copyChartInfo() async {
    final chartViewModel = Provider.of<ChartViewModel>(context, listen: false);

    if (!chartViewModel.isCopying) {
      // 顯示複製選項對話框
      final options = await showCopyOptionsDialog(context);

      // 如果用戶選擇了選項（沒有取消）
      if (options != null && mounted) {
        // 將用戶選擇的選項傳遞給 copyChartInfo 方法
        final success = await chartViewModel.copyChartInfo(options: options);
        if (mounted && success) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('星盤資訊已複製到剪貼板'),
              duration: Duration(seconds: 2),
            ),
          );
        }
      }
    }
  }

  /// 複製主題星盤資訊
  void _copyThemeInfo() async {
    final chartViewModel = Provider.of<ChartViewModel>(context, listen: false);

    if (!chartViewModel.isCopying) {
      // 顯示主題複製對話框
      final result = await showThemeCopyDialog(context);

      // 如果用戶選擇了主題（沒有取消）
      if (result != null && mounted) {
        final success = await chartViewModel.copyThemeChartInfo(
          themeKey: result['theme'],
          themeInfo: result['themeInfo'],
          options: result['copyOptions'],
        );
        if (mounted && success) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('${result['themeInfo'].title}星盤資訊已複製到剪貼板'),
              duration: const Duration(seconds: 2),
            ),
          );
        }
      }
    }
  }

  /// 生成 PDF
  void _generatePdf() async {
    final chartViewModel = Provider.of<ChartViewModel>(context, listen: false);

    if (!chartViewModel.isGeneratingPdf) {
      // 顯示 PDF 選項對話框
      final action = await _showPdfOptionsDialog();
      if (action != null && mounted) {
        // 顯示生成中的提示
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('正在生成 PDF...'),
            duration: Duration(seconds: 30),
          ),
        );

        try {
          final pdfBytes = await chartViewModel.generatePdf();
          if (pdfBytes != null && mounted) {
            // 清除之前的 SnackBar
            ScaffoldMessenger.of(context).hideCurrentSnackBar();

            switch (action) {
              case 'preview':
                await ChartPdfGenerator.previewPdf(
                  context: context,
                  pdfBytes: pdfBytes,
                  title: '${chartViewModel.primaryPerson.name}的星盤數據',
                );
                break;
              case 'share':
                await ChartPdfGenerator.savePdfAndShare(
                  context: context,
                  pdfBytes: pdfBytes,
                  fileName: '${chartViewModel.primaryPerson.name}_星盤數據.pdf',
                );
                break;
            }
          } else if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('無法生成 PDF，請確保星盤數據已計算完成'),
                duration: Duration(seconds: 5),
              ),
            );
          }
        } catch (e) {
          logger.e('生成 PDF 時出錯: $e');
          if (mounted) {
            ScaffoldMessenger.of(context).hideCurrentSnackBar();
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text('生成失敗: $e'),
                backgroundColor: Colors.red,
                duration: const Duration(seconds: 5),
              ),
            );
          }
        }
      }
    }
  }

  /// 發送郵件
  void _sendEmail() async {
    final chartViewModel = Provider.of<ChartViewModel>(context, listen: false);

    if (!chartViewModel.isSendingEmail) {
      // 顯示郵件輸入對話框
      final result = await _showEmailInputDialog();
      if (result != null && mounted) {
        // 顯示發送中的提示
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('正在發送郵件...'),
            duration: Duration(seconds: 30),
          ),
        );

        try {
          final success = await chartViewModel.sendEmail(
            email: result['email'],
            useHtml: result['useHtml'],
          );
          if (mounted) {
            // 清除之前的 SnackBar
            ScaffoldMessenger.of(context).hideCurrentSnackBar();

            if (success) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text('星盤數據已發送至 ${result['email']}'),
                  duration: const Duration(seconds: 3),
                ),
              );
            } else {
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('發送失敗，請檢查網路連接或稍後再試'),
                  backgroundColor: Colors.red,
                  duration: Duration(seconds: 5),
                ),
              );
            }
          }
        } catch (e) {
          logger.e('發送郵件時出錯: $e');
          if (mounted) {
            ScaffoldMessenger.of(context).hideCurrentSnackBar();
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text('發送失敗: $e'),
                backgroundColor: Colors.red,
                duration: const Duration(seconds: 5),
              ),
            );
          }
        }
      }
    }
  }

  /// 顯示 PDF 選項對話框
  Future<String?> _showPdfOptionsDialog() async {
    return showDialog<String>(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('PDF 選項'),
          content: const Text('請選擇要執行的操作：'),
          actions: <Widget>[
            TextButton(
              child: const Text('取消'),
              onPressed: () {
                Navigator.of(context).pop();
              },
            ),
            TextButton(
              child: const Text('預覽'),
              onPressed: () {
                Navigator.of(context).pop('preview');
              },
            ),
            TextButton(
              child: const Text('分享'),
              onPressed: () {
                Navigator.of(context).pop('share');
              },
            ),
          ],
        );
      },
    );
  }

  /// 顯示郵件輸入對話框
  Future<Map<String, dynamic>?> _showEmailInputDialog() async {
    final emailController = TextEditingController();
    bool useHtml = true;

    return showDialog<Map<String, dynamic>>(
      context: context,
      builder: (BuildContext context) {
        return StatefulBuilder(
          builder: (context, setState) {
            return AlertDialog(
              title: const Text('發送星盤數據'),
              content: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text('請輸入接收星盤數據的郵箱地址:'),
                  const SizedBox(height: 16),
                  TextField(
                    controller: emailController,
                    decoration: const InputDecoration(
                      labelText: '郵箱地址',
                      hintText: '<EMAIL>',
                      border: OutlineInputBorder(),
                    ),
                    keyboardType: TextInputType.emailAddress,
                    autofocus: true,
                  ),
                  const SizedBox(height: 16),
                  Row(
                    children: [
                      Checkbox(
                        value: useHtml,
                        onChanged: (value) {
                          setState(() {
                            useHtml = value ?? true;
                          });
                        },
                      ),
                      const Text('使用美化格式 (HTML)'),
                      const Tooltip(
                        message: '使用 HTML 格式可以讓郵件內容更加美觀，但某些郵件客戶端可能不支持',
                        child: Icon(Icons.info_outline, size: 16),
                      ),
                    ],
                  ),
                ],
              ),
              actions: [
                TextButton(
                  onPressed: () => Navigator.of(context).pop(),
                  child: const Text('取消'),
                ),
                ElevatedButton(
                  onPressed: () {
                    if (emailController.text.trim().isEmpty) {
                      ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(content: Text('請輸入郵箱地址')),
                      );
                      return;
                    }
                    Navigator.of(context).pop({
                      'email': emailController.text.trim(),
                      'useHtml': useHtml,
                    });
                  },
                  child: const Text('發送'),
                ),
              ],
            );
          },
        );
      },
    );
  }

  /// 開啟設定
  void _openSettings() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const SettingsPage(),
      ),
    );
  }

  /// 導航到 AI 解讀頁面
  void _navigateToAIInterpretation(ChartData chartData) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => AIInterpretationSelectionPage(
          chartData: chartData,
        ),
      ),
    );
  }

  @override
  void dispose() {
    _chartDisplayController.dispose();
    super.dispose();
  }
}

import 'package:flutter/material.dart';

import '../../../astreal.dart';
import '../../../data/services/api/user_profile_service.dart';
import '../../../shared/widgets/unified_card.dart';
import 'celebrity_management_page.dart';
import 'system_announcement_management_page.dart';
import 'user_list_page.dart';

/// 管理後台頁面
class AdminDashboardPage extends StatefulWidget {
  const AdminDashboardPage({super.key});

  @override
  State<AdminDashboardPage> createState() => _AdminDashboardPageState();
}

class _AdminDashboardPageState extends State<AdminDashboardPage> {
  Map<String, int>? _userStats;
  bool _isLoadingStats = true;

  @override
  void initState() {
    super.initState();
    _loadUserStats();
  }

  /// 載入用戶統計資料
  Future<void> _loadUserStats() async {
    try {
      final stats = await UserProfileService.getUserStats();
      if (mounted) {
        setState(() {
          _userStats = stats;
          _isLoadingStats = false;
        });
      }
    } catch (e) {
      logger.e('載入統計資料失敗: $e');
      if (mounted) {
        setState(() {
          _isLoadingStats = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text(
          '管理後台',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: AppColors.textDark,
          ),
        ),
        backgroundColor: AppColors.pastelSkyBlue,
        elevation: 0,
        iconTheme: const IconThemeData(color: AppColors.textDark),
      ),
      backgroundColor: AppColors.veryLightGray,
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 歡迎卡片
            _buildWelcomeCard(),
            const SizedBox(height: 20),

            // 系統統計
            _buildSystemStatsSection(),
            const SizedBox(height: 20),

            // 用戶管理
            _buildUserManagementSection(),
            const SizedBox(height: 20),

            // 內容管理
            _buildContentManagementSection(),
            const SizedBox(height: 20),

            // 系統設定
            _buildSystemSettingsSection(),
          ],
        ),
      ),
    );
  }

  /// 構建歡迎卡片
  Widget _buildWelcomeCard() {
    return UnifiedCard(
      padding: const EdgeInsets.all(20),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: AppColors.royalIndigo.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(16),
            ),
            child: const Icon(
              Icons.admin_panel_settings,
              color: AppColors.royalIndigo,
              size: 32,
            ),
          ),
          const SizedBox(width: 16),
          const Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  '管理後台',
                  style: TextStyle(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                    color: AppColors.textDark,
                  ),
                ),
                SizedBox(height: 4),
                Text(
                  '系統管理與監控中心',
                  style: TextStyle(
                    fontSize: 14,
                    color: AppColors.textSecondary,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// 構建系統統計區段
  Widget _buildSystemStatsSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          '系統統計',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: AppColors.textDark,
          ),
        ),
        const SizedBox(height: 12),
        if (_isLoadingStats)
          const Center(
            child: Padding(
              padding: EdgeInsets.all(32),
              child: CircularProgressIndicator(),
            ),
          )
        else ...[
          Row(
            children: [
              Expanded(
                child: _buildStatCard(
                  title: '總用戶數',
                  value: _userStats?['totalUsers']?.toString() ?? '0',
                  icon: Icons.people,
                  color: AppColors.royalIndigo,
                  onTap: () => _navigateToUserListWithFilter(UserTypeFilter.all),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildStatCard(
                  title: '今日新增',
                  value: _userStats?['todayUsers']?.toString() ?? '0',
                  icon: Icons.trending_up,
                  color: AppColors.successGreen,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              Expanded(
                child: _buildStatCard(
                  title: '已驗證用戶',
                  value: _userStats?['verifiedUsers']?.toString() ?? '0',
                  icon: Icons.verified_user,
                  color: AppColors.solarAmber,
                  onTap: () => _navigateToUserListWithFilter(UserTypeFilter.verified),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildStatCard(
                  title: '管理者',
                  value: _userStats?['adminUsers']?.toString() ?? '0',
                  icon: Icons.admin_panel_settings,
                  color: AppColors.cosmicPurple,
                  onTap: () => _navigateToUserListWithFilter(UserTypeFilter.admin),
                ),
              ),
            ],
          ),
        ],
      ],
    );
  }

  /// 構建統計卡片
  Widget _buildStatCard({
    required String title,
    required String value,
    required IconData icon,
    required Color color,
    VoidCallback? onTap,
  }) {
    return UnifiedCard(
      onTap: onTap,
      child: Column(
        children: [
          Icon(
            icon,
            color: color,
            size: 32,
          ),
          const SizedBox(height: 8),
          Text(
            value,
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            title,
            style: const TextStyle(
              fontSize: 12,
              color: AppColors.textSecondary,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  /// 構建用戶管理區段
  Widget _buildUserManagementSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          '用戶管理',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: AppColors.textDark,
          ),
        ),
        const SizedBox(height: 12),
        UnifiedFeatureCard(
          title: '用戶列表',
          subtitle: '查看和管理所有用戶',
          icon: Icons.people_outline,
          color: AppColors.royalIndigo,
          onTap: () {
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => const UserListPage(),
              ),
            );
          },
        ),
        const SizedBox(height: 8),
        UnifiedFeatureCard(
          title: '權限管理',
          subtitle: '管理用戶權限和角色',
          icon: Icons.security,
          color: AppColors.warning,
          onTap: () {
            // TODO: 導航到權限管理頁面
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(content: Text('權限管理功能開發中')),
            );
          },
        ),
      ],
    );
  }

  /// 構建內容管理區段
  Widget _buildContentManagementSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          '內容管理',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: AppColors.textDark,
          ),
        ),
        const SizedBox(height: 12),
        UnifiedFeatureCard(
          title: '名人資料管理',
          subtitle: '管理名人出生資料和解讀',
          icon: Icons.star_outline,
          color: AppColors.solarAmber,
          onTap: () {
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => const CelebrityManagementPage(),
              ),
            );
          },
        ),
        const SizedBox(height: 8),
        UnifiedFeatureCard(
          title: '系統公告',
          subtitle: '發布和管理系統公告',
          icon: Icons.announcement_outlined,
          color: AppColors.info,
          onTap: () {
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => const SystemAnnouncementManagementPage(),
              ),
            );
          },
        ),
      ],
    );
  }

  /// 構建系統設定區段
  Widget _buildSystemSettingsSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          '系統設定',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: AppColors.textDark,
          ),
        ),
        const SizedBox(height: 12),
        UnifiedFeatureCard(
          title: '應用程式設定',
          subtitle: '管理應用程式全域設定',
          icon: Icons.settings_outlined,
          color: AppColors.textSecondary,
          onTap: () {
            // TODO: 導航到應用程式設定頁面
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(content: Text('應用程式設定功能開發中')),
            );
          },
        ),
        const SizedBox(height: 8),
        UnifiedFeatureCard(
          title: '日誌管理',
          subtitle: '查看系統日誌和錯誤報告',
          icon: Icons.bug_report_outlined,
          color: AppColors.error,
          onTap: () {
            // TODO: 導航到日誌管理頁面
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(content: Text('日誌管理功能開發中')),
            );
          },
        ),
      ],
    );
  }

  /// 導航到用戶列表頁面並應用過濾
  void _navigateToUserListWithFilter(UserTypeFilter filter) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => UserListPage(initialFilter: filter),
      ),
    );
  }
}

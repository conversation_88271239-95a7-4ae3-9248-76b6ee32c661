import 'package:flutter/material.dart';
import 'package:intl/intl.dart';

import '../../../astreal.dart';
import '../../../data/services/api/account_deletion_service.dart';
import '../../../data/services/api/user_profile_service.dart';
import '../../../shared/utils/user_preferences.dart';
import '../../../shared/widgets/unified_card.dart';
import 'user_edit_page.dart';

/// 用戶排序選項
enum UserSortOption {
  displayName,
  email,
  createdAt,
  updatedAt,
  lastLoginAt,
  loginCount,
  interpretationCredits;

  String get label {
    switch (this) {
      case UserSortOption.displayName:
        return '顯示名稱';
      case UserSortOption.email:
        return '電子郵件';
      case UserSortOption.createdAt:
        return '註冊時間';
      case UserSortOption.updatedAt:
        return '最後更新';
      case UserSortOption.lastLoginAt:
        return '最後登入';
      case UserSortOption.loginCount:
        return '登入次數';
      case UserSortOption.interpretationCredits:
        return '解讀次數';
    }
  }
}

/// 排序方向
enum SortDirection {
  ascending,
  descending;

  String get label {
    switch (this) {
      case SortDirection.ascending:
        return '升序';
      case SortDirection.descending:
        return '降序';
    }
  }
}

/// 用戶類型過濾選項
enum UserTypeFilter {
  all,
  admin,
  regular,
  anonymous,
  verified;

  String get label {
    switch (this) {
      case UserTypeFilter.all:
        return '全部用戶';
      case UserTypeFilter.admin:
        return '管理者';
      case UserTypeFilter.regular:
        return '一般用戶';
      case UserTypeFilter.anonymous:
        return '匿名用戶';
      case UserTypeFilter.verified:
        return '已驗證用戶';
    }
  }

  IconData get icon {
    switch (this) {
      case UserTypeFilter.all:
        return Icons.people;
      case UserTypeFilter.admin:
        return Icons.admin_panel_settings;
      case UserTypeFilter.regular:
        return Icons.person;
      case UserTypeFilter.anonymous:
        return Icons.person_outline;
      case UserTypeFilter.verified:
        return Icons.verified_user;
    }
  }
}

/// 用戶列表頁面 - 顯示 Firestore 中的用戶資料
class UserListPage extends StatefulWidget {
  /// 初始過濾類型
  final UserTypeFilter? initialFilter;

  const UserListPage({
    super.key,
    this.initialFilter,
  });

  @override
  State<UserListPage> createState() => _UserListPageState();
}

class _UserListPageState extends State<UserListPage> {
  final TextEditingController _searchController = TextEditingController();

  String _searchQuery = '';
  bool _isLoading = true;
  List<UserProfile> _users = [];
  List<UserProfile> _filteredUsers = [];

  // 排序相關狀態
  UserSortOption _currentSortOption = UserSortOption.createdAt;
  SortDirection _currentSortDirection = SortDirection.descending;

  // 過濾相關狀態
  UserTypeFilter _currentFilter = UserTypeFilter.all;

  @override
  void initState() {
    super.initState();
    // 設置初始過濾類型
    if (widget.initialFilter != null) {
      _currentFilter = widget.initialFilter!;
    }
    _loadSortPreferences();
    _loadUsers();
    _searchController.addListener(_onSearchChanged);
  }

  @override
  void dispose() {
    _searchController.removeListener(_onSearchChanged);
    _searchController.dispose();
    super.dispose();
  }

  /// 載入排序偏好設定
  Future<void> _loadSortPreferences() async {
    try {
      // 載入排序選項
      final sortOptionIndex = await UserPreferences.getUserListSortOption();
      if (sortOptionIndex != null &&
          sortOptionIndex >= 0 &&
          sortOptionIndex < UserSortOption.values.length) {
        _currentSortOption = UserSortOption.values[sortOptionIndex];
      }

      // 載入排序方向
      final sortDirectionIndex = await UserPreferences.getUserListSortDirection();
      if (sortDirectionIndex != null &&
          sortDirectionIndex >= 0 &&
          sortDirectionIndex < SortDirection.values.length) {
        _currentSortDirection = SortDirection.values[sortDirectionIndex];
      }

      logger.d('載入排序偏好: ${_currentSortOption.label} - ${_currentSortDirection.label}');
    } catch (e) {
      logger.e('載入排序偏好失敗: $e');
      // 使用預設值
      _currentSortOption = UserSortOption.createdAt;
      _currentSortDirection = SortDirection.descending;
    }
  }

  /// 保存排序偏好設定
  Future<void> _saveSortPreferences() async {
    try {
      await UserPreferences.saveUserListSortPreferences(
        _currentSortOption.index,
        _currentSortDirection.index,
      );

      logger.d('保存排序偏好: ${_currentSortOption.label} - ${_currentSortDirection.label}');
    } catch (e) {
      logger.e('保存排序偏好失敗: $e');
    }
  }

  /// 載入用戶資料
  Future<void> _loadUsers() async {
    try {
      setState(() {
        _isLoading = true;
      });

      final users = await UserProfileService.getAllUsers();

      setState(() {
        _users = users;
        _isLoading = false;
      });

      // 應用初始過濾器和排序
      _filterAndSortUsers();

      logger.i('載入了 ${users.length} 個用戶');

      // 除錯：檢查管理者用戶
      final adminUsers = users.where((user) => user.isAdmin == true).toList();
      logger.d('找到 ${adminUsers.length} 個管理者用戶:');
      for (final admin in adminUsers) {
        logger.d('  - ${admin.displayName} (${admin.userId}): isAdmin=${admin.isAdmin}');
      }
    } catch (e) {
      logger.e('載入用戶列表失敗: $e');
      setState(() {
        _isLoading = false;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('載入用戶列表失敗: $e'),
            backgroundColor: AppColors.error,
          ),
        );
      }
    }
  }

  /// 搜尋變更處理
  void _onSearchChanged() {
    setState(() {
      _searchQuery = _searchController.text.toLowerCase();
      _filterAndSortUsers();
    });
  }

  /// 篩選和排序用戶
  void _filterAndSortUsers() {
    _filterUsers();
    _sortUsers();
  }

  /// 篩選用戶
  void _filterUsers() {
    // 首先根據用戶類型過濾
    List<UserProfile> typeFilteredUsers = _users.where((user) {
      switch (_currentFilter) {
        case UserTypeFilter.all:
          return true;
        case UserTypeFilter.admin:
          final isAdmin = user.isAdmin == true;
          logger.d('用戶 ${user.displayName} (${user.userId}) isAdmin: ${user.isAdmin} -> 過濾結果: $isAdmin');
          return isAdmin;
        case UserTypeFilter.regular:
          return user.isAdmin != true && !user.isAnonymous;
        case UserTypeFilter.anonymous:
          return user.isAnonymous;
        case UserTypeFilter.verified:
          return user.emailVerified == true;
      }
    }).toList();

    // 然後根據搜尋條件過濾
    if (_searchQuery.isEmpty) {
      _filteredUsers = typeFilteredUsers;
    } else {
      _filteredUsers = typeFilteredUsers.where((user) {
        final email = user.email?.toLowerCase() ?? '';
        final displayName = user.displayName.toLowerCase();
        final uid = user.userId.toLowerCase();

        return email.contains(_searchQuery) ||
               displayName.contains(_searchQuery) ||
               uid.contains(_searchQuery);
      }).toList();
    }
  }

  /// 更改用戶類型過濾
  void _changeUserTypeFilter(UserTypeFilter filter) {
    setState(() {
      _currentFilter = filter;
      _filterAndSortUsers();
    });
  }

  /// 排序用戶
  void _sortUsers() {
    _filteredUsers.sort((a, b) {
      int comparison = 0;

      switch (_currentSortOption) {
        case UserSortOption.displayName:
          comparison = a.displayName.compareTo(b.displayName);
          break;
        case UserSortOption.email:
          final emailA = a.email ?? '';
          final emailB = b.email ?? '';
          comparison = emailA.compareTo(emailB);
          break;
        case UserSortOption.createdAt:
          comparison = a.createdAt.compareTo(b.createdAt);
          break;
        case UserSortOption.updatedAt:
          comparison = a.updatedAt.compareTo(b.updatedAt);
          break;
        case UserSortOption.lastLoginAt:
          comparison = a.lastLoginAt.compareTo(b.lastLoginAt);
          break;
        case UserSortOption.loginCount:
          comparison = a.loginCount.compareTo(b.loginCount);
          break;
        case UserSortOption.interpretationCredits:
          comparison = a.interpretationCredits.compareTo(b.interpretationCredits);
          break;
      }

      return _currentSortDirection == SortDirection.ascending ? comparison : -comparison;
    });
  }

  /// 更改排序選項
  void _changeSortOption(UserSortOption option) {
    setState(() {
      if (_currentSortOption == option) {
        // 如果選擇相同的排序選項，切換排序方向
        _currentSortDirection = _currentSortDirection == SortDirection.ascending
            ? SortDirection.descending
            : SortDirection.ascending;
      } else {
        // 如果選擇不同的排序選項，使用預設方向
        _currentSortOption = option;
        _currentSortDirection = _getDefaultSortDirection(option);
      }
      _sortUsers();
    });

    // 保存排序偏好
    _saveSortPreferences();
  }

  /// 獲取預設排序方向
  SortDirection _getDefaultSortDirection(UserSortOption option) {
    switch (option) {
      case UserSortOption.displayName:
      case UserSortOption.email:
        return SortDirection.ascending;
      case UserSortOption.createdAt:
      case UserSortOption.updatedAt:
      case UserSortOption.lastLoginAt:
        return SortDirection.descending;
      case UserSortOption.loginCount:
      case UserSortOption.interpretationCredits:
        return SortDirection.descending;
    }
  }

  /// 刷新用戶列表
  Future<void> _refreshUsers() async {
    await _loadUsers();
  }

  /// 導航到用戶編輯頁面
  void _editUser(UserProfile userProfile) async {
    final result = await Navigator.push<bool>(
      context,
      MaterialPageRoute(
        builder: (context) => UserEditPage(userData: userProfile),
      ),
    );

    // 如果編輯成功，刷新列表
    if (result == true) {
      _refreshUsers();
    }
  }

  /// 顯示過濾選項對話框
  void _showFilterOptions() {
    showModalBottomSheet<void>(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (BuildContext context) {
        return Container(
          decoration: const BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // 標題欄
              Container(
                padding: const EdgeInsets.all(16),
                decoration: const BoxDecoration(
                  border: Border(
                    bottom: BorderSide(color: Colors.grey, width: 0.2),
                  ),
                ),
                child: Row(
                  children: [
                    const Text(
                      '用戶類型過濾',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: AppColors.textDark,
                      ),
                    ),
                    const Spacer(),
                    IconButton(
                      icon: const Icon(Icons.close),
                      onPressed: () => Navigator.pop(context),
                      padding: EdgeInsets.zero,
                      constraints: const BoxConstraints(),
                    ),
                  ],
                ),
              ),

              // 過濾選項列表
              Flexible(
                child: ListView.builder(
                  shrinkWrap: true,
                  itemCount: UserTypeFilter.values.length,
                  itemBuilder: (context, index) {
                    final filter = UserTypeFilter.values[index];
                    final isSelected = _currentFilter == filter;

                    return ListTile(
                      leading: Icon(
                        filter.icon,
                        color: isSelected ? AppColors.royalIndigo : AppColors.textSecondary,
                      ),
                      title: Text(
                        filter.label,
                        style: TextStyle(
                          color: isSelected ? AppColors.royalIndigo : AppColors.textDark,
                          fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                        ),
                      ),
                      trailing: isSelected
                          ? const Icon(
                              Icons.check,
                              color: AppColors.royalIndigo,
                            )
                          : null,
                      onTap: () {
                        _changeUserTypeFilter(filter);
                        Navigator.pop(context);
                      },
                    );
                  },
                ),
              ),

              const SizedBox(height: 16),
            ],
          ),
        );
      },
    );
  }

  /// 顯示排序選項對話框
  void _showSortOptions() {
    showModalBottomSheet<void>(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (BuildContext context) {
        return Container(
          decoration: const BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // 標題欄
              Container(
                padding: const EdgeInsets.all(16),
                decoration: const BoxDecoration(
                  border: Border(
                    bottom: BorderSide(color: Colors.grey, width: 0.2),
                  ),
                ),
                child: Row(
                  children: [
                    const Text(
                      '排序選項',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: AppColors.textDark,
                      ),
                    ),
                    const Spacer(),
                    IconButton(
                      icon: const Icon(Icons.close),
                      onPressed: () => Navigator.pop(context),
                      padding: EdgeInsets.zero,
                      constraints: const BoxConstraints(),
                    ),
                  ],
                ),
              ),

              // 排序選項列表
              Flexible(
                child: ListView.builder(
                  shrinkWrap: true,
                  itemCount: UserSortOption.values.length,
                  itemBuilder: (context, index) {
                    final option = UserSortOption.values[index];
                    final isSelected = _currentSortOption == option;

                    return ListTile(
                      leading: Icon(
                        _getSortOptionIcon(option),
                        color: isSelected ? AppColors.royalIndigo : AppColors.textSecondary,
                      ),
                      title: Text(
                        option.label,
                        style: TextStyle(
                          color: isSelected ? AppColors.royalIndigo : AppColors.textDark,
                          fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                        ),
                      ),
                      trailing: isSelected
                          ? Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Icon(
                                  _currentSortDirection == SortDirection.ascending
                                      ? Icons.arrow_upward
                                      : Icons.arrow_downward,
                                  color: AppColors.royalIndigo,
                                  size: 20,
                                ),
                                const SizedBox(width: 8),
                                const Icon(
                                  Icons.check,
                                  color: AppColors.royalIndigo,
                                ),
                              ],
                            )
                          : null,
                      onTap: () {
                        _changeSortOption(option);
                        Navigator.pop(context);
                      },
                    );
                  },
                ),
              ),

              const SizedBox(height: 16),
            ],
          ),
        );
      },
    );
  }

  /// 獲取排序選項圖標
  IconData _getSortOptionIcon(UserSortOption option) {
    switch (option) {
      case UserSortOption.displayName:
        return Icons.person;
      case UserSortOption.email:
        return Icons.email;
      case UserSortOption.createdAt:
        return Icons.person_add;
      case UserSortOption.updatedAt:
        return Icons.update;
      case UserSortOption.lastLoginAt:
        return Icons.login;
      case UserSortOption.loginCount:
        return Icons.login_outlined;
      case UserSortOption.interpretationCredits:
        return Icons.stars;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text(
          '用戶列表',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: AppColors.textDark,
          ),
        ),
        backgroundColor: AppColors.pastelSkyBlue,
        elevation: 0,
        iconTheme: const IconThemeData(color: AppColors.textDark),
        actions: [
          IconButton(
            icon: const Icon(Icons.filter_list),
            onPressed: _showFilterOptions,
            tooltip: '過濾',
          ),
          IconButton(
            icon: const Icon(Icons.sort),
            onPressed: _showSortOptions,
            tooltip: '排序',
          ),
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _refreshUsers,
            tooltip: '刷新',
          ),
        ],
      ),
      backgroundColor: AppColors.scaffoldBackground,
      body: Column(
        children: [
          // 搜尋欄
          _buildSearchBar(),
          
          // 統計資訊
          _buildStatsBar(),
          
          // 用戶列表
          Expanded(
            child: _isLoading
                ? const Center(child: CircularProgressIndicator())
                : _filteredUsers.isEmpty
                    ? _buildEmptyState()
                    : _buildUserList(),
          ),
        ],
      ),
    );
  }

  /// 構建搜尋欄
  Widget _buildSearchBar() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: TextField(
        controller: _searchController,
        decoration: InputDecoration(
          hintText: '搜尋用戶 (Email、名稱、UID)',
          prefixIcon: const Icon(Icons.search),
          suffixIcon: _searchQuery.isNotEmpty
              ? IconButton(
                  icon: const Icon(Icons.clear),
                  onPressed: () {
                    _searchController.clear();
                  },
                )
              : null,
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
            borderSide: BorderSide(color: Colors.grey[300]!),
          ),
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
            borderSide: const BorderSide(color: AppColors.royalIndigo),
          ),
        ),
      ),
    );
  }

  /// 構建統計欄
  Widget _buildStatsBar() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Column(
        children: [
          Row(
            children: [
              Text(
                '總計: ${_users.length} 個用戶',
                style: const TextStyle(
                  fontSize: 14,
                  color: AppColors.textSecondary,
                ),
              ),
              if (_currentFilter != UserTypeFilter.all || _searchQuery.isNotEmpty) ...[
                const SizedBox(width: 16),
                Text(
                  '顯示: ${_filteredUsers.length} 個',
                  style: const TextStyle(
                    fontSize: 14,
                    color: AppColors.royalIndigo,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
              const Spacer(),
              // 過濾狀態顯示
              if (_currentFilter != UserTypeFilter.all) ...[
                Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(
                      _currentFilter.icon,
                      size: 16,
                      color: AppColors.royalIndigo,
                    ),
                    const SizedBox(width: 4),
                    Text(
                      _currentFilter.label,
                      style: const TextStyle(
                        fontSize: 12,
                        color: AppColors.royalIndigo,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
                const SizedBox(width: 16),
              ],
              // 排序狀態顯示
              Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(
                    _currentSortDirection == SortDirection.ascending
                        ? Icons.arrow_upward
                        : Icons.arrow_downward,
                    size: 16,
                    color: AppColors.textSecondary,
                  ),
                  const SizedBox(width: 4),
                  Text(
                    _currentSortOption.label,
                    style: const TextStyle(
                      fontSize: 12,
                      color: AppColors.textSecondary,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// 構建空狀態
  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            _searchQuery.isNotEmpty ? Icons.search_off : Icons.people_outline,
            size: 64,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            _searchQuery.isNotEmpty ? '沒有找到符合條件的用戶' : '暫無用戶資料',
            style: TextStyle(
              fontSize: 16,
              color: Colors.grey[600],
            ),
          ),
          if (_searchQuery.isNotEmpty) ...[
            const SizedBox(height: 8),
            TextButton(
              onPressed: () {
                _searchController.clear();
              },
              child: const Text('清除搜尋條件'),
            ),
          ],
        ],
      ),
    );
  }

  /// 構建用戶列表
  Widget _buildUserList() {
    return RefreshIndicator(
      onRefresh: _refreshUsers,
      child: ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: _filteredUsers.length,
        itemBuilder: (context, index) {
          final user = _filteredUsers[index];
          return _buildUserCard(user);
        },
      ),
    );
  }

  /// 構建用戶卡片
  Widget _buildUserCard(UserProfile userProfile) {
    final email = userProfile.email ?? '未設置';
    final displayName = userProfile.displayName;
    final uid = userProfile.userId;
    String createdAt = DateFormat('yyyy-MM-dd HH:mm:ss').format(userProfile.createdAt);
    String lastLoginAt = DateFormat('yyyy-MM-dd HH:mm:ss').format(userProfile.lastLoginAt);
    final isEmailVerified = userProfile.emailVerified ?? false;
    final isAnonymous = userProfile.isAnonymous;
    String updatedAt = DateFormat('yyyy-MM-dd HH:mm:ss').format(userProfile.updatedAt);

    return UnifiedCard(
      margin: const EdgeInsets.only(bottom: 12),
      onTap: () => _editUser(userProfile),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 用戶基本資訊
          Row(
            children: [
              CircleAvatar(
                radius: 24,
                backgroundColor: AppColors.royalIndigo.withValues(alpha: 0.1),
                child: Text(
                  displayName.isNotEmpty ? displayName[0].toUpperCase() : 'U',
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: AppColors.royalIndigo,
                  ),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      displayName,
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: AppColors.textDark,
                      ),
                    ),
                    const SizedBox(height: 2),
                    Text(
                      email,
                      style: const TextStyle(
                        fontSize: 14,
                        color: AppColors.textSecondary,
                      ),
                    ),
                  ],
                ),
              ),
              // 狀態標籤
              Column(
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  if (userProfile.isAdmin == true)
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                      decoration: BoxDecoration(
                        color: AppColors.cosmicPurple,
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: const Text(
                        '管理者',
                        style: TextStyle(
                          fontSize: 10,
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  if (isEmailVerified) ...[
                    if (userProfile.isAdmin == true) const SizedBox(height: 4),
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                      decoration: BoxDecoration(
                        color: AppColors.successGreen,
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: const Text(
                        '已驗證',
                        style: TextStyle(
                          fontSize: 10,
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ],
                  if (isAnonymous) ...[
                    if (userProfile.isAdmin == true || isEmailVerified) const SizedBox(height: 4),
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                      decoration: BoxDecoration(
                        color: AppColors.warning,
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: const Text(
                        '匿名',
                        style: TextStyle(
                          fontSize: 10,
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ],
                ],
              ),
            ],
          ),
          
          const SizedBox(height: 12),
          
          // 詳細資訊
          _buildInfoRow('UID', uid),
          _buildInfoRow('註冊時間', createdAt),
          _buildInfoRow('最後更新', updatedAt),
          _buildInfoRow('最後登入', lastLoginAt),
          _buildInfoRow('登入次數', '${userProfile.loginCount} 次'),
          _buildInfoRow('解讀次數', '${userProfile.interpretationCredits} 次'),

          const SizedBox(height: 12),

          // 操作按鈕
          Row(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              TextButton.icon(
                onPressed: () => _editUser(userProfile),
                icon: const Icon(Icons.edit, size: 16),
                label: const Text('編輯'),
                style: TextButton.styleFrom(
                  foregroundColor: AppColors.royalIndigo,
                  padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                ),
              ),
              const SizedBox(width: 8),
              TextButton.icon(
                onPressed: () => _deleteUserAccount(userProfile),
                icon: const Icon(Icons.delete_forever, size: 16),
                label: const Text('刪除'),
                style: TextButton.styleFrom(
                  foregroundColor: Colors.red,
                  padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// 構建資訊行
  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 2),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 80,
            child: Text(
              label,
              style: const TextStyle(
                fontSize: 12,
                color: AppColors.textSecondary,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: const TextStyle(
                fontSize: 12,
                color: AppColors.textDark,
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 刪除用戶帳戶
  Future<void> _deleteUserAccount(UserProfile userProfile) async {
    // 第一次確認
    final firstConfirmed = await _showDeleteUserWarning(userProfile);
    if (!firstConfirmed) return;

    // 第二次確認（更詳細的警告）
    final secondConfirmed = await _showFinalDeleteUserConfirmation(userProfile);
    if (!secondConfirmed) return;

    // 顯示進度對話框
    if (!mounted) return;
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const CircularProgressIndicator(),
            const SizedBox(height: 16),
            Text('正在刪除用戶 ${userProfile.displayName} 的帳戶...'),
            const SizedBox(height: 8),
            const Text(
              '這可能需要一些時間，請勿關閉應用程式',
              style: TextStyle(fontSize: 12, color: Colors.grey),
            ),
          ],
        ),
      ),
    );

    try {
      // 使用帳戶刪除服務
      await AccountDeletionService.deleteUserAccountById(userProfile.userId);

      // 關閉進度對話框
      if (mounted) {
        Navigator.of(context).pop();

        // 顯示成功訊息
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('用戶 ${userProfile.displayName} 的帳戶已完全刪除'),
            backgroundColor: AppColors.successGreen,
          ),
        );

        // 重新載入用戶列表
        _loadUsers();
      }
    } catch (e) {
      // 關閉進度對話框
      if (mounted) Navigator.of(context).pop();

      logger.e('刪除用戶帳戶失敗: $e');
      if (mounted) {
        _showDeleteUserErrorDialog(userProfile, e.toString());
      }
    }
  }

  /// 顯示刪除用戶警告
  Future<bool> _showDeleteUserWarning(UserProfile userProfile) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Row(
          children: [
            Icon(Icons.warning, color: Colors.orange),
            SizedBox(width: 8),
            Text('刪除用戶帳戶'),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('您即將刪除用戶：${userProfile.displayName}'),
            const SizedBox(height: 8),
            const Text('此操作將會：'),
            const SizedBox(height: 8),
            const Text('• 永久刪除用戶的所有資料'),
            const Text('• 刪除用戶的出生資料'),
            const Text('• 刪除用戶的解讀記錄'),
            const Text('• 刪除用戶的雲端檔案'),
            const SizedBox(height: 12),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.red.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.red.withValues(alpha: 0.3)),
              ),
              child: const Row(
                children: [
                  Icon(Icons.warning, color: Colors.red, size: 20),
                  SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      '此操作無法復原！',
                      style: TextStyle(
                        color: Colors.red,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('取消'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.orange,
              foregroundColor: Colors.white,
            ),
            child: const Text('繼續'),
          ),
        ],
      ),
    );
    return confirmed ?? false;
  }

  /// 顯示最終刪除確認
  Future<bool> _showFinalDeleteUserConfirmation(UserProfile userProfile) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Row(
          children: [
            Icon(Icons.delete_forever, color: Colors.red),
            SizedBox(width: 8),
            Text('最終確認'),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              '這是最後一次確認。',
              style: TextStyle(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 12),
            Text('用戶：${userProfile.displayName}'),
            Text('電子郵件：${userProfile.email ?? "未設置"}'),
            Text('UID：${userProfile.userId}'),
            const SizedBox(height: 12),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.red.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.red.withValues(alpha: 0.3)),
              ),
              child: const Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(Icons.error, color: Colors.red, size: 20),
                      SizedBox(width: 8),
                      Text(
                        '危險操作',
                        style: TextStyle(
                          color: Colors.red,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                  SizedBox(height: 8),
                  Text(
                    '用戶的所有資料將被永久刪除，包括：\n'
                    '• 個人資料和設定\n'
                    '• 出生資料和星盤\n'
                    '• 解讀記錄和歷史\n'
                    '• 雲端備份檔案\n'
                    '• Firebase 認證帳戶',
                    style: TextStyle(color: Colors.red),
                  ),
                ],
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('取消'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
            ),
            child: const Text('確認刪除'),
          ),
        ],
      ),
    );
    return confirmed ?? false;
  }

  /// 顯示刪除錯誤對話框
  void _showDeleteUserErrorDialog(UserProfile userProfile, String error) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Row(
          children: [
            Icon(Icons.error, color: Colors.red),
            SizedBox(width: 8),
            Text('刪除失敗'),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('刪除用戶 ${userProfile.displayName} 時發生錯誤：'),
            const SizedBox(height: 8),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.grey.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Text(
                error,
                style: const TextStyle(
                  fontFamily: 'monospace',
                  fontSize: 12,
                ),
              ),
            ),
            const SizedBox(height: 12),
            const Text(
              '請檢查網路連接或聯繫技術支援。',
              style: TextStyle(color: Colors.grey),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('確定'),
          ),
        ],
      ),
    );
  }
}

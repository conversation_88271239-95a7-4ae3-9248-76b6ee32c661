import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../../core/utils/logger_utils.dart';
import '../../data/models/astrology/chart_data.dart';
import '../../data/models/astrology/chart_settings.dart';
import '../../data/models/astrology/chart_type.dart';
import '../../data/models/user/birth_data.dart';
import '../../features/astrology/astrology_service.dart';
import '../../features/astrology/models/chart_filter.dart';
import '../../presentation/viewmodels/chart_filter_viewmodel.dart';
import '../../presentation/viewmodels/chart_viewmodel.dart';
import '../../presentation/widgets/chart_filter_widget.dart';
import '../../shared/widgets/common/responsive_wrapper.dart';
import '../themes/app_theme.dart';
import '../themes/theme_provider.dart';
import 'birth_data_form_page.dart';
import 'chart_page_new.dart';


/// 星盤篩選器頁面
///
/// 提供完整的篩選器管理功能，包括：
/// - 創建和編輯篩選器
/// - 應用篩選器到星盤資料
/// - 查看篩選結果
/// - 保存和載入篩選器
/// - 內部處理星盤計算邏輯
class ChartFilterPage extends StatefulWidget {
  // 舊版本參數（向後兼容）
  final List<ChartData>? initialCharts;
  final ChartFilter? initialFilter;

  // 新版本參數（用於內部計算）
  final List<BirthData>? birthDataToProcess;
  final List<ChartData>? cachedChartData;
  final String? cachedDataHash;
  final Map<String, ChartData>? chartDataMap;
  final Map<String, String>? birthDataHashMap;

  const ChartFilterPage({
    Key? key,
    // 舊版本參數
    this.initialCharts,
    this.initialFilter,
    // 新版本參數
    this.birthDataToProcess,
    this.cachedChartData,
    this.cachedDataHash,
    this.chartDataMap,
    this.birthDataHashMap,
  }) : super(key: key);

  @override
  State<ChartFilterPage> createState() => _ChartFilterPageState();
}

class _ChartFilterPageState extends State<ChartFilterPage>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  late ChartFilterViewModel _viewModel;
  bool _isInitializing = false;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _viewModel = ChartFilterViewModel();

    // 初始化資料
    _initializeData();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _initializeData() async {
    if (mounted) {
      setState(() {
        _isInitializing = true;
      });
    }

    try {
      print('🔍 ChartFilterPage: _initializeData 開始');

      // 優先使用新版本參數（來自 FilesPage 的出生資料）
      if (widget.birthDataToProcess != null) {
        logger.i('ChartFilterPage: 使用新版本參數，處理 ${widget.birthDataToProcess!.length} 個出生資料');
        await _processChartDataFromBirthData();
      }
      // 向後兼容：使用舊版本參數（已計算的星盤資料）
      else if (widget.initialCharts != null && widget.initialCharts!.isNotEmpty) {
        logger.i('ChartFilterPage: 使用舊版本參數，已計算的星盤資料，共 ${widget.initialCharts!.length} 個');
        print('🔍 ChartFilterPage: 使用快取資料，不會重新計算');
        _viewModel.setCharts(widget.initialCharts!);
      }
      // 最後選項：從資料庫載入並計算
      else {
        logger.i('ChartFilterPage: 沒有傳入資料，從資料庫載入並計算');
        print('🔍 ChartFilterPage: 沒有快取資料，將重新計算');
        await _viewModel.loadChartsFromBirthData();
      }

      // 設置初始篩選器
      if (widget.initialFilter != null) {
        _viewModel.setCurrentFilter(widget.initialFilter!);
      } else {
        _viewModel.createNewFilter();
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('初始化資料時出錯: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isInitializing = false;
        });
      }
    }
  }

  /// 處理來自 FilesPage 的出生資料並計算星盤
  Future<void> _processChartDataFromBirthData() async {
    final birthDataList = widget.birthDataToProcess!;

    // 生成當前資料的雜湊值
    final currentHash = _generateBirthDataHash(birthDataList);
    logger.i('當前資料雜湊值: $currentHash');

    // 檢查整體快取是否有效
    if (_isCacheValid(currentHash)) {
      logger.i('✅ 使用完整快取的星盤資料，共 ${widget.cachedChartData!.length} 個星盤');
      _viewModel.setCharts(widget.cachedChartData!);
      return;
    }

    // 檢測變更的資料
    final changedBirthData = _getChangedBirthData(birthDataList);
    final deletedIds = _getDeletedBirthDataIds(birthDataList);

    // 如果有快取且只是部分資料變更，進行增量更新
    if (widget.chartDataMap != null &&
        widget.chartDataMap!.isNotEmpty &&
        (changedBirthData.isNotEmpty || deletedIds.isNotEmpty)) {
      logger.i('檢測到資料變更：${changedBirthData.length} 個變更，${deletedIds.length} 個刪除');
      await _performIncrementalUpdate(birthDataList, changedBirthData, deletedIds);
      return;
    }

    // 首次計算或快取完全失效，進行完整計算
    logger.i('執行完整計算，共 ${birthDataList.length} 個出生資料');
    await _performFullCalculation(birthDataList);
  }

  /// 生成出生資料的雜湊值
  String _generateBirthDataHash(List<BirthData> birthDataList) {
    final sortedData = birthDataList.map((data) =>
      '${data.id}_${data.name}_${data.dateTime.millisecondsSinceEpoch}_${data.latitude}_${data.longitude}'
    ).toList()..sort();

    return sortedData.join('|').hashCode.toString();
  }

  /// 檢查快取是否有效
  bool _isCacheValid(String currentHash) {
    return widget.cachedDataHash != null &&
           widget.cachedDataHash == currentHash &&
           widget.cachedChartData != null &&
           widget.cachedChartData!.isNotEmpty;
  }

  /// 獲取變更的出生資料
  List<BirthData> _getChangedBirthData(List<BirthData> currentData) {
    if (widget.birthDataHashMap == null) return currentData;

    final changedData = <BirthData>[];
    for (final birthData in currentData) {
      final currentDataHash = _generateSingleBirthDataHash(birthData);
      final cachedHash = widget.birthDataHashMap![birthData.id];

      if (cachedHash == null || cachedHash != currentDataHash) {
        changedData.add(birthData);
      }
    }
    return changedData;
  }

  /// 獲取已刪除的出生資料ID
  List<String> _getDeletedBirthDataIds(List<BirthData> currentData) {
    if (widget.chartDataMap == null) return [];

    final currentIds = currentData.map((data) => data.id).toSet();
    final cachedIds = widget.chartDataMap!.keys.toSet();

    return cachedIds.difference(currentIds).toList();
  }

  /// 生成單個出生資料的雜湊值
  String _generateSingleBirthDataHash(BirthData birthData) {
    return '${birthData.name}_${birthData.dateTime.millisecondsSinceEpoch}_${birthData.latitude}_${birthData.longitude}';
  }

  /// 執行增量更新
  Future<void> _performIncrementalUpdate(
    List<BirthData> allBirthData,
    List<BirthData> changedBirthData,
    List<String> deletedIds,
  ) async {
    try {
      // 複製現有的快取資料
      final chartDataMap = Map<String, ChartData>.from(widget.chartDataMap!);

      // 移除已刪除的資料
      for (final deletedId in deletedIds) {
        chartDataMap.remove(deletedId);
      }

      // 計算變更的星盤資料
      if (changedBirthData.isNotEmpty) {
        final updatedCharts = await _calculateChartsWithProgress(changedBirthData);

        // 更新快取
        for (final chart in updatedCharts) {
          chartDataMap[chart.primaryPerson.id] = chart;
        }
      }

      // 重建完整的星盤列表
      final allCharts = <ChartData>[];
      for (final birthData in allBirthData) {
        final chart = chartDataMap[birthData.id];
        if (chart != null) {
          allCharts.add(chart);
        }
      }

      logger.i('增量更新完成，共 ${allCharts.length} 個星盤');
      _viewModel.setCharts(allCharts);

    } catch (e) {
      logger.e('增量更新失敗: $e');
      // 如果增量更新失敗，回退到完整計算
      await _performFullCalculation(allBirthData);
    }
  }

  /// 執行完整計算
  Future<void> _performFullCalculation(List<BirthData> birthDataList) async {
    try {
      // 計算所有星盤資料
      final charts = await _calculateChartsWithProgress(birthDataList);

      logger.i('完整計算完成，共 ${charts.length} 個星盤');
      _viewModel.setCharts(charts);

    } catch (e) {
      logger.e('完整計算失敗: $e');
      rethrow;
    }
  }

  /// 帶進度的星盤計算方法
  Future<List<ChartData>> _calculateChartsWithProgress(List<BirthData> birthDataList) async {
    final charts = <ChartData>[];
    final total = birthDataList.length;

    // 載入圖表設定
    final chartSettings = await ChartSettings.loadFromPrefs();

    // 批量處理，每批處理5個
    const batchSize = 5;
    final batches = <List<BirthData>>[];

    for (int i = 0; i < birthDataList.length; i += batchSize) {
      final end = (i + batchSize < birthDataList.length)
          ? i + batchSize
          : birthDataList.length;
      batches.add(birthDataList.sublist(i, end));
    }

    int processedCount = 0;

    for (int batchIndex = 0; batchIndex < batches.length; batchIndex++) {
      final batch = batches[batchIndex];

      // 並行處理當前批次
      final futures = batch.map((birthData) async {
        try {
          // 創建基本ChartData
          final chartData = ChartData(
            chartType: ChartType.natal,
            primaryPerson: birthData,
          );

          // 計算星盤資料
          final calculatedChart = await AstrologyService.calculateChartData(
            chartData,
            houseSystem: chartSettings.houseSystem.name,
            includeMinorAspects: chartSettings.showMinorAspects,
          );

          return calculatedChart;
        } catch (e) {
          logger.w('計算星盤資料失敗: ${birthData.name}, 錯誤: $e');
          return null;
        }
      }).toList();

      // 等待當前批次完成
      final results = await Future.wait(futures);

      // 添加成功計算的結果
      for (final result in results) {
        if (result != null) {
          charts.add(result);
        }
        processedCount++;
      }

      // 更新進度（如果需要的話，可以通過 setState 更新 UI）
      final progress = processedCount / total;
      logger.d('計算進度: ${(progress * 100).toInt()}% ($processedCount/$total)');
    }

    return charts;
  }

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider.value(
      value: _viewModel,
      child: Consumer<ThemeProvider>(
        builder: (context, themeProvider, child) {
          return Scaffold(
            appBar: _buildAppBar(themeProvider),
            body: _isInitializing ? _buildLoadingBody(themeProvider) : _buildBody(themeProvider),
            floatingActionButton: _isInitializing ? null : _buildFloatingActionButton(),
            backgroundColor: Colors.grey.shade50,
          );
        },
      ),
    );
  }

  PreferredSizeWidget _buildAppBar(ThemeProvider themeProvider) {
    final primaryColor = AppColors.royalIndigo;
    final backgroundColor = Colors.grey.shade50;

    return AppBar(
      title: Text(
        '星盤篩選器',
        style: TextStyle(
          color: primaryColor,
          fontSize: 20,
          fontWeight: FontWeight.bold,
        ),
      ),
      backgroundColor: backgroundColor,
      elevation: 0,
      iconTheme: IconThemeData(color: primaryColor),
      bottom: TabBar(
        controller: _tabController,
        labelColor: primaryColor,
        unselectedLabelColor: primaryColor.withValues(alpha: 0.6),
        indicatorColor: primaryColor,
        indicatorWeight: 3,
        tabs: [
          Tab(
            icon: Icon(Icons.tune, color: primaryColor),
            text: '篩選設定',
          ),
          Tab(
            icon: Icon(Icons.view_list, color: primaryColor),
            text: '篩選結果',
          ),
          Tab(
            icon: Icon(Icons.bookmark, color: primaryColor),
            text: '已保存',
          ),
        ],
      ),
      actions: [
        Consumer<ChartFilterViewModel>(
          builder: (context, viewModel, child) {
            return PopupMenuButton<String>(
              icon: Icon(Icons.more_vert, color: primaryColor),
              onSelected: (value) => _handleMenuAction(value, viewModel),
              itemBuilder: (context) => [
                PopupMenuItem(
                  value: 'save',
                  child: ListTile(
                    leading: Icon(Icons.save_alt, color: AppColors.success),
                    title: const Text('保存篩選器'),
                    dense: true,
                  ),
                ),
                // PopupMenuItem(
                //   value: 'load',
                //   child: ListTile(
                //     leading: Icon(Icons.folder_open, color: AppColors.info),
                //     title: const Text('載入篩選器'),
                //     dense: true,
                //   ),
                // ),
                PopupMenuItem(
                  value: 'clear',
                  child: ListTile(
                    leading: Icon(Icons.refresh, color: AppColors.warning),
                    title: const Text('重置篩選條件'),
                    dense: true,
                  ),
                ),
              ],
            );
          },
        ),
        const SizedBox(width: 8),
      ],
    );
  }

  Widget _buildLoadingBody(ThemeProvider themeProvider) {
    final primaryColor = AppColors.royalIndigo;
    final textColor = AppColors.textMedium;

    return Container(
      color: Colors.grey.shade50,
      child: Center(
        child: Card(
          elevation: 8,
          color: Colors.white,
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
          child: Padding(
            padding: const EdgeInsets.all(32),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                CircularProgressIndicator(
                  valueColor: AlwaysStoppedAnimation<Color>(primaryColor),
                  strokeWidth: 3,
                ),
                const SizedBox(height: 24),
                Text(
                  '正在載入星盤資料...',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.w600,
                    color: textColor,
                  ),
                ),
                const SizedBox(height: 12),
                Text(
                  '首次載入需要計算所有星盤資料，請稍候',
                  style: TextStyle(
                    fontSize: 14,
                    color: textColor.withValues(alpha: 0.8),
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 16),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                  decoration: BoxDecoration(
                    color: primaryColor.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(20),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(Icons.auto_awesome, color: primaryColor, size: 16),
                      const SizedBox(width: 8),
                      Text(
                        '智能篩選系統',
                        style: TextStyle(
                          color: primaryColor,
                          fontSize: 12,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildBody(ThemeProvider themeProvider) {
    return Container(
      color: Colors.grey.shade50,
      child: Consumer<ChartFilterViewModel>(
        builder: (context, viewModel, child) {
          if (viewModel.isLoading) {
            final primaryColor = AppColors.royalIndigo;
            return Center(
              child: CircularProgressIndicator(
                valueColor: AlwaysStoppedAnimation<Color>(primaryColor),
              ),
            );
          }

          return TabBarView(
            controller: _tabController,
            children: [
              _buildFilterTab(viewModel, themeProvider),
              _buildResultsTab(viewModel, themeProvider),
              _buildSavedFiltersTab(viewModel, themeProvider),
            ],
          );
        },
      ),
    );
  }

  Widget _buildFilterTab(ChartFilterViewModel viewModel, ThemeProvider themeProvider) {
    final primaryColor = AppColors.royalIndigo;

    // currentFilter 現在永遠不會為 null，因為會自動創建空的篩選器
    return ResponsivePageWrapper(
      maxWidth: 800.0, // 篩選頁面適合中等寬度
      child: SingleChildScrollView(
        padding: ResponsiveUtils.getResponsivePadding(context),
        child: Column(
        children: [
          // 篩選器狀態提示卡片
          if (viewModel.currentFilter!.isEmpty)
            Card(
              elevation: 2,
              shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
              child: Container(
                width: double.infinity,
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: primaryColor.withValues(alpha: 0.05),
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(color: primaryColor.withValues(alpha: 0.2)),
                ),
                child: Row(
                  children: [
                    Icon(
                      Icons.info_outline,
                      color: primaryColor,
                      size: 20,
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Text(
                        '目前沒有設定篩選條件，將顯示所有星盤',
                        style: TextStyle(
                          color: primaryColor,
                          fontSize: 14,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          if (viewModel.currentFilter!.isEmpty) const SizedBox(height: 16),

          // 篩選器設定區域
          Card(
            elevation: 2,
            color: Colors.white,
            shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: ChartFilterWidget(
                filter: viewModel.currentFilter!,
                onFilterChanged: viewModel.updateCurrentFilter,
              ),
            ),
          ),
          const SizedBox(height: 16),
          _buildFilterActions(viewModel, themeProvider),
        ],
        ),
      ),
    );
  }

  Widget _buildFilterActions(ChartFilterViewModel viewModel, ThemeProvider themeProvider) {

    return Card(
      elevation: 2,
      color: Colors.white,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Row(
          children: [
            Expanded(
              child: ElevatedButton.icon(
                onPressed: () {
                  viewModel.applyFilter();
                  // 應用篩選後自動切換到結果頁面
                  _tabController.animateTo(1);
                },
                icon: const Icon(Icons.search),
                label: const Text('應用篩選'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.royalIndigo,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 12),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                  elevation: 2,
                ),
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: OutlinedButton.icon(
                onPressed: () => viewModel.resetFilter(),
                icon: const Icon(Icons.refresh),
                label: const Text('重置篩選'),
                style: OutlinedButton.styleFrom(
                  foregroundColor: AppColors.warning,
                  side: BorderSide(color: AppColors.warning),
                  padding: const EdgeInsets.symmetric(vertical: 12),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildResultsTab(ChartFilterViewModel viewModel, ThemeProvider themeProvider) {
    final filteredCharts = viewModel.filteredCharts;
    final primaryColor = AppColors.royalIndigo;

    if (filteredCharts.isEmpty) {
      return Center(
        child: Card(
          elevation: 4,
          color: Colors.white,
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
          child: Padding(
            padding: const EdgeInsets.all(32),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: primaryColor.withValues(alpha: 0.1),
                    shape: BoxShape.circle,
                  ),
                  child: Icon(
                    Icons.search_off,
                    size: 48,
                    color: primaryColor,
                  ),
                ),
                const SizedBox(height: 20),
                Text(
                  '沒有符合條件的星盤',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.w600,
                    color: AppColors.textDark,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  '請調整篩選條件或檢查資料',
                  style: TextStyle(
                    fontSize: 14,
                    color: AppColors.textMedium,
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 16),
                OutlinedButton.icon(
                  onPressed: () => _tabController.animateTo(0),
                  icon: const Icon(Icons.tune),
                  label: const Text('調整篩選條件'),
                  style: OutlinedButton.styleFrom(
                    foregroundColor: primaryColor,
                    side: BorderSide(color: primaryColor),
                  ),
                ),
              ],
            ),
          ),
        ),
      );
    }

    return Column(
      children: [
        _buildResultsHeader(viewModel, themeProvider),
        Expanded(
          child: ListView.builder(
            padding: const EdgeInsets.all(16),
            itemCount: filteredCharts.length,
            itemBuilder: (context, index) {
              final chart = filteredCharts[index];
              return _buildChartResultCard(chart, index + 1, themeProvider);
            },
          ),
        ),
      ],
    );
  }

  Widget _buildResultsHeader(ChartFilterViewModel viewModel, ThemeProvider themeProvider) {
    final totalCount = viewModel.allCharts.length;
    final filteredCount = viewModel.filteredCharts.length;
    final primaryColor = AppColors.royalIndigo;
    final backgroundColor = Colors.white;

    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: primaryColor.withValues(alpha: 0.3)),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.08),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: primaryColor.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(Icons.analytics, color: primaryColor, size: 20),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  '篩選結果',
                  style: TextStyle(
                    fontSize: 14,
                    color: AppColors.textMedium,
                  ),
                ),
                Text(
                  '$filteredCount / $totalCount 個星盤',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: primaryColor,
                  ),
                ),
              ],
            ),
          ),
          if (viewModel.currentFilter != null && viewModel.currentFilter!.name.isNotEmpty)
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
              decoration: BoxDecoration(
                color: primaryColor.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(16),
                border: Border.all(color: primaryColor.withValues(alpha: 0.3)),
              ),
              child: Text(
                viewModel.currentFilter!.name,
                style: TextStyle(
                  fontSize: 12,
                  fontWeight: FontWeight.w500,
                  color: primaryColor,
                ),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildChartResultCard(ChartData chart, int index, ThemeProvider themeProvider) {
    final primaryColor = AppColors.royalIndigo;

    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      elevation: 3,
      color: Colors.white,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: InkWell(
        borderRadius: BorderRadius.circular(12),
        onTap: () => _handleChartAction('view', chart),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Row(
            children: [
              Container(
                width: 48,
                height: 48,
                decoration: BoxDecoration(
                  color: primaryColor,
                  shape: BoxShape.circle,
                  boxShadow: [
                    BoxShadow(
                      color: primaryColor.withValues(alpha: 0.3),
                      blurRadius: 8,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: Center(
                  child: Text(
                    '$index',
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      chart.primaryPerson.name,
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: AppColors.textDark,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Row(
                      children: [
                        Icon(Icons.location_on, size: 14, color: AppColors.textMedium),
                        const SizedBox(width: 4),
                        Expanded(
                          child: Text(
                            chart.primaryPerson.birthPlace,
                            style: TextStyle(
                              fontSize: 13,
                              color: AppColors.textMedium,
                            ),
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 2),
                    Row(
                      children: [
                        Icon(Icons.access_time, size: 14, color: AppColors.textMedium),
                        const SizedBox(width: 4),
                        Text(
                          _formatDateTime(chart.primaryPerson.dateTime),
                          style: TextStyle(
                            fontSize: 13,
                            color: AppColors.textMedium,
                          ),
                        ),
                      ],
                    ),
                    if (chart.primaryPerson.notes != null && chart.primaryPerson.notes!.isNotEmpty) ...[
                      const SizedBox(height: 2),
                      Row(
                        children: [
                          Icon(Icons.note, size: 14, color: AppColors.textMedium),
                          const SizedBox(width: 4),
                          Expanded(
                            child: Text(
                              chart.primaryPerson.notes!,
                              style: TextStyle(
                                fontSize: 12,
                                color: AppColors.textMedium,
                                fontStyle: FontStyle.italic,
                              ),
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ],
                ),
              ),
              PopupMenuButton<String>(
                icon: Icon(Icons.more_vert, color: primaryColor),
                onSelected: (value) => _handleChartAction(value, chart),
                itemBuilder: (context) => [
                  PopupMenuItem(
                    value: 'view',
                    child: ListTile(
                      leading: Icon(Icons.visibility, color: AppColors.info),
                      title: const Text('查看星盤'),
                      dense: true,
                    ),
                  ),
                  PopupMenuItem(
                    value: 'details',
                    child: ListTile(
                      leading: Icon(Icons.edit, color: AppColors.warning),
                      title: const Text('編輯資料'),
                      dense: true,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSavedFiltersTab(ChartFilterViewModel viewModel, ThemeProvider themeProvider) {
    final savedFilters = viewModel.savedFilters;
    
    if (savedFilters.isEmpty) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.bookmark_border, size: 64, color: Colors.grey),
            SizedBox(height: 16),
            Text(
              '尚未保存任何篩選器',
              style: TextStyle(fontSize: 18, color: Colors.grey),
            ),
            SizedBox(height: 8),
            Text(
              '在篩選設定頁面創建篩選器後可以保存',
              style: TextStyle(fontSize: 14, color: Colors.grey),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: savedFilters.length,
      itemBuilder: (context, index) {
        final filter = savedFilters[index];
        return _buildSavedFilterCard(filter, viewModel);
      },
    );
  }

  Widget _buildSavedFilterCard(ChartFilter filter, ChartFilterViewModel viewModel) {
    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      child: ListTile(
        leading: const Icon(Icons.filter_list, color: Colors.blue),
        title: Text(filter.name),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              filter.getDescription(),
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
            const SizedBox(height: 4),
            Text(
              '創建時間：${_formatDateTime(filter.createdAt)}',
              style: const TextStyle(fontSize: 12, color: Colors.grey),
            ),
          ],
        ),
        trailing: PopupMenuButton<String>(
          onSelected: (value) => _handleSavedFilterAction(value, filter, viewModel),
          itemBuilder: (context) => [
            const PopupMenuItem(
              value: 'load',
              child: ListTile(
                leading: Icon(Icons.folder_open),
                title: Text('載入'),
              ),
            ),
            const PopupMenuItem(
              value: 'duplicate',
              child: ListTile(
                leading: Icon(Icons.copy),
                title: Text('複製'),
              ),
            ),
            const PopupMenuItem(
              value: 'rename',
              child: ListTile(
                leading: Icon(Icons.edit),
                title: Text('重新命名'),
              ),
            ),
            const PopupMenuItem(
              value: 'delete',
              child: ListTile(
                leading: Icon(Icons.delete),
                title: Text('刪除'),
              ),
            ),
          ],
        ),
        onTap: () => _handleSavedFilterAction('load', filter, viewModel),
      ),
    );
  }

  Widget? _buildFloatingActionButton() {
    // 移除新增篩選器按鈕，因為用戶進入頁面時就有空的篩選器可用
    return null;
  }

  void _handleMenuAction(String action, ChartFilterViewModel viewModel) {
    switch (action) {
      case 'save':
        _saveCurrentFilter(viewModel);
        break;
      // case 'load':
      //   _showLoadFilterDialog(viewModel);
      //   break;
      case 'clear':
        _clearFilter(viewModel);
        break;
    }
  }

  void _handleChartAction(String action, ChartData chart) {
    switch (action) {
      case 'view':
        _viewChart(chart);
        break;
      case 'details':
        _showChartDetails(chart);
        break;
    }
  }

  void _handleSavedFilterAction(String action, ChartFilter filter, ChartFilterViewModel viewModel) {
    switch (action) {
      case 'load':
        viewModel.loadFilter(filter);
        _tabController.animateTo(0); // 切換到篩選設定頁面
        break;
      case 'duplicate':
        viewModel.duplicateFilter(filter);
        break;
      case 'rename':
        _showRenameFilterDialog(filter, viewModel);
        break;
      case 'delete':
        _showDeleteFilterDialog(filter, viewModel);
        break;
    }
  }

  String _formatDateTime(DateTime dateTime) {
    return '${dateTime.year}/${dateTime.month.toString().padLeft(2, '0')}/${dateTime.day.toString().padLeft(2, '0')} '
           '${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}';
  }

  // 以下方法需要實作具體功能
  void _saveCurrentFilter(ChartFilterViewModel viewModel) {
    final currentFilter = viewModel.currentFilter!;

    // 檢查篩選器是否為空（沒有設定任何條件）
    if (currentFilter.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('請先設定篩選條件再保存'),
          backgroundColor: Colors.orange,
        ),
      );
      return;
    }

    // 顯示保存對話框
    showDialog(
      context: context,
      builder: (BuildContext context) {
        String filterName = currentFilter.name ?? '';
        final nameController = TextEditingController(text: filterName);

        return AlertDialog(
          title: const Text('保存篩選器'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              TextField(
                controller: nameController,
                decoration: const InputDecoration(
                  labelText: '篩選器名稱',
                  hintText: '請輸入篩選器名稱',
                ),
                autofocus: true,
              ),
              const SizedBox(height: 16),
              Text(
                '篩選條件：${currentFilter.groups.length} 個群組',
                style: const TextStyle(color: Colors.grey),
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('取消'),
            ),
            ElevatedButton(
              onPressed: () async {
                final name = nameController.text.trim();
                if (name.isEmpty) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('請輸入篩選器名稱'),
                      backgroundColor: Colors.red,
                    ),
                  );
                  return;
                }

                Navigator.of(context).pop();

                // 保存篩選器
                final filterToSave = currentFilter.copyWith(name: name);
                await viewModel.saveFilter(filterToSave);

                if (mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text('篩選器「$name」已保存'),
                      backgroundColor: Colors.green,
                    ),
                  );
                }
              },
              child: const Text('保存'),
            ),
          ],
        );
      },
    );
  }

  void _showLoadFilterDialog(ChartFilterViewModel viewModel) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('載入篩選器'),
          content: SizedBox(
            width: double.maxFinite,
            height: 400,
            child: Consumer<ChartFilterViewModel>(
              builder: (context, vm, child) {
                if (vm.savedFilters.isEmpty) {
                  return const Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(Icons.filter_list_off, size: 64, color: Colors.grey),
                        SizedBox(height: 16),
                        Text(
                          '沒有已保存的篩選器',
                          style: TextStyle(color: Colors.grey),
                        ),
                      ],
                    ),
                  );
                }

                return ListView.builder(
                  itemCount: vm.savedFilters.length,
                  itemBuilder: (context, index) {
                    final filter = vm.savedFilters[index];
                    return ListTile(
                      leading: const Icon(Icons.filter_alt),
                      title: Text(filter.name ?? '未命名篩選器'),
                      subtitle: Text(
                        '${filter.groups.length} 個群組 • '
                        '${filter.groups.map((g) => g.conditions.length).reduce((a, b) => a + b)} 個條件',
                      ),
                      trailing: PopupMenuButton<String>(
                        onSelected: (value) {
                          Navigator.of(context).pop(); // 先關閉載入對話框
                          switch (value) {
                            case 'rename':
                              _showRenameFilterDialog(filter, viewModel);
                              break;
                            case 'delete':
                              _showDeleteFilterDialog(filter, viewModel);
                              break;
                          }
                        },
                        itemBuilder: (context) => [
                          const PopupMenuItem(
                            value: 'rename',
                            child: ListTile(
                              leading: Icon(Icons.edit),
                              title: Text('重新命名'),
                            ),
                          ),
                          const PopupMenuItem(
                            value: 'delete',
                            child: ListTile(
                              leading: Icon(Icons.delete, color: Colors.red),
                              title: Text('刪除', style: TextStyle(color: Colors.red)),
                            ),
                          ),
                        ],
                      ),
                      onTap: () {
                        Navigator.of(context).pop();
                        viewModel.loadFilter(filter);
                        ScaffoldMessenger.of(context).showSnackBar(
                          SnackBar(
                            content: Text('已載入篩選器「${filter.name}」'),
                            backgroundColor: Colors.green,
                          ),
                        );
                      },
                    );
                  },
                );
              },
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('關閉'),
            ),
          ],
        );
      },
    );
  }

  void _clearFilter(ChartFilterViewModel viewModel) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('重置篩選條件'),
          content: const Text('確定要清除所有篩選條件嗎？這將移除所有設定的篩選規則，但保留篩選器結構。'),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('取消'),
            ),
            ElevatedButton(
              onPressed: () {
                Navigator.of(context).pop();
                viewModel.clearCurrentFilter();
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('篩選條件已重置'),
                    backgroundColor: Colors.green,
                  ),
                );
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.orange,
                foregroundColor: Colors.white,
              ),
              child: const Text('重置'),
            ),
          ],
        );
      },
    );
  }



  void _viewChart(ChartData chart) {
    // 導航到星盤頁面
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => ChangeNotifierProvider(
          create: (_) => ChartViewModel.withChartData(initialChartData: chart),
          child: ChartPageNew(chartData: chart),
        ),
      ),
    );
  }

  void _showChartDetails(ChartData chart) {
    // 導航到編輯出生資料頁面
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => BirthDataFormPage(
          initialData: chart.primaryPerson,
        ),
      ),
    ).then((result) {
      // 如果編輯了資料，可以在這裡處理更新
      if (result != null) {
        // 可以考慮重新載入篩選結果或更新顯示
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('出生資料已更新'),
            backgroundColor: Colors.green,
          ),
        );
      }
    });
  }

  void _showRenameFilterDialog(ChartFilter filter, ChartFilterViewModel viewModel) {
    final nameController = TextEditingController(text: filter.name ?? '');

    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('重新命名篩選器'),
          content: TextField(
            controller: nameController,
            decoration: const InputDecoration(
              labelText: '篩選器名稱',
              hintText: '請輸入新的篩選器名稱',
            ),
            autofocus: true,
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('取消'),
            ),
            ElevatedButton(
              onPressed: () async {
                final newName = nameController.text.trim();
                if (newName.isEmpty) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('請輸入篩選器名稱'),
                      backgroundColor: Colors.red,
                    ),
                  );
                  return;
                }

                Navigator.of(context).pop();

                // 更新篩選器名稱
                final updatedFilter = filter.copyWith(name: newName);
                await viewModel.updateFilter(updatedFilter);

                if (mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text('篩選器已重新命名為「$newName」'),
                      backgroundColor: Colors.green,
                    ),
                  );
                }
              },
              child: const Text('確定'),
            ),
          ],
        );
      },
    );
  }

  void _showDeleteFilterDialog(ChartFilter filter, ChartFilterViewModel viewModel) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('刪除篩選器'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text('確定要刪除篩選器「${filter.name ?? '未命名篩選器'}」嗎？'),
              const SizedBox(height: 8),
              const Text(
                '此操作無法復原。',
                style: TextStyle(color: Colors.red, fontSize: 12),
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('取消'),
            ),
            ElevatedButton(
              onPressed: () async {
                Navigator.of(context).pop();

                // 刪除篩選器
                await viewModel.deleteFilter(filter);

                if (mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text('篩選器「${filter.name}」已刪除'),
                      backgroundColor: Colors.orange,
                    ),
                  );
                }
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.red,
                foregroundColor: Colors.white,
              ),
              child: const Text('刪除'),
            ),
          ],
        );
      },
    );
  }
}

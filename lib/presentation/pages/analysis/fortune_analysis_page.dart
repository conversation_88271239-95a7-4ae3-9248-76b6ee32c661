import 'package:flutter/material.dart';

import '../../../astreal.dart';
import '../ai_interpretation_result_page.dart';
import '../interpretation_records_page.dart';

/// 運勢分析頁面
class FortuneAnalysisPage extends StatefulWidget {
  final BirthData selectedPerson;

  const FortuneAnalysisPage({
    super.key,
    required this.selectedPerson,
  });

  @override
  State<FortuneAnalysisPage> createState() => _FortuneAnalysisPageState();
}

class _FortuneAnalysisPageState extends State<FortuneAnalysisPage> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: AppBar(
        title: const Text(
          '我的運勢分析',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.w500,
            color: Colors.white,
          ),
        ),
        backgroundColor: AppColors.solarAmber,
        foregroundColor: Colors.white,
        elevation: 0,
        actions: [
          // 查看分析記錄按鈕
          IconButton(
            onPressed: _viewAnalysisHistory,
            icon: const Icon(
              Icons.history,
              color: Colors.white,
            ),
            tooltip: '查看分析記錄',
          ),
        ],
      ),
      body: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 用戶資訊區域
            _buildUserInfoSection(),

            // 分隔線
            Divider(
              height: 1,
              color: Colors.grey[200],
            ),

            // 運勢分析類別列表
            _buildFortuneAnalysisCategoriesSection(),
          ],
        ),
      ),
    );
  }

  /// 構建用戶資訊區域
  Widget _buildUserInfoSection() {
    final selectedPerson = widget.selectedPerson;
    return Container(
      padding: const EdgeInsets.all(20),
      color: Colors.white,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: AppColors.solarAmber.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: const Icon(
                  Icons.auto_awesome,
                  color: AppColors.solarAmber,
                  size: 24,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      '個人運勢分析',
                      style: TextStyle(
                        fontSize: 22,
                        fontWeight: FontWeight.bold,
                        color: AppColors.textDark,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      '基於 ${selectedPerson.name} 的星盤',
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.grey[600],
                        height: 1.3,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Container(
                width: 40,
                height: 40,
                decoration: BoxDecoration(
                  color: AppColors.solarAmber.withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(20),
                ),
                child: Center(
                  child: Text(
                    selectedPerson.name.isNotEmpty
                        ? selectedPerson.name[0].toUpperCase()
                        : '✨',
                      style: const TextStyle(
                        color: AppColors.solarAmber,
                        fontWeight: FontWeight.w500,
                        fontSize: 16,
                      ),
                    ),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        selectedPerson.name,
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w500,
                          color: AppColors.textDark,
                        ),
                      ),
                      const SizedBox(height: 2),
                      Text(
                        '${selectedPerson.dateTime.year}年${selectedPerson.dateTime.month}月${selectedPerson.dateTime.day}日 • ${selectedPerson.birthPlace}',
                        style: TextStyle(
                          fontSize: 13,
                          color: Colors.grey[600],
                        ),
                        overflow: TextOverflow.ellipsis,
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ],
      ),
    );
  }

  /// 構建運勢分析類別區域
  Widget _buildFortuneAnalysisCategoriesSection() {
    final selectedPerson = widget.selectedPerson;

    return Container(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            '運勢分析類別',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: AppColors.textDark,
            ),
          ),
          const SizedBox(height: 16),

          // 運勢分析類別列表
          ListView.separated(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemCount: FortuneCategory.getHomePageItems().length,
            separatorBuilder: (context, index) => const SizedBox(height: 12),
            itemBuilder: (context, index) {
              final category = FortuneCategory.getHomePageItems()[index];
              return _buildFortuneCategoryItem(
                  category, selectedPerson);
            },
          ),
        ],
      ),
    );
  }

  /// 構建運勢分析類別項目
  Widget _buildFortuneCategoryItem(
      FortuneCategory category, BirthData birthData) {
    return GestureDetector(
      onTap: () => _navigateToAnalysisDetail(category, birthData),
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: Colors.grey[200]!,
            width: 1,
          ),
        ),
        child: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: category.color.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(10),
              ),
              child: Icon(
                category.icon,
                color: category.color,
                size: 20,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    category.name,
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w500,
                      color: AppColors.textDark,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    category.description,
                    style: TextStyle(
                      fontSize: 13,
                      color: Colors.grey[600],
                      height: 1.3,
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                ],
              ),
            ),
            Icon(
              Icons.arrow_forward_ios,
              color: category.color.withValues(alpha: 0.6),
              size: 16,
            ),
          ],
        ),
      ),
    );
  }

  /// 導航到分析詳情
  void _navigateToAnalysisDetail(FortuneCategory category, BirthData birthData) {
    final chartData = ChartData(
      chartType: ChartType.natal,
      primaryPerson: birthData,
    );

    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => AIInterpretationResultPage(
          chartData: chartData,
          interpretationTitle: category.name,
          subtitle: category.description,
          suggestedQuestions: category.analysisPoints,
          autoExecuteFirstQuestion: false,
        ),
      ),
    );
  }


  /// 查看分析記錄
  void _viewAnalysisHistory() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const InterpretationRecordsPage(),
      ),
    );
  }
}

/// 運勢分析類別模型
class FortuneCategory {
  final String id;
  final String name;
  final String description;
  final IconData icon;
  final Color color;
  final ChartType chartType;
  final List<String> analysisPoints;
  final String? question; // 新增問題字段，用於AI分析

  const FortuneCategory({
    required this.id,
    required this.name,
    required this.description,
    required this.icon,
    required this.color,
    required this.chartType,
    required this.analysisPoints,
    this.question,
  });

  /// 創建用於主頁面顯示的運勢項目
  static List<FortuneCategory> getHomePageItems() {
    return [
      const FortuneCategory(
        id: 'recent_trends',
        name: '近期運勢走向',
        description: '看看最近的好機會與提醒',
        icon: Icons.trending_up,
        color: Color(0xFF10B981),
        chartType: ChartType.transit,
        analysisPoints: ['近期機會', '潛在挑戰', '行動建議'],
        question: '我想知道這段時間會遇到什麼挑戰或成長機會',
      ),
      const FortuneCategory(
        id: 'inner_growth',
        name: '內在成長節奏',
        description: '最近內心正在經歷哪些變化？',
        icon: Icons.psychology_alt,
        color: Color(0xFFEC4899),
        chartType: ChartType.secondaryProgression,
        analysisPoints: ['內在變化', '成長方向', '心理發展'],
        question: '我內在的成長正在往哪個方向發展？',
      ),
      const FortuneCategory(
        id: 'emotional_waves',
        name: '情緒波動觀察',
        description: '這陣子的心情變化代表什麼？',
        icon: Icons.mood,
        color: Color(0xFF3B82F6),
        chartType: ChartType.tertiaryProgression,
        analysisPoints: ['情緒模式', '心情變化', '情感意義'],
        question: '我最近的情緒起伏有哪些意義？',
      ),
      const FortuneCategory(
        id: 'key_turning_points',
        name: '關鍵轉折預測',
        description: '即將出現的人生變化提示',
        icon: Icons.flash_on,
        color: Color(0xFFF97316),
        chartType: ChartType.solarArcDirection,
        analysisPoints: ['轉折時機', '變化預測', '準備建議'],
        question: '我接下來可能會遇到什麼重要的改變？',
      ),
      const FortuneCategory(
        id: 'annual_theme',
        name: '今年的主題',
        description: '每年生日揭示的重要方向',
        icon: Icons.wb_sunny,
        color: Color(0xFFEAB308),
        chartType: ChartType.solarReturn,
        analysisPoints: ['年度主題', '重點挑戰', '發展方向'],
        question: '我想知道這一年會有哪些重點與挑戰？',
      ),
      const FortuneCategory(
        id: 'monthly_mood',
        name: '本月的心情與重點',
        description: '每月一次的情緒與生活指引',
        icon: Icons.nightlight_round,
        color: Color(0xFF9CA3AF),
        chartType: ChartType.lunarReturn,
        analysisPoints: ['月度情緒', '生活重點', '行動指引'],
        question: '我想了解這個月我的情緒與行動方向？',
      ),
      const FortuneCategory(
        id: 'life_stage_map',
        name: '人生階段地圖',
        description: '看看你現在正處在哪一段人生旅程',
        icon: Icons.timeline,
        color: Color(0xFF6366F1),
        chartType: ChartType.firdaria,
        analysisPoints: ['人生階段', '主題特質', '發展重點'],
        question: '我目前這幾年的人生主題是什麼？',
      ),
      const FortuneCategory(
        id: 'annual_focus',
        name: '今年的人生焦點',
        description: '探索你今年最重要的生活領域',
        icon: Icons.my_location,
        color: Color(0xFF14B8A6),
        chartType: ChartType.profection,
        analysisPoints: ['年度焦點', '生活重點', '發展方向'],
        question: '我今年的生活重點在哪？',
      ),
    ];
  }
}

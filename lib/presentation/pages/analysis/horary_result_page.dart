import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_markdown/flutter_markdown.dart';
import 'package:provider/provider.dart';
import 'package:url_launcher/url_launcher.dart';

import '../../../data/models/astrology/chart_data.dart';
import '../../../data/services/api/ai_api_service.dart';
import '../../../data/services/api/chart_interpretation_service.dart';
import '../../themes/app_theme.dart';
import '../../viewmodels/chart_viewmodel.dart';
import '../../viewmodels/horary_chart_viewmodel.dart';
import '../../widgets/dialogs/copy_options_dialog.dart';
import '../chart_page_new.dart';

/// 占星卜卦結果頁面
/// 專門用於顯示卜卦結果，不包含星盤圖與行星位置的tab
class HoraryResultPage extends StatefulWidget {
  final String question;
  final ChartData chartData;

  const HoraryResultPage({
    Key? key,
    required this.question,
    required this.chartData,
  }) : super(key: key);

  @override
  State<HoraryResultPage> createState() => _HoraryResultPageState();
}

class _HoraryResultPageState extends State<HoraryResultPage> {
  late HoraryChartViewModel _horaryViewModel;
  String? _aiInterpretation;
  bool _isLoadingAI = false;
  bool _showAIInterpretation = false;

  @override
  void initState() {
    super.initState();
    _horaryViewModel =
        Provider.of<HoraryChartViewModel>(context, listen: false);
    // 自動開始 AI 解讀
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadAIInterpretation();
    });
  }

  /// 載入 AI 解讀
  Future<void> _loadAIInterpretation() async {
    setState(() {
      _isLoadingAI = true;
      _showAIInterpretation = true;
    });

    try {
      final interpretation = await _generateAIInterpretation();
      setState(() {
        _aiInterpretation = interpretation.content;
        _isLoadingAI = false;
      });
    } catch (e) {
      setState(() {
        _isLoadingAI = false;
      });
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('解讀載入失敗：$e')),
        );
      }
    }
  }

  /// 生成 AI 解讀
  Future<AIApiResponse> _generateAIInterpretation() async {
    final prompt = _buildHoraryInterpretationPrompt();

    try {
      final interpretation =
          await ChartInterpretationService.getCustomInterpretation(
        chartData: widget.chartData,
        customPrompt: prompt,
      );
      return interpretation;
    } catch (e) {
      throw Exception('解讀服務暫時不可用：$e');
    }
  }

  /// 構建占星卜卦解讀提示詞
  String _buildHoraryInterpretationPrompt() {
    final chartData = widget.chartData;
    final birthData = chartData.primaryPerson;

    String prompt = '''
請為以下占星卜卦提供專業的解讀分析：

**卜卦問題**：${widget.question.isNotEmpty ? widget.question : (birthData.notes ?? '未記錄問題')}
**卜卦時間**：${_formatDateTime(birthData.dateTime)}
**卜卦地點**：${birthData.birthPlace}

**星盤資訊**：
- 星盤類型：${chartData.chartType.displayName}
- 經緯度：${birthData.latitude.toStringAsFixed(4)}, ${birthData.longitude.toStringAsFixed(4)}

請根據占星卜卦的傳統原則，提供以下內容的解讀：

## 1. 問題分析
- 分析問題的性質和類型
- 確定相關的宮位和行星

## 2. 星盤解讀
- 上升點和宮位系統分析
- 主要行星位置和相位
- 關鍵的占星因素

## 3. 卜卦判斷
- 根據傳統卜卦規則進行分析
- 考慮行星的力量和狀態
- 評估相位的影響

## 4. 結論與建議
- 對問題的明確答案
- 時間預測（如適用）
- 具體的行動建議

## 5. 注意事項
- 可能的變數和影響因素
- 需要關注的時間點

請用繁體中文回答，語言要專業但通俗易懂，並使用 Markdown 格式來組織內容，包括標題、列表、粗體等格式。
''';

    return prompt;
  }

  /// 複製 AI 解讀內容
  void _copyAIInterpretation() {
    if (_aiInterpretation != null) {
      // 移除 Markdown 格式，方便分享給其他人
      final cleanContent = _removeMarkdownFormatting(_aiInterpretation!);
      Clipboard.setData(ClipboardData(text: cleanContent));
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('解讀內容已複製到剪貼板（已移除格式）')),
      );
    }
  }

  /// 複製卜卦結果（使用星盤頁面的複製功能）
  Future<void> _copyHoraryResultWithOptions() async {
    try {
      // 創建 ChartViewModel 來使用複製功能
      final chartViewModel =
          ChartViewModel.withChartData(initialChartData: widget.chartData);

      // 顯示複製選項對話框
      final options = await showCopyOptionsDialog(context);

      // 如果用戶選擇了選項（沒有取消）
      if (options != null && mounted) {
        // 使用 ChartViewModel 的複製功能
        final success = await chartViewModel.copyChartInfo(options: options);
        if (mounted && success) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('卜卦結果已複製到剪貼板'),
              duration: Duration(seconds: 2),
            ),
          );
        } else if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('複製失敗，請重試'),
              duration: Duration(seconds: 2),
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('複製資訊時出錯: $e')),
        );
      }
    }
  }

  /// 複製簡單的卜卦結果文本
  Future<void> _copySimpleHoraryResult() async {
    try {
      final text = await _horaryViewModel.getHoraryResultText();

      // 複製到剪貼板
      Clipboard.setData(ClipboardData(text: text));

      // 顯示提示
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('卜卦結果已複製到剪貼板'),
            duration: Duration(seconds: 2),
          ),
        );
      }
    } catch (e) {
      // 顯示錯誤提示
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('複製資訊時出錯: $e')),
        );
      }
    }
  }

  /// 查看完整星盤
  void _viewFullChart() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => ChangeNotifierProvider(
          create: (_) =>
              ChartViewModel.withChartData(initialChartData: widget.chartData),
          child: ChartPageNew(chartData: widget.chartData),
        ),
      ),
    );
  }

  /// 格式化日期時間
  String _formatDateTime(DateTime dateTime) {
    return '${dateTime.year}/${dateTime.month}/${dateTime.day} '
        '${dateTime.hour.toString().padLeft(2, '0')}:'
        '${dateTime.minute.toString().padLeft(2, '0')}';
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('卜卦結果'),
        backgroundColor: AppColors.royalIndigo,
        foregroundColor: Colors.white,
        actions: [
          // 複製選項按鈕
          PopupMenuButton<String>(
            icon: const Icon(Icons.more_vert, color: Colors.white),
            tooltip: '複製選項',
            onSelected: (String action) async {
              switch (action) {
                case 'copy_simple':
                  _copySimpleHoraryResult();
                  break;
                case 'copy_detailed':
                  await _copyHoraryResultWithOptions();
                  break;
                case 'view_chart':
                  _viewFullChart();
                  break;
              }
            },
            itemBuilder: (BuildContext context) => [
              const PopupMenuItem<String>(
                value: 'copy_simple',
                child: Row(
                  children: [
                    Icon(Icons.content_copy, color: AppColors.royalIndigo),
                    SizedBox(width: 12),
                    Text('複製卜卦結果'),
                  ],
                ),
              ),
              const PopupMenuItem<String>(
                value: 'copy_detailed',
                child: Row(
                  children: [
                    Icon(Icons.copy_all, color: AppColors.royalIndigo),
                    SizedBox(width: 12),
                    Text('詳細複製選項'),
                  ],
                ),
              ),
              const PopupMenuItem<String>(
                value: 'view_chart',
                child: Row(
                  children: [
                    Icon(Icons.auto_awesome, color: AppColors.royalIndigo),
                    SizedBox(width: 12),
                    Text('查看完整星盤'),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 問題信息卡片
            _buildQuestionCard(),
            const SizedBox(height: 16),

            // 快速操作按鈕
            _buildQuickActions(),

            // const SizedBox(height: 16),
            // 卜卦結果內容
            // _buildResultContent(),

            // AI 解讀內容
            if (_showAIInterpretation) ...[
              const SizedBox(height: 16),
              _buildAIInterpretationCard(),
            ],
          ],
        ),
      ),
    );
  }

  /// 構建問題信息卡片
  Widget _buildQuestionCard() {
    final birthData = widget.chartData.primaryPerson;

    return Card(
      elevation: 3,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Container(
        width: double.infinity,
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: [
              AppColors.royalIndigo.withValues(alpha: 0.1),
              AppColors.solarAmber.withValues(alpha: 0.1),
            ],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
          borderRadius: BorderRadius.circular(12),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Row(
              children: [
                Icon(Icons.auto_awesome, color: AppColors.royalIndigo),
                SizedBox(width: 8),
                Text(
                  '卜卦問題',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: AppColors.royalIndigo,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Text(
              widget.question.isNotEmpty
                  ? widget.question
                  : (birthData.notes ?? '未記錄問題'),
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w500,
              ),
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                const Icon(Icons.access_time, size: 16, color: Colors.grey),
                const SizedBox(width: 4),
                Text(
                  '時間：${_formatDateTime(birthData.dateTime)}',
                  style: const TextStyle(fontSize: 14, color: Colors.grey),
                ),
              ],
            ),
            const SizedBox(height: 4),
            Row(
              children: [
                const Icon(Icons.location_on, size: 16, color: Colors.grey),
                const SizedBox(width: 4),
                Text(
                  '地點：${birthData.birthPlace}',
                  style: const TextStyle(fontSize: 14, color: Colors.grey),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// 構建結果內容
  Widget _buildResultContent() {
    return Consumer<HoraryChartViewModel>(
      builder: (context, viewModel, child) {
        return Card(
          elevation: 3,
          shape:
              RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Row(
                  children: [
                    Icon(Icons.psychology, color: AppColors.solarAmber),
                    SizedBox(width: 8),
                    Text(
                      '卜卦結果',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: AppColors.solarAmber,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                Container(
                  width: double.infinity,
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: Colors.grey.shade50,
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: Colors.grey.shade200),
                  ),
                  child: FutureBuilder<String>(
                    future: viewModel.getHoraryResultText(),
                    builder: (context, snapshot) {
                      if (snapshot.connectionState == ConnectionState.waiting) {
                        return const Center(
                          child: CircularProgressIndicator(),
                        );
                      } else if (snapshot.hasError) {
                        return Text(
                          '載入卜卦結果時出錯: ${snapshot.error}',
                          style: const TextStyle(
                            fontSize: 15,
                            height: 1.6,
                            color: Colors.red,
                          ),
                        );
                      } else {
                        final resultText = snapshot.data ?? '';
                        return Text(
                          resultText.isNotEmpty ? resultText : '暫無卜卦結果',
                          style: const TextStyle(
                            fontSize: 15,
                            height: 1.6,
                            color: Colors.black87,
                          ),
                        );
                      }
                    },
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  /// 構建快速操作按鈕
  Widget _buildQuickActions() {
    return Row(
      children: [
        Expanded(
          child: ElevatedButton.icon(
            icon: const Icon(Icons.content_copy, size: 18),
            label: const Text('複製結果'),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.solarAmber,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(vertical: 12),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            onPressed: _copySimpleHoraryResult,
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: ElevatedButton.icon(
            icon: const Icon(Icons.auto_awesome, size: 18),
            label: const Text('查看星盤'),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.royalIndigo,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(vertical: 12),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            onPressed: _viewFullChart,
          ),
        ),
      ],
    );
  }

  /// 構建 AI 解讀卡片
  Widget _buildAIInterpretationCard() {
    return Card(
      elevation: 3,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(
                  Icons.psychology_rounded,
                  color: AppColors.royalIndigo,
                  size: 24,
                ),
                const SizedBox(width: 8),
                const Text(
                  '解讀',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: AppColors.royalIndigo,
                  ),
                ),
                if (_aiInterpretation != null || kDebugMode)
                  const Spacer(),
                  IconButton(
                    icon: const Icon(Icons.copy_rounded),
                    onPressed: _copyAIInterpretation,
                    tooltip: '複製解讀',
                    color: AppColors.royalIndigo,
                  ),
              ],
            ),
            const SizedBox(height: 16),
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.grey.shade50,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.grey.shade200),
              ),
              child: _buildAIInterpretationContent(),
            ),
          ],
        ),
      ),
    );
  }

  /// 構建 AI 解讀內容
  Widget _buildAIInterpretationContent() {
    if (_isLoadingAI) {
      return const Center(
        child: Padding(
          padding: EdgeInsets.all(32),
          child: Column(
            children: [
              CircularProgressIndicator(),
              SizedBox(height: 16),
              Text('正在分析星盤，請稍候...'),
            ],
          ),
        ),
      );
    } else if (_aiInterpretation != null) {
      return MarkdownBody(
        data: _aiInterpretation!,
        styleSheet: MarkdownStyleSheet(
          h1: const TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: AppColors.royalIndigo,
          ),
          h2: const TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: AppColors.royalIndigo,
          ),
          h3: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: AppColors.solarAmber,
          ),
          p: const TextStyle(
            fontSize: 14,
            height: 1.6,
            color: Colors.black87,
          ),
          strong: const TextStyle(
            fontWeight: FontWeight.bold,
            color: AppColors.royalIndigo,
          ),
          listBullet: const TextStyle(
            color: AppColors.solarAmber,
          ),
        ),
        onTapLink: (text, href, title) {
          if (href != null) {
            launchUrl(Uri.parse(href));
          }
        },
      );
    } else {
      return Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.orange.shade50,
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: Colors.orange.shade200),
        ),
        child: Row(
          children: [
            Icon(
              Icons.warning_rounded,
              color: Colors.orange.shade600,
              size: 20,
            ),
            const SizedBox(width: 8),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    '解讀載入失敗',
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 4),
                  const Text(
                    '請檢查網路連接後重試',
                    style: TextStyle(fontSize: 12),
                  ),
                  const SizedBox(height: 8),
                  ElevatedButton(
                    onPressed: _loadAIInterpretation,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppColors.solarAmber,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(
                        horizontal: 16,
                        vertical: 8,
                      ),
                    ),
                    child: const Text('重試'),
                  ),
                ],
              ),
            ),
          ],
        ),
      );
    }
  }

  /// 移除 Markdown 格式，方便分享給其他人
  String _removeMarkdownFormatting(String content) {
    // 移除 Markdown 標記，但保留內容
    String cleaned = content;

    // 先移除代碼塊（包含內容）
    final lines = cleaned.split('\n');
    final filteredLines = <String>[];
    bool inCodeBlock = false;

    for (final line in lines) {
      if (line.trim().startsWith('```')) {
        inCodeBlock = !inCodeBlock;
        continue; // 跳過代碼塊標記行
      }
      if (!inCodeBlock) {
        filteredLines.add(line);
      }
    }

    cleaned = filteredLines.join('\n');

    // 處理粗體：**text** -> text
    cleaned = cleaned.replaceAllMapped(RegExp(r'\*\*(.*?)\*\*'), (match) => match.group(1) ?? '');

    // 處理斜體：*text* -> text
    cleaned = cleaned.replaceAllMapped(RegExp(r'\*(.*?)\*'), (match) => match.group(1) ?? '');

    // 移除標題：# -> 空
    cleaned = cleaned.replaceAll(RegExp(r'#{1,6}\s*'), '');

    // 處理連結：[text](url) -> text
    cleaned = cleaned.replaceAllMapped(RegExp(r'\[([^\]]+)\]\([^)]+\)'), (match) => match.group(1) ?? '');

    // 處理行內代碼：`code` -> code
    cleaned = cleaned.replaceAllMapped(RegExp(r'`([^`]+)`'), (match) => match.group(1) ?? '');

    // 列表項目：- item -> • item
    cleaned = cleaned.replaceAll(RegExp(r'^\s*[-*+]\s+', multiLine: true), '• ');

    // 數字列表：1. item -> item
    cleaned = cleaned.replaceAll(RegExp(r'^\s*\d+\.\s+', multiLine: true), '');

    // 多餘的換行：最多保留兩個
    cleaned = cleaned.replaceAll(RegExp(r'\n{3,}'), '\n\n');

    return cleaned.trim();
  }
}

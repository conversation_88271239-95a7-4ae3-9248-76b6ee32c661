import 'package:flutter/material.dart';

import '../../../astreal.dart';
/// 行星知識頁面
class PlanetKnowledgePage extends StatefulWidget {
  const PlanetKnowledgePage({super.key});

  @override
  State<PlanetKnowledgePage> createState() => _PlanetKnowledgePageState();
}

class _PlanetKnowledgePageState extends State<PlanetKnowledgePage> {
  
  /// 行星資料
  final List<Map<String, dynamic>> _planets = [
    {
      'name': '太陽',
      'symbol': '☉',
      'keywords': ['自我', '意志', '生命力', '創造'],
      'description': '太陽代表我們的核心自我、意志力和生命力。它象徵著我們的基本性格、創造力和領導能力。',
      'meaning': '太陽在占星學中是最重要的天體之一，代表著個人的本質和生命目標。',
      'color': Colors.orange,
    },
    {
      'name': '月亮',
      'symbol': '☽',
      'keywords': ['情感', '直覺', '潛意識', '母性'],
      'description': '月亮代表我們的情感世界、直覺和潛意識。它影響著我們的情緒反應和內在需求。',
      'meaning': '月亮反映了我們的情感模式、安全感需求和與母親的關係。',
      'color': Colors.blue,
    },
    {
      'name': '水星',
      'symbol': '☿',
      'keywords': ['溝通', '思維', '學習', '交流'],
      'description': '水星掌管溝通、思維和學習能力。它影響著我們的表達方式和資訊處理能力。',
      'meaning': '水星代表我們如何思考、學習和與他人溝通的方式。',
      'color': Colors.yellow,
    },
    {
      'name': '金星',
      'symbol': '♀',
      'keywords': ['愛情', '美感', '價值', '和諧'],
      'description': '金星代表愛情、美感和價值觀。它影響著我們的審美觀和人際關係。',
      'meaning': '金星顯示了我們如何表達愛意、追求美感和建立關係。',
      'color': Colors.pink,
    },
    {
      'name': '火星',
      'symbol': '♂',
      'keywords': ['行動', '勇氣', '競爭', '慾望'],
      'description': '火星代表行動力、勇氣和競爭精神。它影響著我們的動機和戰鬥力。',
      'meaning': '火星顯示了我們如何追求目標、表達憤怒和面對挑戰。',
      'color': Colors.red,
    },
    {
      'name': '木星',
      'symbol': '♃',
      'keywords': ['擴展', '智慧', '幸運', '哲學'],
      'description': '木星代表擴展、智慧和幸運。它影響著我們的信念系統和成長機會。',
      'meaning': '木星顯示了我們的哲學觀、學習方向和獲得幸運的領域。',
      'color': Colors.purple,
    },
    {
      'name': '土星',
      'symbol': '♄',
      'keywords': ['責任', '限制', '紀律', '成熟'],
      'description': '土星代表責任、限制和紀律。它影響著我們的成熟過程和人生課題。',
      'meaning': '土星顯示了我們需要學習的人生功課和成長的挑戰。',
      'color': Colors.grey,
    },
    {
      'name': '天王星',
      'symbol': '♅',
      'keywords': ['革新', '獨立', '突破', '未來'],
      'description': '天王星代表革新、獨立和突破。它影響著我們的創新能力和對自由的渴望。',
      'meaning': '天王星顯示了我們如何追求獨特性和突破傳統的方式。',
      'color': Colors.cyan,
    },
    {
      'name': '海王星',
      'symbol': '♆',
      'keywords': ['夢想', '直覺', '靈性', '幻想'],
      'description': '海王星代表夢想、直覺和靈性。它影響著我們的想像力和精神追求。',
      'meaning': '海王星顯示了我們的靈性傾向、藝術天賦和理想主義。',
      'color': Colors.teal,
    },
    {
      'name': '冥王星',
      'symbol': '♇',
      'keywords': ['轉化', '重生', '深度', '力量'],
      'description': '冥王星代表轉化、重生和深層力量。它影響著我們的深層變化和心理轉化。',
      'meaning': '冥王星顯示了我們經歷深層轉化和重生的領域。',
      'color': Colors.deepPurple,
    },
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text(
          '行星意義',
          style: TextStyle(
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        backgroundColor: AppColors.indigoLight,
        foregroundColor: Colors.white,
        elevation: 0,
      ),
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              AppColors.indigoLight.withValues(alpha: 0.1),
              Colors.white,
            ],
            stops: const [0.0, 0.3],
          ),
        ),
        child: ListView.builder(
          padding: const EdgeInsets.all(16),
          itemCount: _planets.length,
          itemBuilder: (context, index) {
            final planet = _planets[index];
            return Padding(
              padding: const EdgeInsets.only(bottom: 16),
              child: _buildPlanetCard(planet),
            );
          },
        ),
      ),
    );
  }

  /// 構建行星卡片
  Widget _buildPlanetCard(Map<String, dynamic> planet) {
    return StyledCard(
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 行星標題
            Row(
              children: [
                Container(
                  width: 60,
                  height: 60,
                  decoration: BoxDecoration(
                    color: (planet['color'] as Color).withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(30),
                  ),
                  child: Center(
                    child: Text(
                      planet['symbol'],
                      style: TextStyle(
                        fontSize: 28,
                        color: planet['color'],
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        planet['name'],
                        style: const TextStyle(
                          fontSize: 24,
                          fontWeight: FontWeight.bold,
                          color: AppColors.textDark,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        '占星行星',
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.grey.shade600,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: 16),
            
            // 關鍵詞
            const Text(
              '關鍵詞：',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: AppColors.textDark,
              ),
            ),
            const SizedBox(height: 8),
            Wrap(
              spacing: 8,
              runSpacing: 8,
              children: (planet['keywords'] as List<String>).map((keyword) {
                return Container(
                  padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                  decoration: BoxDecoration(
                    color: (planet['color'] as Color).withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(16),
                    border: Border.all(
                      color: (planet['color'] as Color).withValues(alpha: 0.3),
                    ),
                  ),
                  child: Text(
                    keyword,
                    style: TextStyle(
                      fontSize: 12,
                      color: planet['color'],
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                );
              }).toList(),
            ),
            
            const SizedBox(height: 16),
            
            // 基本描述
            const Text(
              '基本意義：',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: AppColors.textDark,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              planet['description'],
              style: const TextStyle(
                fontSize: 14,
                color: AppColors.textDark,
                height: 1.5,
              ),
            ),
            
            const SizedBox(height: 12),
            
            // 占星意義
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: (planet['color'] as Color).withValues(alpha: 0.05),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: (planet['color'] as Color).withValues(alpha: 0.2),
                ),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    '占星意義：',
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.bold,
                      color: planet['color'],
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    planet['meaning'],
                    style: TextStyle(
                      fontSize: 13,
                      color: planet['color'],
                      height: 1.4,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}

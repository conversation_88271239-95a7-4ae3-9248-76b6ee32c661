import 'package:flutter/material.dart';
import 'package:url_launcher/url_launcher.dart';

import '../../../astreal.dart';
import '../../../data/models/admin/system_announcement.dart';
import '../../../data/services/api/hidden_announcement_service.dart';


/// 系統公告對話框
/// 美化的 Dialog 版本，支援多個公告的輪播顯示
class SystemAnnouncementDialog extends StatefulWidget {
  final List<SystemAnnouncementWithId> announcements;
  final VoidCallback? onDismiss;

  const SystemAnnouncementDialog({
    super.key,
    required this.announcements,
    this.onDismiss,
  });

  /// 顯示系統公告對話框
  static Future<bool?> show(
    BuildContext context,
    List<SystemAnnouncementWithId> announcements,
  ) {
    return showDialog<bool>(
      context: context,
      barrierDismissible: false,
      builder: (context) => SystemAnnouncementDialog(
        announcements: announcements,
      ),
    );
  }

  @override
  State<SystemAnnouncementDialog> createState() => _SystemAnnouncementDialogState();
}

class _SystemAnnouncementDialogState extends State<SystemAnnouncementDialog>
    with TickerProviderStateMixin {
  late PageController _pageController;
  late AnimationController _fadeController;
  late AnimationController _scaleController;
  late Animation<double> _fadeAnimation;
  late Animation<double> _scaleAnimation;
  
  int _currentIndex = 0;
  bool _canSkip = false;
  Set<String> _hiddenAnnouncementIds = <String>{};

  @override
  void initState() {
    super.initState();
    _pageController = PageController();
    
    // 初始化動畫控制器
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _scaleController = AnimationController(
      duration: const Duration(milliseconds: 400),
      vsync: this,
    );
    
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _fadeController, curve: Curves.easeInOut),
    );
    _scaleAnimation = Tween<double>(begin: 0.8, end: 1.0).animate(
      CurvedAnimation(parent: _scaleController, curve: Curves.elasticOut),
    );
    
    // 啟動進入動畫
    _fadeController.forward();
    _scaleController.forward();
    
    // 載入已隱藏的公告列表
    _loadHiddenAnnouncements();

    // 3秒後允許跳過
    Future.delayed(const Duration(seconds: 3), () {
      if (mounted) {
        setState(() {
          _canSkip = true;
        });
      }
    });
  }

  @override
  void dispose() {
    _pageController.dispose();
    _fadeController.dispose();
    _scaleController.dispose();
    super.dispose();
  }

  /// 載入已隱藏的公告列表
  Future<void> _loadHiddenAnnouncements() async {
    try {
      final hiddenIds = await HiddenAnnouncementService.getHiddenAnnouncementIds();
      setState(() {
        _hiddenAnnouncementIds = hiddenIds;
      });
    } catch (e) {
      logger.e('載入已隱藏公告列表失敗: $e');
    }
  }

  /// 隱藏當前公告
  Future<void> _hideCurrentAnnouncement() async {
    try {
      final currentAnnouncement = widget.announcements[_currentIndex];
      final announcementId = currentAnnouncement.id;

      // 使用服務隱藏公告
      final success = await HiddenAnnouncementService.hideAnnouncement(announcementId);

      if (success) {
        // 更新本地狀態
        setState(() {
          _hiddenAnnouncementIds.add(announcementId);
        });

        // 顯示確認訊息
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: const Text('已隱藏此公告，您可以在設定中還原'),
              backgroundColor: AppColors.success,
              behavior: SnackBarBehavior.floating,
              action: SnackBarAction(
                label: '撤銷',
                textColor: Colors.white,
                onPressed: () => _unhideAnnouncement(announcementId),
              ),
            ),
          );
        }

        // 自動跳到下一個公告或關閉
        _nextAnnouncement();
      } else {
        throw Exception('隱藏公告操作失敗');
      }
    } catch (e) {
      logger.e('隱藏公告失敗: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('隱藏公告失敗: $e'),
            backgroundColor: AppColors.error,
            behavior: SnackBarBehavior.floating,
          ),
        );
      }
    }
  }

  /// 取消隱藏公告
  Future<void> _unhideAnnouncement(String announcementId) async {
    try {
      // 使用服務取消隱藏公告
      final success = await HiddenAnnouncementService.unhideAnnouncement(announcementId);

      if (success) {
        // 更新本地狀態
        setState(() {
          _hiddenAnnouncementIds.remove(announcementId);
        });

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('已取消隱藏此公告'),
              backgroundColor: AppColors.success,
              behavior: SnackBarBehavior.floating,
            ),
          );
        }
      } else {
        throw Exception('取消隱藏公告操作失敗');
      }
    } catch (e) {
      logger.e('取消隱藏公告失敗: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('取消隱藏公告失敗: $e'),
            backgroundColor: AppColors.error,
            behavior: SnackBarBehavior.floating,
          ),
        );
      }
    }
  }

  /// 關閉對話框
  void _closeDialog() async {
    await _fadeController.reverse();
    if (mounted) {
      Navigator.of(context).pop(true);
      widget.onDismiss?.call();
    }
  }

  /// 下一個公告
  void _nextAnnouncement() {
    if (_currentIndex < widget.announcements.length - 1) {
      _pageController.nextPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    } else {
      _closeDialog();
    }
  }

  /// 上一個公告
  void _previousAnnouncement() {
    if (_currentIndex > 0) {
      _pageController.previousPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  /// 啟動 URL
  Future<void> _launchUrl(String url) async {
    try {
      final uri = Uri.parse(url);
      if (await canLaunchUrl(uri)) {
        await launchUrl(uri, mode: LaunchMode.externalApplication);
      } else {
        throw Exception('無法開啟連結');
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('開啟連結失敗: $e'),
            backgroundColor: AppColors.error,
            behavior: SnackBarBehavior.floating,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _fadeAnimation,
      builder: (context, child) {
        return Opacity(
          opacity: _fadeAnimation.value,
          child: Dialog(
            backgroundColor: Colors.transparent,
            insetPadding: const EdgeInsets.all(16),
            child: AnimatedBuilder(
              animation: _scaleAnimation,
              builder: (context, child) {
                return Transform.scale(
                  scale: _scaleAnimation.value,
                  child: _buildDialogContent(),
                );
              },
            ),
          ),
        );
      },
    );
  }

  /// 構建對話框內容
  Widget _buildDialogContent() {
    return Container(
      constraints: const BoxConstraints(
        maxWidth: 500,
        maxHeight: 600,
      ),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(24),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.15),
            blurRadius: 20,
            offset: const Offset(0, 10),
          ),
        ],
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // 頂部標題欄
          _buildHeader(),
          
          // 公告內容
          Flexible(
            child: PageView.builder(
              controller: _pageController,
              onPageChanged: (index) {
                setState(() {
                  _currentIndex = index;
                });
              },
              itemCount: widget.announcements.length,
              itemBuilder: (context, index) {
                final announcementWithId = widget.announcements[index];
                return _buildAnnouncementContent(announcementWithId.announcement);
              },
            ),
          ),
          
          // 底部導航欄
          _buildFooter(),
        ],
      ),
    );
  }

  /// 構建頂部標題欄
  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: const BoxDecoration(
        color: AppColors.royalIndigo,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(24),
          topRight: Radius.circular(24),
        ),
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: Colors.white.withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(12),
            ),
            child: const Icon(
              Icons.campaign,
              color: Colors.white,
              size: 24,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  '系統公告',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                if (widget.announcements.length > 1)
                  Text(
                    '${_currentIndex + 1} / ${widget.announcements.length}',
                    style: TextStyle(
                      color: Colors.white.withValues(alpha: 0.8),
                      fontSize: 14,
                    ),
                  ),
              ],
            ),
          ),
          Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              // 隱藏按鈕
              Container(
                decoration: BoxDecoration(
                  color: Colors.white.withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(20),
                ),
                child: IconButton(
                  onPressed: _hideCurrentAnnouncement,
                  icon: const Icon(Icons.visibility_off, color: Colors.white),
                  tooltip: '不再顯示此公告',
                  style: IconButton.styleFrom(
                    padding: const EdgeInsets.all(8),
                  ),
                ),
              ),

              if (_canSkip) ...[
                const SizedBox(width: 8),
                Container(
                  decoration: BoxDecoration(
                    color: Colors.white.withValues(alpha: 0.2),
                    borderRadius: BorderRadius.circular(20),
                  ),
                  child: TextButton(
                    onPressed: _closeDialog,
                    style: TextButton.styleFrom(
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                    ),
                    child: const Text(
                      '跳過',
                      style: TextStyle(
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ),
              ],
            ],
          ),
        ],
      ),
    );
  }

  /// 構建公告內容
  Widget _buildAnnouncementContent(SystemAnnouncement announcement) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 公告標題和類型
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: announcement.type.color.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Icon(
                  announcement.type.icon,
                  color: announcement.type.color,
                  size: 28,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      announcement.title,
                      style: const TextStyle(
                        fontSize: 22,
                        fontWeight: FontWeight.bold,
                        color: AppColors.textDark,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                      decoration: BoxDecoration(
                        color: announcement.type.color.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Text(
                        announcement.type.displayName,
                        style: TextStyle(
                          fontSize: 12,
                          fontWeight: FontWeight.w600,
                          color: announcement.type.color,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          
          const SizedBox(height: 20),
          
          // 優先級標記
          if (announcement.priority != AnnouncementPriority.normal)
            Container(
              margin: const EdgeInsets.only(bottom: 16),
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
              decoration: BoxDecoration(
                color: announcement.priority.color.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(16),
                border: Border.all(
                  color: announcement.priority.color.withValues(alpha: 0.3),
                ),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(
                    announcement.type.icon,
                    size: 16,
                    color: announcement.priority.color,
                  ),
                  const SizedBox(width: 6),
                  Text(
                    announcement.priority.displayName,
                    style: TextStyle(
                      fontSize: 12,
                      fontWeight: FontWeight.w600,
                      color: announcement.priority.color,
                    ),
                  ),
                ],
              ),
            ),
          
          // 公告內容
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.grey[50],
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: Colors.grey[200]!),
            ),
            child: Text(
              announcement.content,
              style: const TextStyle(
                fontSize: 16,
                height: 1.6,
                color: AppColors.textDark,
              ),
            ),
          ),
          
          const SizedBox(height: 16),
          
          // 圖片（如果有）
          if (announcement.imageUrl != null && announcement.imageUrl!.isNotEmpty)
            Container(
              margin: const EdgeInsets.only(bottom: 16),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(12),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.1),
                    blurRadius: 8,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(12),
                child: Image.network(
                  announcement.imageUrl!,
                  width: double.infinity,
                  fit: BoxFit.cover,
                  errorBuilder: (context, error, stackTrace) {
                    return Container(
                      height: 100,
                      color: Colors.grey[200],
                      child: const Center(
                        child: Icon(Icons.broken_image, color: Colors.grey),
                      ),
                    );
                  },
                ),
              ),
            ),
          
          // 行動按鈕（如果有）
          if (announcement.actionUrl != null && announcement.actionUrl!.isNotEmpty)
            Container(
              margin: const EdgeInsets.only(bottom: 16),
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: () => _launchUrl(announcement.actionUrl!),
                icon: const Icon(Icons.open_in_new),
                label: Text(announcement.actionText ?? '了解更多'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: announcement.type.color,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 12),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
              ),
            ),
          
          // 發布時間
          Row(
            children: [
              Icon(
                Icons.schedule,
                size: 16,
                color: Colors.grey[600],
              ),
              const SizedBox(width: 6),
              Text(
                '發布時間：${_formatDateTime(announcement.createdAt)}',
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.grey[600],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// 構建底部導航欄
  Widget _buildFooter() {
    if (widget.announcements.length == 1) {
      return Container(
        padding: const EdgeInsets.all(20),
        child: SizedBox(
          width: double.infinity,
          child: ElevatedButton(
            onPressed: _closeDialog,
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.royalIndigo,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(vertical: 12),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
            child: const Text(
              '我知道了',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ),
      );
    }

    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: const BorderRadius.only(
          bottomLeft: Radius.circular(24),
          bottomRight: Radius.circular(24),
        ),
      ),
      child: Row(
        children: [
          // 上一個按鈕
          if (_currentIndex > 0)
            Expanded(
              child: OutlinedButton.icon(
                onPressed: _previousAnnouncement,
                icon: const Icon(Icons.chevron_left),
                label: const Text('上一個'),
                style: OutlinedButton.styleFrom(
                  foregroundColor: AppColors.royalIndigo,
                  side: BorderSide(color: AppColors.royalIndigo),
                  padding: const EdgeInsets.symmetric(vertical: 12),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
              ),
            )
          else
            const Expanded(child: SizedBox()),

          const SizedBox(width: 16),

          // 頁面指示器
          Row(
            children: List.generate(
              widget.announcements.length,
              (index) => AnimatedContainer(
                duration: const Duration(milliseconds: 300),
                margin: const EdgeInsets.symmetric(horizontal: 4),
                width: index == _currentIndex ? 24 : 8,
                height: 8,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(4),
                  color: index == _currentIndex
                      ? AppColors.royalIndigo
                      : Colors.grey[300],
                ),
              ),
            ),
          ),

          const SizedBox(width: 16),

          // 下一個/完成按鈕
          Expanded(
            child: ElevatedButton.icon(
              onPressed: _nextAnnouncement,
              icon: Icon(_currentIndex < widget.announcements.length - 1
                  ? Icons.chevron_right
                  : Icons.check),
              label: Text(_currentIndex < widget.announcements.length - 1
                  ? '下一個'
                  : '完成'),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.royalIndigo,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 12),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 格式化日期時間
  String _formatDateTime(DateTime dateTime) {
    return '${dateTime.year}/${dateTime.month.toString().padLeft(2, '0')}/${dateTime.day.toString().padLeft(2, '0')} '
        '${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}';
  }
}

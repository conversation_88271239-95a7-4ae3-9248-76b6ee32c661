import 'dart:async';

import 'package:flutter/foundation.dart';

import '../../astreal.dart';
import '../../data/services/api/auth_service.dart';

/// 認證 ViewModel
class AuthViewModel extends ChangeNotifier {
  AuthState _authState = const AuthState.uninitialized();
  AppUser? _currentUser;
  StreamSubscription<AppUser?>? _authStateSubscription;

  /// 當前認證狀態
  AuthState get authState => _authState;

  /// 當前用戶
  AppUser? get currentUser => _currentUser;

  /// 是否已認證
  bool get isAuthenticated => _authState.isAuthenticated;

  /// 是否認證中
  bool get isAuthenticating => _authState.isAuthenticating;

  /// 是否有錯誤
  bool get hasError => _authState.hasError;

  /// 錯誤訊息
  String? get errorMessage => _authState.errorMessage;

  AuthViewModel() {
    _initializeAuth();
  }

  /// 初始化認證狀態
  void _initializeAuth() {
    logger.i('初始化認證狀態');

    // 檢查當前用戶狀態
    final currentUser = AuthService.getCurrentUser();
    if (currentUser != null) {
      _currentUser = currentUser;
      _updateAuthState(AuthState.authenticated(currentUser.uid));
      logger.i('發現已登入用戶: ${currentUser.uid}');
    } else {
      _updateAuthState(const AuthState.unauthenticated());
      logger.i('用戶未登入');
    }

    // 監聽認證狀態變化
    _authStateSubscription = AuthService.authStateChanges.listen(
      (AppUser? user) {
        if (user != null) {
          _currentUser = user;
          _updateAuthState(AuthState.authenticated(user.uid));
          logger.i('用戶已登入: ${user.uid}');
        } else {
          _currentUser = null;
          _updateAuthState(const AuthState.unauthenticated());
          logger.i('用戶未登入');
        }
      },
      onError: (error) {
        logger.e('認證狀態監聽錯誤: $error');
        _updateAuthState(AuthState.error(error.toString()));
      },
    );
  }

  /// 更新認證狀態
  void _updateAuthState(AuthState newState) {
    if (_authState != newState) {
      logger.i('認證狀態變化: ${_authState.status} -> ${newState.status}');
      _authState = newState;
      notifyListeners();
      logger.i('已通知 UI 更新');
    } else {
      logger.d('認證狀態未變化，跳過更新');
    }
  }

  /// 使用電子郵件和密碼註冊
  Future<bool> registerWithEmailAndPassword({
    required String email,
    required String password,
    String? displayName,
  }) async {
    try {
      _updateAuthState(const AuthState.authenticating());

      final user = await AuthService.registerWithEmailAndPassword(
        email: email,
        password: password,
        displayName: displayName,
      );

      if (user != null) {
        logger.i('註冊成功: ${user.uid}');
        // 狀態更新由 authStateChanges 監聽器處理
        return true;
      }

      _updateAuthState(const AuthState.error('註冊失敗'));
      return false;
    } catch (e) {
      logger.e('註冊失敗: $e');
      _updateAuthState(AuthState.error(e.toString()));
      return false;
    }
  }

  /// 使用電子郵件和密碼登入
  Future<bool> signInWithEmailAndPassword({
    required String email,
    required String password,
  }) async {
    try {
      _updateAuthState(const AuthState.authenticating());

      final user = await AuthService.signInWithEmailAndPassword(
        email: email,
        password: password,
      );

      if (user != null) {
        logger.i('登入成功: ${user.uid}');
        // 狀態更新由 authStateChanges 監聽器處理
        return true;
      }

      _updateAuthState(const AuthState.error('登入失敗'));
      return false;
    } catch (e) {
      logger.e('登入失敗: $e');
      _updateAuthState(AuthState.error(e.toString()));
      return false;
    }
  }

  /// Google 登入
  Future<bool> signInWithGoogle() async {
    try {
      _updateAuthState(const AuthState.authenticating());

      final user = await AuthService.signInWithGoogle();

      if (user != null) {
        logger.i('Google 登入成功: ${user.uid}');
        // 狀態更新由 authStateChanges 監聽器處理
        return true;
      }

      _updateAuthState(const AuthState.error('Google 登入失敗'));
      return false;
    } catch (e) {
      logger.e('Google 登入失敗: $e');
      _updateAuthState(AuthState.error(e.toString()));
      return false;
    }
  }

  /// Apple 登入
  Future<bool> signInWithApple() async {
    try {
      _updateAuthState(const AuthState.authenticating());

      final user = await AuthService.signInWithApple();

      if (user != null) {
        logger.i('Apple 登入成功: ${user.uid}');
        // 狀態更新由 authStateChanges 監聽器處理
        return true;
      }

      _updateAuthState(const AuthState.error('Apple 登入失敗'));
      return false;
    } catch (e) {
      logger.e('Apple 登入失敗: $e');
      _updateAuthState(AuthState.error(e.toString()));
      return false;
    }
  }

  /// 匿名登入
  Future<bool> signInAnonymously() async {
    try {
      _updateAuthState(const AuthState.authenticating());

      final user = await AuthService.signInAnonymously();

      if (user != null) {
        logger.i('匿名登入成功: ${user.uid}');
        // 狀態更新由 authStateChanges 監聽器處理
        return true;
      }

      _updateAuthState(const AuthState.error('匿名登入失敗'));
      return false;
    } catch (e) {
      logger.e('匿名登入失敗: $e');
      _updateAuthState(AuthState.error(e.toString()));
      return false;
    }
  }

  /// 登出
  Future<void> signOut() async {
    try {
      _updateAuthState(const AuthState.authenticating());
      await AuthService.signOut();
      logger.i('登出成功');
    } catch (e) {
      logger.e('登出失敗: $e');
      _updateAuthState(AuthState.error(e.toString()));
    }
  }

  /// 發送密碼重設電子郵件
  Future<bool> sendPasswordResetEmail(String email) async {
    try {
      await AuthService.sendPasswordResetEmail(email: email);
      logger.i('密碼重設郵件發送成功');
      return true;
    } catch (e) {
      logger.e('發送密碼重設郵件失敗: $e');
      _updateAuthState(AuthState.error(e.toString()));
      return false;
    }
  }

  /// 重新發送電子郵件驗證
  Future<bool> sendEmailVerification() async {
    try {
      await AuthService.sendEmailVerification();
      logger.i('電子郵件驗證發送成功');
      return true;
    } catch (e) {
      logger.e('發送電子郵件驗證失敗: $e');
      _updateAuthState(AuthState.error(e.toString()));
      return false;
    }
  }

  /// 更新用戶資料
  Future<bool> updateUserProfile({
    String? displayName,
    String? photoURL,
  }) async {
    try {
      await AuthService.updateUserProfile(
        displayName: displayName,
        photoURL: photoURL,
      );
      
      // 用戶資料更新由 AuthService 處理，狀態變化由監聽器處理
      
      logger.i('用戶資料更新成功');
      return true;
    } catch (e) {
      logger.e('更新用戶資料失敗: $e');
      _updateAuthState(AuthState.error(e.toString()));
      return false;
    }
  }

  /// 刪除用戶帳戶
  Future<bool> deleteAccount() async {
    try {
      _updateAuthState(const AuthState.authenticating());
      await AuthService.deleteUser();
      logger.i('帳戶刪除成功');
      return true;
    } catch (e) {
      logger.e('刪除帳戶失敗: $e');
      _updateAuthState(AuthState.error(e.toString()));
      return false;
    }
  }

  /// 清除錯誤狀態
  void clearError() {
    if (_authState.hasError) {
      if (_currentUser != null) {
        _updateAuthState(AuthState.authenticated(_currentUser!.uid));
      } else {
        _updateAuthState(const AuthState.unauthenticated());
      }
    }
  }

  /// 重新載入用戶資料
  Future<void> reloadUser() async {
    try {
      final user = AuthService.getCurrentUser();
      // 狀態更新由 authStateChanges 監聽器處理
      logger.i('用戶資料重新載入: ${user != null ? '成功' : '未找到用戶'}');
    } catch (e) {
      logger.e('重新載入用戶資料失敗: $e');
      _updateAuthState(AuthState.error(e.toString()));
    }
  }

  @override
  void dispose() {
    _authStateSubscription?.cancel();
    super.dispose();
  }
}

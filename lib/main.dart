import 'dart:io';

import 'package:astreal/core/services/app_initialization_service.dart';
import 'package:astreal/core/services/fast_startup_service.dart';
import 'package:astreal/core/services/navigation_service.dart';
import 'package:astreal/core/utils/logger_utils.dart';
import 'package:astreal/data/models/astrology/chart_settings.dart';
import 'package:astreal/data/services/api/location_service.dart';
import 'package:astreal/data/services/api/system_announcement_service.dart';
import 'package:astreal/presentation/pages/main/starmaster_analysis_page.dart';
import 'package:astreal/presentation/pages/pages.dart';
import 'package:astreal/presentation/pages/system_announcement_display_page.dart';
import 'package:astreal/presentation/themes/app_theme.dart';
import 'package:astreal/presentation/themes/theme_provider.dart';
import 'package:astreal/presentation/viewmodels/auth_viewmodel.dart';
import 'package:astreal/presentation/viewmodels/files_viewmodel.dart';
import 'package:astreal/presentation/viewmodels/recent_charts_viewmodel.dart';
import 'package:astreal/presentation/viewmodels/recent_persons_viewmodel.dart';
import 'package:astreal/presentation/viewmodels/settings_viewmodel.dart';
import 'package:astreal/shared/utils/geocoding_service.dart';
import 'package:astreal/shared/widgets/update_dialog.dart';
import 'package:astreal/shared/widgets/web_aware_pop_scope.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import 'package:shared_preferences/shared_preferences.dart';

import 'core/utils/web_fullscreen_helper.dart' if (dart.library.io) 'core/utils/web_fullscreen_helper_stub.dart';
import 'core/utils/web_navigation_helper.dart' if (dart.library.io) 'core/utils/web_navigation_helper_stub.dart';
import 'data/services/api/remote_config_version_service.dart';
import 'presentation/pages/main/starlight_home_page.dart';
import 'presentation/pages/main/starmaster_home_page.dart';
import 'presentation/pages/onboarding/user_mode_selection_page.dart';
import 'web_helper.dart' if (dart.library.ffi) 'io_helper.dart';

/// 背景通知處理器（必須是頂層函數）
@pragma('vm:entry-point')
Future<void> firebaseMessagingBackgroundHandler(RemoteMessage message) async {
  // 在背景 isolate 中初始化 Firebase
  await Firebase.initializeApp();

  print('收到背景通知: ${message.notification?.title}');
  print('通知內容: ${message.notification?.body}');
  print('數據: ${message.data}');

  // 這裡可以處理背景通知的邏輯
  // 注意：在背景 isolate 中無法使用 UI 相關的功能
}

class MyHttpOverrides extends HttpOverrides {
  @override
  HttpClient createHttpClient(SecurityContext? context) {
    return super.createHttpClient(context)
      ..badCertificateCallback =
          (X509Certificate cert, String host, int port) => true;
  }
}

void main() async {
  // 記錄啟動開始時間
  StartupPerformanceMonitor.recordTimestamp('app_start');

  // 確保 Flutter 綁定在應用啟動前初始化
  WidgetsFlutterBinding.ensureInitialized();
  StartupPerformanceMonitor.recordTimestamp('flutter_binding_initialized');

  // 設定 HTTP 覆寫
  HttpOverrides.global = MyHttpOverrides();

  // 設定網頁全螢幕模式和導航輔助工具（如果是在網頁平台）
  if (kIsWeb) {
    await WebFullscreenHelper.initialize();
    WebNavigationHelper.initialize();
  } else {
    // 設定導航欄與狀態列樣式（僅限移動平台）
    SystemChrome.setSystemUIOverlayStyle(const SystemUiOverlayStyle(
      statusBarColor: Colors.transparent, // 狀態列透明
      statusBarIconBrightness: Brightness.dark, // 狀態列圖示深色（白底）
      systemNavigationBarColor: Colors.white, // 導航欄白色
      systemNavigationBarIconBrightness: Brightness.dark, // 導航欄圖示深色
    ));
  }
  StartupPerformanceMonitor.recordTimestamp('ui_style_configured');

  // 使用快速啟動服務（優化啟動速度）
  await FastStartupService.quickInitialize();
  StartupPerformanceMonitor.recordTimestamp('fast_startup_completed');

  // 初始化應用程式服務（在背景進行）
  await AppInitializationService.initialize();
  StartupPerformanceMonitor.recordTimestamp('app_initialization_completed');

  // 註冊背景通知處理器（必須在應用啟動時註冊）
  FirebaseMessaging.onBackgroundMessage(firebaseMessagingBackgroundHandler);

  // 註冊應用程式生命週期監聽器
  AstrealLifecycleObserver.instance.register();

  logger.i('應用啟動中...');

  // 初始化 Sweph（占星計算引擎）
  await initSweph([
    'packages/sweph/assets/ephe/seas_18.se1', // For house calc
    'packages/sweph/assets/ephe/sefstars.txt', // For star position
    'packages/sweph/assets/ephe/seasnam.txt', // For asteriods
  ]);
  StartupPerformanceMonitor.recordTimestamp('sweph_initialized');

  // 記錄應用準備完成
  StartupPerformanceMonitor.recordTimestamp('app_ready');

  // 輸出性能報告
  final performanceReport = StartupPerformanceMonitor.getPerformanceReport();
  logger.i('啟動性能報告: $performanceReport');

  runApp(const AstrealApp());
}



class AstrealApp extends StatelessWidget {
  const AstrealApp({super.key});

  @override
  Widget build(BuildContext context) {
    // Using MultiProvider for app-wide providers
    return MultiProvider(
      providers: [
        ChangeNotifierProvider(create: (_) => ThemeProvider()),
        ChangeNotifierProvider(create: (_) => AuthViewModel()),
        ChangeNotifierProvider(create: (_) => SettingsViewModel()),
        ChangeNotifierProvider(create: (_) => FilesViewModel()),
        ChangeNotifierProvider(create: (_) => RecentChartsViewModel()),
        ChangeNotifierProvider(create: (_) => RecentPersonsViewModel()),
      ],
      child: Consumer<ThemeProvider>(
        builder: (context, themeProvider, _) {
          return MaterialApp(
            title: 'AstReal',
            navigatorKey: NavigationService.navigatorKey,
            theme: AstrealAppTheme.lightTheme,
            darkTheme: AstrealAppTheme.lightTheme,
            themeMode: themeProvider.themeMode,
            home: const AppInitializer(),
            routes: {
              '/mode-selection': (context) => const UserModeSelectionPage(),
              '/main': (context) => FutureBuilder<String>(
                    future: _getUserMode(),
                    builder: (context, snapshot) {
                      final userMode = snapshot.data ?? 'starmaster';
                      return MultiProvider(
                        providers: [
                          Provider<LocationService>(
                              create: (_) => LocationService()),
                          Provider<GeocodingService>(
                              create: (_) => GeocodingService()),
                          FutureProvider<ChartSettings>(
                            create: (_) => ChartSettings.loadFromPrefs(),
                            initialData: ChartSettings(),
                          ),
                        ],
                        child: MainScreen(
                          key: ValueKey('main_screen_$userMode'),
                        ),
                      );
                    },
                  ),
            },
            debugShowCheckedModeBanner: false,
          );
        },
      ),
    );
  }
}

/// 獲取用戶模式的輔助方法
Future<String> _getUserMode() async {
  try {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString('user_mode') ?? 'starmaster';
  } catch (e) {
    return 'starmaster';
  }
}

/// 應用初始化器，決定顯示哪個頁面
class AppInitializer extends StatefulWidget {
  const AppInitializer({super.key});

  @override
  State<AppInitializer> createState() => _AppInitializerState();
}

class _AppInitializerState extends State<AppInitializer> {
  String _initializationStatus = '正在啟動應用...';
  double _progress = 0.0;
  bool _hasError = false;

  @override
  void initState() {
    super.initState();
    _initializeApp();
  }

  /// 初始化應用程式
  Future<void> _initializeApp() async {
    try {
      // 1. 檢查快速啟動狀態 (30%)
      _updateStatus('檢查啟動狀態...', 0.3);
      await _checkStartupStatus();

      // 2. 載入用戶設定 (70%)
      _updateStatus('載入用戶設定...', 0.7);
      await _checkUserMode();

      // 3. 完成初始化 (100%)
      _updateStatus('啟動完成', 1.0);
      await Future.delayed(const Duration(milliseconds: 10));
    } catch (e) {
      logger.e('應用初始化失敗: $e');
      setState(() {
        _hasError = true;
        _initializationStatus = '啟動失敗，請重試';
      });
    }
  }

  /// 檢查啟動狀態
  Future<void> _checkStartupStatus() async {
    // 檢查快速啟動服務是否已初始化
    if (!FastStartupService.isInitialized) {
      logger.w('快速啟動服務未初始化，嘗試重新初始化...');
      await FastStartupService.quickInitialize();
    }

    // 記錄初始化狀態
    final status = FastStartupService.getInitializationStatus();
    logger.i('啟動狀態檢查: $status');

    // 短暫延遲，讓用戶看到進度
    await Future.delayed(const Duration(milliseconds: 10));
  }

  /// 檢查用戶模式選擇
  Future<void> _checkUserMode() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final hasSelectedMode = prefs.getBool('has_selected_mode') ?? false;

      if (mounted) {
        if (hasSelectedMode) {
          // 用戶已選擇模式，檢查並顯示系統公告
          await _checkAndShowSystemAnnouncements();
        } else {
          // 用戶未選擇模式，清除歷史並導向模式選擇頁
          Navigator.pushNamedAndRemoveUntil(context, '/mode-selection', (route) => false);
        }
      }
    } catch (e) {
      logger.e('檢查用戶模式失敗: $e');
      // 發生錯誤時，預設導向模式選擇頁
      if (mounted) {
        Navigator.pushNamedAndRemoveUntil(context, '/mode-selection', (route) => false);
      }
    }
  }

  /// 檢查並顯示系統公告
  Future<void> _checkAndShowSystemAnnouncements() async {
    try {
      _updateStatus('檢查系統公告...', 0.8);

      // 獲取用戶類型
      final prefs = await SharedPreferences.getInstance();
      final userMode = prefs.getString('user_mode') ?? 'starmaster';

      // 獲取活躍的系統公告
      final announcements = await SystemAnnouncementService.getActiveAnnouncements(
        userType: userMode,
      );

      if (mounted) {
        // 先導航到主頁面
        Navigator.pushNamedAndRemoveUntil(context, '/main', (route) => false);

        // 等待主頁面載入完成後再顯示公告 Dialog
        if (announcements.isNotEmpty) {
          // 短暫延遲確保主頁面已完全載入
          await Future.delayed(const Duration(milliseconds: 10));

          if (mounted) {
            // 使用新的 Dialog 方式顯示系統公告
            await SystemAnnouncementDisplayPage.show(context, announcements);
          }
        }
      }
    } catch (e) {
      logger.e('檢查系統公告失敗: $e');
      // 發生錯誤時，直接導向主頁
      if (mounted) {
        Navigator.pushNamedAndRemoveUntil(context, '/main', (route) => false);
      }
    }
  }


  /// 更新初始化狀態
  void _updateStatus(String status, double progress) {
    if (mounted) {
      setState(() {
        _initializationStatus = status;
        _progress = progress;
      });
    }
  }

  /// 重試初始化
  void _retryInitialization() {
    setState(() {
      _hasError = false;
      _initializationStatus = '正在重新啟動...';
      _progress = 0.0;
    });
    _initializeApp();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // 應用 Logo 或圖標
            Container(
              width: 120,
              height: 120,
              decoration: BoxDecoration(
                color: AppColors.royalIndigo.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(60),
              ),
              child: Image.asset(
                'assets/images/flutter_launcher_icons.png',
                width: 36,
                height: 36,
              ),
            ),

            const SizedBox(height: 40),

            // 應用名稱
            const Text(
              'AstReal',
              style: TextStyle(
                fontSize: 32,
                fontWeight: FontWeight.bold,
                color: AppColors.royalIndigo,
              ),
            ),

            const SizedBox(height: 12),

            // 進度指示器
            if (!_hasError) ...[
              SizedBox(
                width: 200,
                child: LinearProgressIndicator(
                  value: _progress,
                  backgroundColor: AppColors.royalIndigo.withValues(alpha: 0.2),
                  valueColor: const AlwaysStoppedAnimation<Color>(
                      AppColors.royalIndigo),
                ),
              ),
              const SizedBox(height: 16),
              Text(
                _initializationStatus,
                style: const TextStyle(
                  fontSize: 14,
                  color: AppColors.royalIndigo,
                ),
                textAlign: TextAlign.center,
              ),
            ] else ...[
              // 錯誤狀態
              const Icon(
                Icons.error_outline,
                size: 48,
                color: Colors.red,
              ),

              const SizedBox(height: 16),

              Text(
                _initializationStatus,
                style: const TextStyle(
                  fontSize: 14,
                  color: Colors.red,
                ),
                textAlign: TextAlign.center,
              ),

              const SizedBox(height: 24),

              ElevatedButton(
                onPressed: _retryInitialization,
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.royalIndigo,
                  foregroundColor: Colors.white,
                ),
                child: const Text('重試'),
              ),
            ],
          ],
        ),
      ),
    );
  }
}

class MainScreen extends StatefulWidget {
  final int? initialIndex;

  const MainScreen({super.key, this.initialIndex});

  @override
  State<MainScreen> createState() => _MainScreenState();
}

class _MainScreenState extends State<MainScreen> with WidgetsBindingObserver {
  int _selectedIndex = 0;
  String _userMode = 'starmaster'; // 默認為專業模式

  // 頁面列表
  List<Widget> _pages = [];

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);

    // 設定初始索引
    if (widget.initialIndex != null) {
      _selectedIndex = widget.initialIndex!;
    }

    _loadUserMode();

    // 初始化 RecentPersonsViewModel
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final recentPersonsViewModel =
          Provider.of<RecentPersonsViewModel>(context, listen: false);
      recentPersonsViewModel.initialize();

      // 檢查版本更新
      _checkForUpdates();
    });
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);
    // 當應用從後台回到前台時，重新載入用戶模式
    if (state == AppLifecycleState.resumed) {
      _loadUserMode();
    }
  }

  /// 載入用戶模式
  Future<void> _loadUserMode() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final userMode = prefs.getString('user_mode') ?? 'starmaster';

      // 檢查模式是否發生變化
      final bool modeChanged = _userMode != userMode;

      // 更新 ThemeProvider 的用戶模式
      if (mounted) {
        final themeProvider =
            Provider.of<ThemeProvider>(context, listen: false);
        themeProvider.setUserMode(userMode);
      }

      setState(() {
        _userMode = userMode;
        // 如果模式發生變化，重置到首頁
        if (modeChanged) {
          _selectedIndex = 0;
        }
        _initializePages();
      });

      logger.i('用戶模式載入: $userMode${modeChanged ? ' (模式已變更)' : ''}');
    } catch (e) {
      logger.e('載入用戶模式失敗: $e');
      _initializePages();
    }
  }

  /// 初始化頁面列表
  void _initializePages() {
    if (_userMode == 'starlight') {
      // 初心者模式頁面
      if (kDebugMode) {
        _pages = [
          const StarlightHomePage(), // 初心者主頁
          const FilesPage(),
          const FinancePage(),
          const StarlightAnalysisPage(),
          const SettingsPage(),
        ];
      } else {
        _pages = [
          const StarlightHomePage(), // 初心者主頁
          const FilesPage(),
          const SettingsPage(),
        ];
      }
    } else {
      // 專業模式頁面
      if (kDebugMode) {
        _pages = [
          const StarmasterHomePage(), // 專業主頁
          const FilesPage(),
          const FinancePage(),
          const StarMasterAnalysisPage(),
          const SettingsPage(),
        ];
      } else {
        _pages = [
          const StarmasterHomePage(), // 專業主頁
          const FilesPage(),
          const SettingsPage(),
        ];
      }
    }
  }

  void _onItemTapped(int index) {
    setState(() {
      _selectedIndex = index;
    });
  }

  /// 檢查版本更新（使用 Remote Config）
  Future<void> _checkForUpdates() async {
    try {
      logger.i('應用啟動時檢查版本更新（使用 Remote Config）...');

      // 延遲一秒再檢查，確保UI已經完全載入
      await Future.delayed(const Duration(milliseconds: 100));

      // 優先使用 Remote Config 版本檢查
      final remoteConfigStatus =
          await RemoteConfigVersionService.checkForUpdates();

      if (!mounted) return;

      if (remoteConfigStatus.isSuccess) {
        if (remoteConfigStatus.needsUpdate &&
            remoteConfigStatus.latestVersion != null) {
          await UpdateDialog.show(
            context,
            versionInfo: remoteConfigStatus.latestVersion!,
            isForceUpdate: remoteConfigStatus.isForceUpdate,
            onLater: () {
              logger.i('用戶選擇稍後更新');
            },
            onUpdate: () {
              logger.i('用戶選擇立即更新');
            },
          );
        } else {
          logger.i('當前版本是最新的（Remote Config）');
        }
      } else {
        // Remote Config 失敗，嘗試使用舊的版本檢查服務作為降級方案
        logger.w(
            'Remote Config 版本檢查失敗，嘗試使用舊服務: ${remoteConfigStatus.errorMessage}');
      }
    } catch (e) {
      logger.e('檢查版本更新時發生錯誤: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    // 如果頁面列表為空，顯示載入畫面
    if (_pages.isEmpty) {
      return const Scaffold(
        body: Center(
          child: CircularProgressIndicator(),
        ),
      );
    }

    // 確保選擇的索引在有效範圍內
    final int safeIndex = _selectedIndex < _pages.length ? _selectedIndex : 0;

    return Consumer<ThemeProvider>(
      builder: (context, themeProvider, child) {
        return Theme(
            data: themeProvider.themeData,
            child: WebAwarePopScope(
              routeName: '/main',
              onBackPressed: () {
                // 在主頁面，返回按鈕不做任何操作或顯示退出確認
                if (kIsWeb) {
                  logger.d('🌐 在主頁面，忽略返回操作');
                }
              },
              child: Scaffold(
                backgroundColor: themeProvider.userMode == 'starlight'
                    ? AppColors.lightCornsilk
                    : AppColors.pastelSkyBlue,
                body: _pages[safeIndex],
                bottomNavigationBar: NavigationBar(
                destinations: const [
                  NavigationDestination(
                    icon: Icon(Icons.home_outlined),
                    selectedIcon: Icon(Icons.home),
                    label: '首頁',
                  ),
                  NavigationDestination(
                    icon: Icon(Icons.folder_outlined),
                    selectedIcon: Icon(Icons.folder),
                    label: '檔案',
                  ),
                  if (kDebugMode)
                    NavigationDestination(
                      icon: Icon(Icons.account_balance_wallet_outlined),
                      selectedIcon: Icon(Icons.account_balance_wallet),
                      label: '理財',
                    ),
                  if (kDebugMode)
                    NavigationDestination(
                      icon: Icon(Icons.analytics_outlined),
                      selectedIcon: Icon(Icons.analytics),
                      label: '分析',
                    ),
                  // 只在開發模式下顯示預約頁
                  // if (kDebugMode)
                  //   NavigationDestination(
                  //     icon: Icon(Icons.event_outlined),
                  //     selectedIcon: Icon(Icons.event),
                  //     label: '預約',
                  //   ),
                  NavigationDestination(
                    icon: Icon(Icons.settings_outlined),
                    selectedIcon: Icon(Icons.settings),
                    label: '設定',
                  ),
                ],
                selectedIndex: safeIndex,
                onDestinationSelected: _onItemTapped,
                elevation: 0,
                height: 65,
                labelBehavior: NavigationDestinationLabelBehavior.alwaysShow,
              ),
            ),
          ));
      },
    );
  }
}

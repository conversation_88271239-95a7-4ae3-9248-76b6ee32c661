import '../../core/utils/logger_utils.dart';
import '../models/user/user_profile.dart';
import '../repositories/user_profile_credits_repository.dart';
import '../repositories/user_profile_repository.dart';

/// 統一的用戶檔案服務
/// 整合所有與 user_profiles 相關的功能
class UserProfileUnifiedService {
  
  // ==================== 基本 CRUD 操作 ====================

  /// 根據 UID 獲取用戶檔案
  static Future<UserProfile?> getUserById(String userId) async {
    return await UserProfileRepository.getUserById(userId);
  }

  /// 創建用戶檔案
  static Future<void> createUser(String userId, UserProfile userProfile) async {
    await UserProfileRepository.createUser(userId, userProfile);
  }

  /// 更新用戶檔案
  static Future<void> updateUser(String userId, UserProfile userProfile) async {
    await UserProfileRepository.updateUser(userId, userProfile);
  }

  /// 刪除用戶檔案
  static Future<void> deleteUser(String userId) async {
    await UserProfileRepository.deleteUser(userId);
  }

  /// 檢查用戶是否存在
  static Future<bool> userExists(String userId) async {
    final user = await getUserById(userId);
    return user != null;
  }

  // ==================== 批量操作 ====================

  /// 獲取所有用戶
  static Future<List<UserProfile>> getAllUsers({
    int? limit,
    String? orderBy = 'created_at',
    bool descending = true,
  }) async {
    return await UserProfileRepository.getAllUsers(
      limit: limit,
      orderBy: orderBy,
      descending: descending,
    );
  }

  /// 批量更新用戶
  static Future<void> batchUpdateUsers(
    List<String> userIds,
    Map<String, dynamic> updateData,
  ) async {
    await UserProfileRepository.batchUpdateUsers(userIds, updateData);
  }

  /// 批量創建用戶
  static Future<Map<String, bool>> batchCreateUsers(
    Map<String, UserProfile> users,
  ) async {
    final results = <String, bool>{};
    
    for (final entry in users.entries) {
      try {
        await createUser(entry.key, entry.value);
        results[entry.key] = true;
      } catch (e) {
        logger.e('批量創建用戶失敗: ${entry.key} - $e');
        results[entry.key] = false;
      }
    }
    
    return results;
  }

  // ==================== 查詢操作 ====================

  /// 搜尋用戶
  static Future<List<UserProfile>> searchUsers({
    String? email,
    String? displayName,
    bool? emailVerified,
    bool? isAnonymous,
    bool? isAdmin,
    int? limit = 50,
  }) async {
    return await UserProfileRepository.searchUsers(
      email: email,
      displayName: displayName,
      emailVerified: emailVerified,
      isAnonymous: isAnonymous,
      isAdmin: isAdmin,
      limit: limit,
    );
  }

  /// 根據電子郵件查找用戶
  static Future<UserProfile?> getUserByEmail(String email) async {
    final users = await searchUsers(email: email, limit: 1);
    return users.isNotEmpty ? users.first : null;
  }

  /// 根據顯示名稱查找用戶
  static Future<List<UserProfile>> getUsersByDisplayName(String displayName) async {
    return await searchUsers(displayName: displayName);
  }

  /// 獲取管理員用戶列表
  static Future<List<String>> getAdminUserIds() async {
    return await UserProfileRepository.getAdminUserIds();
  }

  /// 獲取管理員用戶檔案列表
  static Future<List<UserProfile>> getAdminUsers() async {
    final adminIds = await getAdminUserIds();
    final adminUsers = <UserProfile>[];
    
    for (final adminId in adminIds) {
      final user = await getUserById(adminId);
      if (user != null) {
        adminUsers.add(user);
      }
    }
    
    return adminUsers;
  }

  // ==================== 統計操作 ====================

  /// 獲取用戶統計資料
  static Future<Map<String, int>> getUserStats() async {
    return await UserProfileRepository.getUserStats();
  }

  /// 獲取解讀次數統計
  static Future<Map<String, dynamic>> getCreditsStatistics() async {
    return await UserProfileCreditsRepository.getCreditsStatistics();
  }

  // ==================== 解讀次數管理 ====================

  /// 獲取用戶的解讀次數
  static Future<int> getInterpretationCredits(String userId) async {
    return await UserProfileCreditsRepository.getInterpretationCredits(userId);
  }

  /// 添加解讀次數
  static Future<bool> addInterpretationCredits(String userId, int count) async {
    return await UserProfileCreditsRepository.addInterpretationCredits(userId, count);
  }

  /// 使用一次解讀次數
  static Future<bool> useInterpretationCredit(String userId) async {
    return await UserProfileCreditsRepository.useInterpretationCredit(userId);
  }

  /// 批量使用解讀次數
  static Future<bool> useInterpretationCredits(String userId, int count) async {
    return await UserProfileCreditsRepository.useInterpretationCredits(userId, count);
  }

  /// 設置解讀次數
  static Future<bool> setInterpretationCredits(String userId, int count) async {
    return await UserProfileCreditsRepository.setInterpretationCredits(userId, count);
  }

  /// 重置解讀次數為 0
  static Future<bool> resetInterpretationCredits(String userId) async {
    return await UserProfileCreditsRepository.resetInterpretationCredits(userId);
  }

  /// 批量添加解讀次數
  static Future<Map<String, bool>> batchAddInterpretationCredits(
    Map<String, int> userCredits,
  ) async {
    return await UserProfileCreditsRepository.batchAddInterpretationCredits(userCredits);
  }

  /// 批量設置解讀次數
  static Future<Map<String, bool>> batchSetInterpretationCredits(
    Map<String, int> userCredits,
  ) async {
    return await UserProfileCreditsRepository.batchSetInterpretationCredits(userCredits);
  }

  // ==================== 用戶狀態管理 ====================

  /// 設置用戶為管理員
  static Future<bool> setUserAdmin(String userId, bool isAdmin) async {
    try {
      final user = await getUserById(userId);
      if (user == null) {
        logger.w('用戶不存在: $userId');
        return false;
      }

      final updatedUser = user.copyWith(
        isAdmin: isAdmin,
        updatedAt: DateTime.now(),
      );

      await updateUser(userId, updatedUser);
      logger.i('設置用戶管理員狀態成功: $userId -> $isAdmin');
      return true;
    } catch (e) {
      logger.e('設置用戶管理員狀態失敗: $e');
      return false;
    }
  }

  /// 驗證用戶電子郵件
  static Future<bool> verifyUserEmail(String userId, bool isVerified) async {
    try {
      final user = await getUserById(userId);
      if (user == null) {
        logger.w('用戶不存在: $userId');
        return false;
      }

      final updatedUser = user.copyWith(
        emailVerified: isVerified,
        updatedAt: DateTime.now(),
      );

      await updateUser(userId, updatedUser);
      logger.i('設置用戶電子郵件驗證狀態成功: $userId -> $isVerified');
      return true;
    } catch (e) {
      logger.e('設置用戶電子郵件驗證狀態失敗: $e');
      return false;
    }
  }

  /// 更新用戶登入資訊
  static Future<bool> updateUserLoginInfo(String userId) async {
    try {
      final user = await getUserById(userId);
      if (user == null) {
        logger.w('用戶不存在: $userId');
        return false;
      }

      // 使用專門的登入更新方法，避免影響其他欄位
      final loginUpdateData = user.toFirestoreJsonForLogin();
      await UserProfileRepository.updateUserLoginData(userId, loginUpdateData);

      logger.i('更新用戶登入資訊成功: $userId');
      return true;
    } catch (e) {
      logger.e('更新用戶登入資訊失敗: $e');
      return false;
    }
  }

  /// 完成用戶檔案設置
  static Future<bool> completeUserProfile(String userId) async {
    try {
      final user = await getUserById(userId);
      if (user == null) {
        logger.w('用戶不存在: $userId');
        return false;
      }

      final updatedUser = user.copyWith(
        profileCompleted: true,
        updatedAt: DateTime.now(),
      );

      await updateUser(userId, updatedUser);
      logger.i('完成用戶檔案設置: $userId');
      return true;
    } catch (e) {
      logger.e('完成用戶檔案設置失敗: $e');
      return false;
    }
  }

  // ==================== 即時監聽 ====================

  /// 監聽用戶資料變更
  static Stream<List<UserProfile>> watchUsers({
    int? limit,
    String? orderBy = 'created_at',
    bool descending = true,
  }) {
    return UserProfileRepository.watchUsers(
      limit: limit,
      orderBy: orderBy,
      descending: descending,
    );
  }

  /// 監聽單個用戶資料變更
  static Stream<UserProfile?> watchUser(String userId) {
    return UserProfileRepository.watchUser(userId);
  }

  // ==================== 初始化與遷移 ====================

  /// 初始化用戶檔案（如果不存在則創建）
  static Future<UserProfile> initializeUserProfile(String userId, {
    String? email,
    String? displayName,
    bool isAnonymous = false,
  }) async {
    try {
      // 檢查用戶是否已存在
      final existingUser = await getUserById(userId);
      if (existingUser != null) {
        logger.d('用戶檔案已存在: $userId');
        return existingUser;
      }

      // 創建新用戶檔案
      final now = DateTime.now();
      final newUser = UserProfile(
        userId: userId,
        email: email,
        displayName: displayName ?? (isAnonymous ? '匿名用戶' : '新用戶'),
        isAnonymous: isAnonymous,
        createdAt: now,
        updatedAt: now,
        profileCompleted: false,
        lastLoginAt: now,
        loginCount: 1,
        interpretationCredits: 0,
        creditsLastUpdated: now,
      );

      await createUser(userId, newUser);
      logger.i('初始化用戶檔案成功: $userId');
      return newUser;
    } catch (e) {
      logger.e('初始化用戶檔案失敗: $e');
      rethrow;
    }
  }
}

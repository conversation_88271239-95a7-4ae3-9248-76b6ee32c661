import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'dart:math';

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:crypto/crypto.dart';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'package:google_sign_in/google_sign_in.dart';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';
import 'package:sign_in_with_apple/sign_in_with_apple.dart';

import '../../../core/config/firebase_config_windows.dart';
import '../../../core/constants/firebase_collections.dart';
import '../../../core/utils/logger_utils.dart';
import '../../../shared/utils/email_auth_diagnostics.dart';
import '../../../shared/utils/utils.dart';
import '../../models/user/app_user.dart';
import 'admin_service.dart';
import 'user_data_initialization_service.dart';

/// Firebase 認證服務類，支援多種登入方式
class FirebaseAuthService {
  static const String _tokenKey = 'firebase_auth_token';
  static const String _userKey = 'firebase_user_data';
  static const String _refreshTokenKey = 'firebase_refresh_token';

  // 設備級匿名帳號管理相關鍵值
  static const String _deviceAnonymousUidKey = 'device_anonymous_uid';
  static const String _deviceAnonymousUserKey = 'device_anonymous_user_data';
  static const String _isDeviceAnonymousKey = 'is_device_anonymous';
  static const String _deviceFingerprintKey = 'device_fingerprint';

  // Firestore 集合名稱
  static const String _deviceAnonymousCollection = 'device_anonymous_accounts';

  static AppUser? _currentUser;
  static String? _idToken;
  static String? _refreshToken;

  // Google 登入配置 - 針對不同平台進行優化配置
  static final GoogleSignIn _googleSignIn = GoogleSignIn(
    scopes: [
      'email',
      'profile',
      // 移除 openid scope，讓 Firebase SDK 自動處理
    ],
    // Web 平台需要明確指定客戶端 ID，其他平台讓 Firebase SDK 自動處理
    clientId: kIsWeb
        ? '************-4kcl1ng61mk48gtqns86f8irlqsiab48.apps.googleusercontent.com'
        : null,
  );

  /// 檢查是否應該使用 REST API
  static bool _shouldUseRestApi() {
    try {
      // 優先使用 Firebase SDK，只在特殊情況下使用 REST API
      if (Platform.isWindows) {
        return true;
      } else if (kIsWeb) {
        return false; // Firebase 可用，優先使用 SDK
      } else if (Platform.isIOS) {
        return false; // Firebase 可用，優先使用 SDK
      } else if (Platform.isAndroid) {
        return false; // Firebase 可用，優先使用 SDK
      } else if (Platform.isMacOS) {
        return false; // Firebase 可用，優先使用 SDK
      } else if (Platform.isLinux) {
        return false; // Firebase 可用，優先使用 SDK
      } else {
        return false; // Firebase 可用，優先使用 SDK
      }
      // 檢查 Firebase 是否可用
      try {
        final _ = FirebaseAuth.instance.app;
        logger.i('Firebase SDK 可用，使用 SDK 模式');
        return false; // Firebase 可用，優先使用 SDK
      } catch (e) {
        logger.w('Firebase SDK 不可用: $e');
      }

      // Windows 平台作為備用使用 REST API
      if (!kIsWeb && Platform.isWindows) {
        logger.i('Windows 平台，使用 REST API 模式');
        return true;
      }

      // 其他情況下仍嘗試使用 SDK
      logger.w('Firebase SDK 檢查失敗，但仍嘗試使用 SDK 模式');
      return false;
    } catch (e) {
      logger.w('平台檢測失敗，使用 REST API: $e');
      return true; // 檢測失敗時使用 REST API
    }
  }

  /// 初始化服務
  static Future<void> initialize() async {
    try {
      logger.i('開始初始化 Firebase 認證服務...');

      // 檢查 Firebase 是否已初始化
      bool firebaseAvailable = false;
      try {
        // 檢查 Firebase 應用是否存在
        final apps = Firebase.apps;
        final hasDefaultApp = apps.any((app) => app.name == '[DEFAULT]');

        if (hasDefaultApp) {
          // 嘗試訪問 Firebase Auth 實例
          final _ = FirebaseAuth.instance.app;
          firebaseAvailable = true;
          logger.i('Firebase 已初始化，認證服務可用');
        } else {
          logger.w('Firebase 默認應用不存在');
        }
      } catch (e) {
        logger.w('Firebase 檢查失敗: $e');
        logger.w('將使用 REST API 模式');
      }

      if (!firebaseAvailable) {
        logger.i('Firebase 不可用，強制使用 REST API 模式');
      }

      // 初始化 Google Sign-In
      try {
        // 檢查 Google Sign-In 是否已經初始化
        if (await _googleSignIn.isSignedIn()) {
          logger.i('檢測到已有 Google 登入狀態');
        }
      } catch (e) {
        logger.w('Google Sign-In 初始化檢查失敗: $e');
      }

      final prefs = await SharedPreferences.getInstance();
      final useFirebaseSDK = prefs.getBool('_useFirebaseSDK') ?? false;

      if (useFirebaseSDK) {
        // 使用 Firebase SDK 恢復會話
        logger.i('檢測到 Firebase SDK 會話，嘗試恢復...');
        final firebaseUser = FirebaseAuth.instance.currentUser;
        if (firebaseUser != null) {
          _currentUser = await _createAppUserWithAdminCheck(
            uid: firebaseUser.uid,
            email: firebaseUser.email,
            displayName: firebaseUser.displayName,
            photoURL: firebaseUser.photoURL,
            emailVerified: firebaseUser.emailVerified,
            isAnonymous: firebaseUser.isAnonymous,
            createdAt: firebaseUser.metadata.creationTime ?? DateTime.now(),
          );
          logger.i('Firebase SDK 用戶會話已恢復: ${_currentUser?.email}');
        }
      } else {
        // 使用 REST API 恢復會話
        _idToken = prefs.getString(_tokenKey);
        _refreshToken = prefs.getString(_refreshTokenKey);

        if (_idToken != null) {
          final userData = prefs.getString(_userKey);
          if (userData != null) {
            try {
              final userJson = json.decode(userData);
              _currentUser = AppUser.fromJson(userJson);
              logger.i('Firebase REST API 用戶會話已恢復: ${_currentUser?.email}');

              // 在背景驗證 token 是否仍然有效
              _validateToken().catchError((e) {
                logger.w('Token 驗證失敗: $e');
              });
            } catch (e) {
              logger.e('用戶數據解析失敗: $e');
              // 清除無效的用戶數據
              await _clearUserSession();
            }
          }
        }
      }

      logger.i('Firebase 認證服務初始化完成');
    } catch (e) {
      logger.e('Firebase 認證服務初始化失敗: $e');
      // 不重新拋出異常，避免影響應用啟動
    }
  }

  /// 獲取當前用戶
  static AppUser? getCurrentUser() {
    return _currentUser;
  }

  /// 認證狀態變化流
  static Stream<AppUser?> get authStateChanges {
    // 使用 StreamController 來控制狀態變化的觸發
    return _authStateController.stream;
  }

  // 認證狀態變化控制器
  static final StreamController<AppUser?> _authStateController =
      StreamController<AppUser?>.broadcast();

  /// 觸發認證狀態變化
  static void _notifyAuthStateChange(AppUser? user) {
    if (!_authStateController.isClosed) {
      _authStateController.add(user);
    }
  }

  /// 關閉認證狀態流（通常在應用關閉時調用）
  static void dispose() {
    _authStateController.close();
  }

  /// 使用電子郵件和密碼註冊
  static Future<AppUser?> registerWithEmailAndPassword({
    required String email,
    required String password,
    String? displayName,
  }) async {
    try {
      logger.i('開始 Firebase 用戶註冊: $email');

      if (_shouldUseRestApi()) {
        return await _registerWithEmailPasswordViaRestApi(
            email, password, displayName);
      } else {
        return await _registerWithEmailPasswordViaSDK(
            email, password, displayName);
      }
    } catch (e) {
      logger.e('Firebase 用戶註冊失敗: $e');
      rethrow;
    }
  }

  /// 使用電子郵件和密碼登入
  static Future<AppUser?> signInWithEmailAndPassword({
    required String email,
    required String password,
  }) async {
    try {
      logger.i('開始 Firebase 用戶登入: $email');

      // 在登入其他帳號前，先保存當前的匿名用戶狀態
      await _preserveAnonymousUserBeforeOtherSignIn();

      if (_shouldUseRestApi()) {
        return await _signInWithEmailPasswordViaRestApi(email, password);
      } else {
        return await _signInWithEmailPasswordViaSDK(email, password);
      }
    } catch (e) {
      logger.e('Firebase 用戶登入失敗: $e');
      rethrow;
    }
  }

  /// Google 登入（區分 Web 和移動平台）
  static Future<AppUser?> signInWithGoogle() async {
    try {
      logger.i('開始 Google 登入 - 平台: ${kIsWeb ? "Web" : "Mobile"}');

      // 在登入其他帳號前，先保存當前的匿名用戶狀態
      await _preserveAnonymousUserBeforeOtherSignIn();

      if (kIsWeb) {
        // Web 平台：使用 FirebaseAuth + signInWithPopup()
        return await _signInWithGoogleWeb();
      } else {
        // 移動平台：使用 GoogleSignIn().signIn() + FirebaseAuth.signInWithCredential()
        return await _signInWithGoogleMobile();
      }
    } catch (e) {
      logger.e('Google 登入失敗: $e');
      rethrow;
    }
  }

  /// Web 平台 Google 登入
  static Future<AppUser?> _signInWithGoogleWeb() async {
    try {
      logger.i('使用 Web 平台 Google 登入');

      // 創建 Google Auth Provider
      final GoogleAuthProvider googleProvider = GoogleAuthProvider();

      // 設定 OAuth 範圍（可選）
      googleProvider.addScope('email');
      googleProvider.addScope('profile');

      // 使用 signInWithPopup 進行登入
      final UserCredential userCredential =
          await FirebaseAuth.instance.signInWithPopup(googleProvider);
      final User? firebaseUser = userCredential.user;

      if (firebaseUser == null) {
        logger.w('Web Google 登入失敗：未返回用戶信息');
        return null;
      }

      logger.i('Web Google 登入成功: ${firebaseUser.email}');

      // 創建 AppUser
      final user = AppUser(
        uid: firebaseUser.uid,
        email: firebaseUser.email,
        displayName: firebaseUser.displayName,
        photoURL: firebaseUser.photoURL,
        emailVerified: firebaseUser.emailVerified,
        isAnonymous: firebaseUser.isAnonymous,
        createdAt: firebaseUser.metadata.creationTime ?? DateTime.now(),
      );

      // 保存用戶會話
      await _saveUserSessionSDK(user);

      // 初始化新用戶的免費試用記錄
      await UserDataInitializationService.initializeNewUserData(
        user.uid,
        email: user.email,
        displayName: user.displayName,
      );

      logger.i('Web Google 登入完成: ${user.uid}');
      return user;
    } on FirebaseAuthException catch (e) {
      logger.e('Web Google 登入 Firebase 異常: ${e.code} - ${e.message}');

      switch (e.code) {
        case 'popup-closed-by-user':
          logger.i('用戶關閉了登入彈窗');
          return null;
        case 'popup-blocked':
          throw Exception('瀏覽器阻止了登入彈窗，請允許彈窗並重試');
        case 'cancelled-popup-request':
          logger.i('用戶取消了登入請求');
          return null;
        case 'network-request-failed':
          throw Exception('網路連接失敗，請檢查網路設定');
        default:
          throw Exception('Google 登入失敗：${_getFirebaseErrorMessage(e.code)}');
      }
    } catch (e) {
      logger.e('Web Google 登入失敗: $e');

      final errorString = e.toString().toLowerCase();
      if (errorString.contains('popup')) {
        throw Exception('登入彈窗被阻止或關閉，請重試');
      } else if (errorString.contains('network')) {
        throw Exception('網路連接失敗，請檢查網路設定');
      }

      rethrow;
    }
  }

  /// 移動平台 Google 登入
  static Future<AppUser?> _signInWithGoogleMobile() async {
    try {
      logger.i('使用移動平台 Google 登入');

      // 先檢查是否已經登入，如果是則先登出
      if (await _googleSignIn.isSignedIn()) {
        logger.i('檢測到已有 Google 登入，先登出');
        await _googleSignIn.signOut();
      }

      // 執行 Google 登入
      final GoogleSignInAccount? googleUser = await _googleSignIn.signIn();
      if (googleUser == null) {
        logger.i('用戶取消 Google 登入');
        return null;
      }

      logger.i('Google 用戶登入成功: ${googleUser.email}');

      // 獲取認證信息
      logger.i('開始獲取 Google 認證信息...');
      final GoogleSignInAuthentication googleAuth =
          await googleUser.authentication;

      // 詳細檢查認證信息
      logger.i('Google 認證信息狀態:');
      logger.i(
          '- ID Token: ${googleAuth.idToken != null ? "存在 (${googleAuth.idToken!.length} 字符)" : "為空"}');
      logger.i(
          '- Access Token: ${googleAuth.accessToken != null ? "存在 (${googleAuth.accessToken!.length} 字符)" : "為空"}');

      if (googleAuth.idToken == null) {
        logger.e('Google ID Token 為空 - 可能的原因:');
        logger.e('1. Google 服務配置問題');
        logger.e('2. 網路連接問題');
        logger.e('3. Google Play 服務版本過舊');
        logger.e('4. SHA-1 指紋配置錯誤');

        // 嘗試重新獲取認證信息
        logger.i('嘗試重新獲取認證信息...');
        try {
          await googleUser.clearAuthCache();
          final retryAuth = await googleUser.authentication;
          if (retryAuth.idToken != null) {
            logger.i('重新獲取成功');
            // 使用 Firebase 認證憑證
            return await _signInWithGoogleCredential(retryAuth);
          }
        } catch (retryError) {
          logger.e('重新獲取認證信息失敗: $retryError');
        }

        throw Exception('Google 認證失敗：無法獲取有效的認證令牌。請檢查網路連接並重試，或聯繫技術支援');
      }

      if (googleAuth.accessToken == null) {
        logger.e('Google Access Token 為空');
        throw Exception('Google 認證失敗：無法獲取訪問令牌。請重試');
      }

      logger.i('Google 認證信息獲取成功');

      // 使用 FirebaseAuth.signInWithCredential()
      return await _signInWithGoogleCredential(googleAuth);
    } on PlatformException catch (e) {
      logger.e('移動平台 Google 登入異常: ${e.code} - ${e.message}');

      switch (e.code) {
        case 'sign_in_canceled':
          logger.i('用戶取消 Google 登入');
          return null;
        case 'sign_in_failed':
          throw Exception('Google 登入失敗，請稍後再試');
        case 'network_error':
          throw Exception('網路連接失敗，請檢查網路設定');
        case 'sign_in_required':
          throw Exception('需要重新登入 Google 帳戶');
        default:
          throw Exception('Google 登入失敗：${e.message ?? e.code}');
      }
    } catch (e) {
      logger.e('移動平台 Google 登入失敗: $e');

      // 處理其他類型的錯誤
      final errorString = e.toString().toLowerCase();
      if (errorString.contains('network')) {
        throw Exception('網路連接失敗，請檢查網路設定');
      } else if (errorString.contains('canceled') ||
          errorString.contains('cancelled')) {
        logger.i('用戶取消 Google 登入');
        return null;
      } else if (errorString.contains('timeout')) {
        throw Exception('Google 登入超時，請稍後再試');
      }

      rethrow;
    }
  }

  /// 使用 Google 認證信息登入 Firebase（移動平台專用）
  static Future<AppUser?> _signInWithGoogleCredential(
      GoogleSignInAuthentication googleAuth) async {
    try {
      logger.i('使用 Google 認證信息登入 Firebase');

      // 創建 Firebase 認證憑證
      final credential = GoogleAuthProvider.credential(
        accessToken: googleAuth.accessToken,
        idToken: googleAuth.idToken,
      );

      // 使用認證憑證登入 Firebase
      final userCredential =
          await FirebaseAuth.instance.signInWithCredential(credential);
      final firebaseUser = userCredential.user;

      if (firebaseUser == null) {
        throw Exception('Firebase Google 登入失敗：未返回用戶信息');
      }

      logger.i('Firebase Google 登入成功: ${firebaseUser.email}');

      // 創建 AppUser
      final user = AppUser(
        uid: firebaseUser.uid,
        email: firebaseUser.email,
        displayName: firebaseUser.displayName,
        photoURL: firebaseUser.photoURL,
        emailVerified: firebaseUser.emailVerified,
        isAnonymous: firebaseUser.isAnonymous,
        createdAt: firebaseUser.metadata.creationTime ?? DateTime.now(),
      );

      // 保存用戶會話
      await _saveUserSessionSDK(user);

      // 初始化新用戶的免費試用記錄
      await UserDataInitializationService.initializeNewUserData(
        user.uid,
        email: user.email,
        displayName: user.displayName,
      );

      logger.i('移動平台 Google 登入完成: ${user.uid}');
      return user;
    } on FirebaseAuthException catch (e) {
      logger.e('Firebase Google 認證失敗: ${e.code} - ${e.message}');

      // 特殊處理 invalid-credential 錯誤
      if (e.code == 'invalid-credential') {
        logger.w('檢測到 invalid-credential 錯誤，嘗試清除 Google 認證快取...');
        try {
          await _googleSignIn.signOut();
          logger.i('Google 認證快取已清除，請重新嘗試登入');
        } catch (signOutError) {
          logger.e('清除 Google 認證快取失敗: $signOutError');
        }
      }

      throw Exception(_getFirebaseErrorMessage(e.code));
    } catch (e) {
      logger.e('Google 認證登入失敗: $e');
      rethrow;
    }
  }

  /// Apple 登入
  static Future<AppUser?> signInWithApple() async {
    try {
      logger.i('開始 Apple 登入');

      // 在登入其他帳號前，先保存當前的匿名用戶狀態
      await _preserveAnonymousUserBeforeOtherSignIn();

      // 檢查平台支援
      if (!kIsWeb && !Platform.isIOS && !Platform.isMacOS) {
        logger.w('當前平台不支援 Apple 登入');
        throw Exception('Apple 登入僅支援 iOS 和 macOS 平台');
      }

      // 檢查 Apple 登入可用性
      logger.i('檢查 Apple 登入可用性...');
      final isAvailable = await SignInWithApple.isAvailable();
      logger.i('Apple 登入可用性: $isAvailable');

      if (!isAvailable) {
        throw Exception('Apple 登入在此設備上不可用');
      }

      logger.i('開始獲取 Apple ID 憑證...');
      final nonce = _generateNonce();
      logger.i('生成的 nonce: ${nonce.substring(0, 8)}...');

      final credential = await SignInWithApple.getAppleIDCredential(
        scopes: [
          AppleIDAuthorizationScopes.email,
          AppleIDAuthorizationScopes.fullName,
        ],
        nonce: nonce,
      );

      logger.i('Apple ID 憑證獲取成功');
      logger.i('用戶 ID: ${credential.userIdentifier}');
      logger.i('電子郵件: ${credential.email ?? "未提供"}');
      logger.i(
          '姓名: ${credential.givenName ?? ""} ${credential.familyName ?? ""}');

      if (_shouldUseRestApi()) {
        return await _signInWithAppleViaRestApi(credential);
      } else {
        return await _signInWithAppleViaSDK(credential);
      }
    } on SignInWithAppleAuthorizationException catch (e) {
      logger.e('Apple 登入授權失敗: ${e.code} - ${e.message}');
      logger.e('錯誤詳情: $e');

      // 處理特定的錯誤情況
      switch (e.code) {
        case AuthorizationErrorCode.canceled:
          logger.i('用戶取消 Apple 登入');
          return null; // 用戶取消，不拋出異常
        case AuthorizationErrorCode.failed:
          throw Exception('Apple 登入失敗，請稍後再試');
        case AuthorizationErrorCode.invalidResponse:
          throw Exception('Apple 登入回應無效，請重試');
        case AuthorizationErrorCode.notHandled:
          throw Exception('Apple 登入請求未處理，請重試');
        case AuthorizationErrorCode.unknown:
          // 錯誤 1000 通常是網路或配置問題
          logger.e('Apple 登入未知錯誤 (可能是網路或配置問題)');
          throw Exception('Apple 登入暫時不可用，請檢查網路連接或稍後再試');
        default:
          throw Exception('Apple 登入失敗：${e.message}');
      }
    } on PlatformException catch (e) {
      logger.e('Apple 登入平台異常: ${e.code} - ${e.message}');
      throw Exception('Apple 登入系統錯誤：${e.message ?? e.code}');
    } catch (e) {
      logger.e('Apple 登入失敗: $e');

      // 處理其他類型的錯誤
      final errorString = e.toString().toLowerCase();
      if (errorString.contains('network')) {
        throw Exception('網路連接失敗，請檢查網路設定');
      } else if (errorString.contains('timeout')) {
        throw Exception('Apple 登入超時，請稍後再試');
      }

      rethrow;
    }
  }

  /// 匿名登入（設備級持久化 + 雲端同步）
  static Future<AppUser?> signInAnonymously() async {
    try {
      logger.i('開始匿名登入');

      // 1. 首先嘗試從雲端恢復設備匿名帳號
      final cloudAnonymousUser = await _getCloudDeviceAnonymousUser();
      if (cloudAnonymousUser != null) {
        logger.i('從雲端找到設備匿名帳號，嘗試恢復: ${cloudAnonymousUser.uid}');

        // 嘗試恢復雲端匿名帳號
        final restoredUser = await _restoreCloudAnonymousUser(cloudAnonymousUser);
        if (restoredUser != null) {
          logger.i('雲端設備匿名帳號恢復成功: ${restoredUser.uid}');
          // 同步到本地
          await _saveDeviceAnonymousUser(restoredUser);
          return restoredUser;
        } else {
          logger.w('雲端設備匿名帳號恢復失敗，但雲端記錄存在');
          logger.i('將跳過本地檢查，直接嘗試強制恢復或創建新帳號');

          // 如果雲端有記錄但恢復失敗，跳過本地檢查
          // 直接進入創建新帳號流程，但會保留雲端關聯
        }
      }

      // 2. 檢查本地設備級的匿名帳號（只有在沒有雲端記錄時才檢查）
      if (cloudAnonymousUser == null) {
        final deviceAnonymousUser = await _getDeviceAnonymousUser();
        if (deviceAnonymousUser != null) {
          logger.i('找到本地設備級匿名帳號，嘗試恢復: ${deviceAnonymousUser.uid}');

          // 嘗試恢復設備級匿名帳號
          final restoredUser = await _restoreDeviceAnonymousUser(deviceAnonymousUser);
          if (restoredUser != null) {
            logger.i('本地設備級匿名帳號恢復成功: ${restoredUser.uid}');
            // 同步到雲端
            await _saveCloudDeviceAnonymousUser(restoredUser);
            return restoredUser;
          } else {
            logger.w('本地設備級匿名帳號恢復失敗，將創建新的匿名帳號');
            // 清除無效的設備匿名帳號記錄
            await _clearDeviceAnonymousUser();
          }
        }
      }

      // 3. 創建新的匿名帳號（可能繼承雲端記錄）
      AppUser? newAnonymousUser;
      if (_shouldUseRestApi()) {
        newAnonymousUser = await _signInAnonymouslyViaRestApi();
      } else {
        newAnonymousUser = await _signInAnonymouslyViaSDK();
      }

      // 4. 如果有雲端記錄但恢復失敗，嘗試繼承雲端記錄的信息
      if (newAnonymousUser != null && cloudAnonymousUser != null) {
        logger.i('新匿名帳號創建成功，嘗試繼承雲端記錄信息');

        // 創建繼承雲端信息的用戶對象
        final inheritedUser = AppUser(
          uid: newAnonymousUser.uid, // 使用新的 Firebase UID
          email: cloudAnonymousUser.email, // 繼承雲端記錄的信息
          displayName: cloudAnonymousUser.displayName,
          photoURL: cloudAnonymousUser.photoURL,
          emailVerified: cloudAnonymousUser.emailVerified,
          isAnonymous: true,
          createdAt: cloudAnonymousUser.createdAt, // 保持原始創建時間
        );

        // 更新會話為繼承的用戶
        if (_shouldUseRestApi()) {
          // REST API 模式下，會話已經在創建時保存
          _currentUser = inheritedUser;
        } else {
          await _saveUserSessionSDK(inheritedUser);
        }
        newAnonymousUser = inheritedUser;

        // 更新雲端記錄為新的 UID，保持設備關聯
        try {
          final deviceFingerprint = await _generateDeviceFingerprint();
          final firestore = FirebaseFirestore.instance;
          await firestore
              .collection(_deviceAnonymousCollection)
              .doc(deviceFingerprint)
              .update({
            'uid': inheritedUser.uid, // 更新為新的 UID
            'updatedAt': FieldValue.serverTimestamp(),
            'lastUsedAt': FieldValue.serverTimestamp(),
          });
          logger.i('已更新雲端記錄的 UID: ${inheritedUser.uid}');
        } catch (e) {
          logger.e('更新雲端記錄 UID 失敗: $e');
        }

        logger.i('成功繼承雲端記錄信息，新 UID: ${newAnonymousUser.uid}');
        logger.i('原始創建時間: ${cloudAnonymousUser.createdAt}');
      }

      // 5. 保存為設備級匿名帳號（本地 + 雲端）
      if (newAnonymousUser != null) {
        await _saveDeviceAnonymousUser(newAnonymousUser);
        await _saveCloudDeviceAnonymousUser(newAnonymousUser);

        if (cloudAnonymousUser != null) {
          logger.i('新匿名帳號已保存並繼承雲端記錄（本地+雲端）: ${newAnonymousUser.uid}');
        } else {
          logger.i('新匿名帳號已保存為設備級帳號（本地+雲端）: ${newAnonymousUser.uid}');
        }
      }

      return newAnonymousUser;
    } catch (e) {
      logger.e('匿名登入失敗: $e');

      // 處理匿名登入的特定錯誤
      if (e.toString().contains('OPERATION_NOT_ALLOWED')) {
        throw Exception('匿名登入功能已被停用');
      } else if (e.toString().contains('TOO_MANY_ATTEMPTS_TRY_LATER')) {
        throw Exception('嘗試次數過多，請稍後再試');
      }

      rethrow;
    }
  }

  /// 發送密碼重置郵件
  static Future<void> sendPasswordResetEmail({required String email}) async {
    try {
      logger.i('發送密碼重置郵件: $email');

      if (_shouldUseRestApi()) {
        await _sendPasswordResetEmailViaRestApi(email);
      } else {
        await _sendPasswordResetEmailViaSDK(email);
      }
    } catch (e) {
      logger.e('發送密碼重置郵件失敗: $e');
      rethrow;
    }
  }

  /// 發送電子郵件驗證
  static Future<void> sendEmailVerification() async {
    try {
      if (_currentUser == null || _idToken == null) {
        throw Exception('用戶未登入');
      }

      logger.i('發送電子郵件驗證');

      if (_shouldUseRestApi()) {
        await _sendEmailVerificationViaRestApi();
      } else {
        await _sendEmailVerificationViaSDK();
      }
    } catch (e) {
      logger.e('發送電子郵件驗證失敗: $e');
      rethrow;
    }
  }

  /// 更新用戶資料
  static Future<AppUser?> updateUserProfile({
    String? displayName,
    String? photoURL,
  }) async {
    try {
      if (_currentUser == null || _idToken == null) {
        throw Exception('用戶未登入');
      }

      logger.i('更新用戶資料');

      if (_shouldUseRestApi()) {
        return await _updateUserProfileViaRestApi(displayName, photoURL);
      } else {
        return await _updateUserProfileViaSDK(displayName, photoURL);
      }
    } catch (e) {
      logger.e('更新用戶資料失敗: $e');
      rethrow;
    }
  }

  /// 登出
  static Future<void> signOut() async {
    try {
      logger.i('用戶登出');

      // 檢查當前用戶是否為匿名用戶
      final isCurrentUserAnonymous = _currentUser?.isAnonymous ?? false;
      final currentUserUid = _currentUser?.uid;

      // 如果是匿名用戶登出，先確保雲端記錄已保存
      if (isCurrentUserAnonymous && _currentUser != null) {
        logger.i('匿名用戶登出，確保雲端記錄已保存');
        await _saveCloudDeviceAnonymousUser(_currentUser!);
      }

      // Google 登出
      if (await _googleSignIn.isSignedIn()) {
        await _googleSignIn.signOut();
      }

      // 對於匿名用戶，我們不調用 Firebase signOut，以保持匿名用戶在 Firebase Auth 中的狀態
      // 但對於其他帳號登入的情況，我們需要完全清除 Firebase Auth 狀態
      if (!isCurrentUserAnonymous) {
        // 非匿名用戶登出時，完全清除 Firebase Auth 狀態
        if (FirebaseAuth.instance.currentUser != null) {
          await FirebaseAuth.instance.signOut();
        }
        logger.i('非匿名用戶登出，已清除 Firebase Auth 狀態');
      } else {
        logger.i('匿名用戶登出，保持 Firebase Auth 狀態以便後續恢復');
      }

      // 清除本地會話（但保留匿名用戶的設備級信息）
      await _clearUserSession(preserveAnonymous: isCurrentUserAnonymous);

      // 如果是匿名用戶登出，確保設備級匿名帳號信息被保留
      if (isCurrentUserAnonymous && currentUserUid != null) {
        logger.i('匿名用戶登出，保留設備級帳號信息: $currentUserUid');
      }

      logger.i('用戶登出成功');
    } catch (e) {
      logger.e('用戶登出失敗: $e');
      rethrow;
    }
  }

  /// 刪除用戶帳戶
  static Future<void> deleteUser() async {
    try {
      // 檢查用戶登入狀態
      AppUser? userToDelete = _currentUser;

      // 如果 _currentUser 為 null，檢查是否有 Firebase Auth 中的匿名用戶
      if (userToDelete == null && !_shouldUseRestApi()) {
        final firebaseUser = FirebaseAuth.instance.currentUser;
        if (firebaseUser != null && firebaseUser.isAnonymous) {
          // 從 Firebase Auth 創建 AppUser 對象
          userToDelete = AppUser(
            uid: firebaseUser.uid,
            email: firebaseUser.email,
            displayName: firebaseUser.displayName,
            photoURL: firebaseUser.photoURL,
            emailVerified: firebaseUser.emailVerified,
            isAnonymous: firebaseUser.isAnonymous,
            createdAt: firebaseUser.metadata.creationTime ?? DateTime.now(),
          );
          logger.i('檢測到 Firebase Auth 中的匿名用戶，允許刪除: ${userToDelete.uid}');
        }
      }

      // 如果仍然沒有用戶，檢查是否有設備級匿名帳號
      if (userToDelete == null) {
        final deviceAnonymousUser = await _getDeviceAnonymousUser();
        if (deviceAnonymousUser != null) {
          userToDelete = deviceAnonymousUser;
          logger.i('檢測到設備級匿名帳號，允許刪除: ${userToDelete.uid}');
        }
      }

      if (userToDelete == null) {
        throw Exception('用戶未登入，無法刪除帳戶');
      }

      logger.i('刪除用戶帳戶: ${userToDelete.uid}');

      // 如果是匿名用戶，需要額外清除設備級記錄
      if (userToDelete.isAnonymous) {
        logger.i('刪除匿名用戶，清除設備級記錄');
        await _clearDeviceAnonymousUser();
        await _clearCloudDeviceAnonymousUser();
      }

      if (_shouldUseRestApi()) {
        await _deleteUserViaRestApi();
      } else {
        await _deleteUserViaSDK();
      }

      // 完全清除會話（不保留匿名用戶信息）
      await _clearUserSession(preserveAnonymous: false);

      logger.i('用戶帳戶刪除成功');
    } catch (e) {
      logger.e('刪除用戶帳戶失敗: $e');
      rethrow;
    }
  }

  /// 生成隨機 nonce
  static String _generateNonce([int length = 32]) {
    const charset =
        '0123456789ABCDEFGHIJKLMNOPQRSTUVXYZabcdefghijklmnopqrstuvwxyz-._';
    final random = Random.secure();
    return List.generate(length, (_) => charset[random.nextInt(charset.length)])
        .join();
  }

  /// 保存用戶會話
  static Future<void> _saveUserSession(AppUser user, String idToken,
      [String? refreshToken]) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(_tokenKey, idToken);
    await prefs.setString(_userKey, json.encode(user.toJson()));
    if (refreshToken != null) {
      await prefs.setString(_refreshTokenKey, refreshToken);
    }

    _currentUser = user;
    _idToken = idToken;
    _refreshToken = refreshToken;

    // 通知認證狀態變化
    _notifyAuthStateChange(user);
  }

  /// 驗證 token 有效性
  static Future<void> _validateToken() async {
    if (_idToken == null) return;

    try {
      logger.i('驗證 Firebase token...');

      // 使用 token 驗證 API
      final response = await http
          .post(
            Uri.parse(
                'https://identitytoolkit.googleapis.com/v1/accounts:lookup?key=${FirebaseConfigWindows.apiKey}'),
            headers: {'Content-Type': 'application/json'},
            body: json.encode({'idToken': _idToken}),
          )
          .timeout(const Duration(seconds: 10));

      if (response.statusCode == 200) {
        logger.i('Token 驗證成功');
      } else {
        logger.w('Token 無效，狀態碼: ${response.statusCode}');
        // Token 無效，嘗試刷新
        if (_refreshToken != null) {
          logger.i('嘗試刷新 token...');
          await _refreshIdToken();
        } else {
          logger.w('無 refresh token，執行登出');
          await signOut();
        }
      }
    } catch (e) {
      logger.w('Token 驗證失敗: $e');

      // 網路錯誤不強制登出，只記錄警告
      if (e.toString().contains('timeout') ||
          e.toString().contains('network') ||
          e.toString().contains('connection')) {
        logger.w('網路問題導致 token 驗證失敗，保持登入狀態');
      } else {
        logger.w('Token 驗證嚴重錯誤，執行登出');
        await signOut();
      }
    }
  }

  /// 刷新 ID Token
  static Future<void> _refreshIdToken() async {
    if (_refreshToken == null) return;

    try {
      final response = await http.post(
        Uri.parse(
            'https://securetoken.googleapis.com/v1/token?key=${FirebaseConfigWindows.apiKey}'),
        headers: {'Content-Type': 'application/json'},
        body: json.encode({
          'grant_type': 'refresh_token',
          'refresh_token': _refreshToken,
        }),
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        _idToken = data['id_token'];
        _refreshToken = data['refresh_token'];

        final prefs = await SharedPreferences.getInstance();
        await prefs.setString(_tokenKey, _idToken!);
        await prefs.setString(_refreshTokenKey, _refreshToken!);

        logger.i('ID Token 刷新成功');
      } else {
        throw Exception('Token 刷新失敗');
      }
    } catch (e) {
      logger.e('Token 刷新失敗: $e');
      await signOut();
    }
  }

  // ==================== REST API 實作方法 ====================

  /// 通過 REST API 註冊用戶
  static Future<AppUser?> _registerWithEmailPasswordViaRestApi(
    String email,
    String password,
    String? displayName,
  ) async {
    final response = await http.post(
      Uri.parse(
          'https://identitytoolkit.googleapis.com/v1/accounts:signUp?key=${FirebaseConfigWindows.apiKey}'),
      headers: {'Content-Type': 'application/json'},
      body: json.encode({
        'email': email,
        'password': password,
        'returnSecureToken': true,
      }),
    );

    if (response.statusCode == 200) {
      final data = json.decode(response.body);
      final user = AppUser(
        uid: data['localId'],
        email: email,
        displayName: displayName,
        emailVerified: false,
        isAnonymous: false,
        createdAt: DateTime.now(),
      );

      await _saveUserSession(user, data['idToken'], data['refreshToken']);

      // 如果有顯示名稱，更新用戶資料
      if (displayName != null) {
        await _updateUserProfileViaRestApi(displayName, null);
      }

      // 初始化新用戶的完整數據
      await _initializeNewUserData(
        user.uid,
        email: email,
        displayName: displayName,
        isAnonymous: false,
      );

      logger.i('REST API 用戶註冊成功: ${user.uid}');
      return user;
    } else {
      final error = json.decode(response.body);
      throw Exception(_getFirebaseErrorMessage(error['error']['message']));
    }
  }

  /// 通過 REST API 登入用戶
  static Future<AppUser?> _signInWithEmailPasswordViaRestApi(
      String email, String password) async {
    final response = await http.post(
      Uri.parse(
          'https://identitytoolkit.googleapis.com/v1/accounts:signInWithPassword?key=${FirebaseConfigWindows.apiKey}'),
      headers: {'Content-Type': 'application/json'},
      body: json.encode({
        'email': email,
        'password': password,
        'returnSecureToken': true,
      }),
    );

    if (response.statusCode == 200) {
      final data = json.decode(response.body);
      final user = AppUser(
        uid: data['localId'],
        email: data['email'],
        displayName: data['displayName'],
        photoURL: data['photoUrl'],
        emailVerified: data['emailVerified'] == 'true',
        isAnonymous: false,
        createdAt: DateTime.now(),
      );

      await _saveUserSession(user, data['idToken'], data['refreshToken']);
      logger.i('REST API 用戶登入成功: ${user.uid}');
      return user;
    } else {
      final error = json.decode(response.body);
      throw Exception(_getFirebaseErrorMessage(error['error']['message']));
    }
  }

  /// 通過 REST API Google 登入
  static Future<AppUser?> _signInWithGoogleViaRestApi(
      GoogleSignInAuthentication googleAuth) async {
    final response = await http.post(
      Uri.parse(
          'https://identitytoolkit.googleapis.com/v1/accounts:signInWithIdp?key=${FirebaseConfigWindows.apiKey}'),
      headers: {'Content-Type': 'application/json'},
      body: json.encode({
        'postBody':
            'id_token=${googleAuth.idToken}&access_token=${googleAuth.accessToken}',
        'requestUri': 'http://localhost',
        'returnIdpCredential': true,
        'returnSecureToken': true,
      }),
    );

    if (response.statusCode == 200) {
      final data = json.decode(response.body);
      final user = AppUser(
        uid: data['localId'],
        email: data['email'],
        displayName: data['displayName'],
        photoURL: data['photoUrl'],
        emailVerified: data['emailVerified'] == 'true',
        isAnonymous: false,
        createdAt: DateTime.now(),
      );

      await _saveUserSession(user, data['idToken'], data['refreshToken']);
      logger.i('REST API Google 登入成功: ${user.uid}');
      return user;
    } else {
      final error = json.decode(response.body);
      throw Exception(_getFirebaseErrorMessage(error['error']['message']));
    }
  }

  /// 通過 REST API Apple 登入
  static Future<AppUser?> _signInWithAppleViaRestApi(
      AuthorizationCredentialAppleID credential) async {
    final response = await http.post(
      Uri.parse(
          'https://identitytoolkit.googleapis.com/v1/accounts:signInWithIdp?key=${FirebaseConfigWindows.apiKey}'),
      headers: {'Content-Type': 'application/json'},
      body: json.encode({
        'postBody':
            'id_token=${credential.identityToken}&provider_id=apple.com',
        'requestUri': 'http://localhost',
        'returnIdpCredential': true,
        'returnSecureToken': true,
      }),
    );

    if (response.statusCode == 200) {
      final data = json.decode(response.body);
      final user = AppUser(
        uid: data['localId'],
        email: data['email'] ?? credential.email,
        displayName: data['displayName'] ??
            '${credential.givenName ?? ''} ${credential.familyName ?? ''}'
                .trim(),
        photoURL: data['photoUrl'],
        emailVerified: data['emailVerified'] == 'true',
        isAnonymous: false,
        createdAt: DateTime.now(),
      );

      await _saveUserSession(user, data['idToken'], data['refreshToken']);
      logger.i('REST API Apple 登入成功: ${user.uid}');
      return user;
    } else {
      final error = json.decode(response.body);
      throw Exception(_getFirebaseErrorMessage(error['error']['message']));
    }
  }

  /// 通過 REST API 匿名登入
  static Future<AppUser?> _signInAnonymouslyViaRestApi() async {
    final response = await http.post(
      Uri.parse(
          'https://identitytoolkit.googleapis.com/v1/accounts:signUp?key=${FirebaseConfigWindows.apiKey}'),
      headers: {'Content-Type': 'application/json'},
      body: json.encode({
        'returnSecureToken': true,
      }),
    );

    if (response.statusCode == 200) {
      final data = json.decode(response.body);
      final user = AppUser(
        uid: data['localId'],
        email: null,
        displayName: null,
        photoURL: null,
        emailVerified: false,
        isAnonymous: true,
        createdAt: DateTime.now(),
      );

      await _saveUserSession(user, data['idToken'], data['refreshToken']);

      // 初始化新用戶的數據
      await _initializeNewUserData(
        user.uid,
        email: null,
        displayName: null,
        isAnonymous: true,
      );

      logger.i('REST API 匿名登入成功: ${user.uid}');
      return user;
    } else {
      final error = json.decode(response.body);
      throw Exception(_getFirebaseErrorMessage(error['error']['message']));
    }
  }

  /// 通過 REST API 發送密碼重置郵件
  static Future<void> _sendPasswordResetEmailViaRestApi(String email) async {
    final response = await http.post(
      Uri.parse(
          'https://identitytoolkit.googleapis.com/v1/accounts:sendOobCode?key=${FirebaseConfigWindows.apiKey}'),
      headers: {'Content-Type': 'application/json'},
      body: json.encode({
        'requestType': 'PASSWORD_RESET',
        'email': email,
      }),
    );

    if (response.statusCode == 200) {
      logger.i('REST API 密碼重置郵件發送成功');
    } else {
      final error = json.decode(response.body);
      throw Exception(_getFirebaseErrorMessage(error['error']['message']));
    }
  }

  /// 通過 REST API 發送電子郵件驗證
  static Future<void> _sendEmailVerificationViaRestApi() async {
    final response = await http.post(
      Uri.parse(
          'https://identitytoolkit.googleapis.com/v1/accounts:sendOobCode?key=${FirebaseConfigWindows.apiKey}'),
      headers: {'Content-Type': 'application/json'},
      body: json.encode({
        'requestType': 'VERIFY_EMAIL',
        'idToken': _idToken,
      }),
    );

    if (response.statusCode == 200) {
      logger.i('REST API 電子郵件驗證發送成功');
    } else {
      final error = json.decode(response.body);
      throw Exception(_getFirebaseErrorMessage(error['error']['message']));
    }
  }

  /// 通過 REST API 更新用戶資料
  static Future<AppUser?> _updateUserProfileViaRestApi(
      String? displayName, String? photoURL) async {
    final response = await http.post(
      Uri.parse(
          'https://identitytoolkit.googleapis.com/v1/accounts:update?key=${FirebaseConfigWindows.apiKey}'),
      headers: {'Content-Type': 'application/json'},
      body: json.encode({
        'idToken': _idToken,
        'displayName': displayName,
        'photoUrl': photoURL,
        'returnSecureToken': true,
      }),
    );

    if (response.statusCode == 200) {
      final data = json.decode(response.body);
      final updatedUser = _currentUser!.copyWith(
        displayName: data['displayName'],
        photoURL: data['photoUrl'],
      );

      await _saveUserSession(
          updatedUser, data['idToken'], data['refreshToken']);
      logger.i('REST API 用戶資料更新成功');
      return updatedUser;
    } else {
      final error = json.decode(response.body);
      throw Exception(_getFirebaseErrorMessage(error['error']['message']));
    }
  }

  /// 通過 REST API 刪除用戶
  static Future<void> _deleteUserViaRestApi() async {
    String? tokenToUse = _idToken;

    // 如果沒有 token，嘗試從本地存儲獲取
    if (tokenToUse == null) {
      final prefs = await SharedPreferences.getInstance();
      tokenToUse = prefs.getString(_tokenKey);

      if (tokenToUse != null) {
        logger.i('從本地存儲獲取到 token，嘗試刪除用戶');
      } else {
        logger.w('REST API 模式下沒有有效的 token，無法刪除用戶');
        throw Exception('無法獲取有效的認證 token，請重新登入後再試');
      }
    }

    final response = await http.post(
      Uri.parse(
          'https://identitytoolkit.googleapis.com/v1/accounts:delete?key=${FirebaseConfigWindows.apiKey}'),
      headers: {'Content-Type': 'application/json'},
      body: json.encode({
        'idToken': tokenToUse,
      }),
    );

    if (response.statusCode == 200) {
      logger.i('REST API 用戶刪除成功');
    } else {
      final error = json.decode(response.body);
      final errorMessage = error['error']['message'];

      // 如果是 token 相關錯誤，提供更友好的錯誤訊息
      if (errorMessage.contains('INVALID_ID_TOKEN') ||
          errorMessage.contains('TOKEN_EXPIRED')) {
        throw Exception('認證已過期，請重新登入後再試刪除帳戶');
      }

      throw Exception(_getFirebaseErrorMessage(errorMessage));
    }
  }

  // ==================== Firebase SDK 實作方法（預留） ====================

  static Future<AppUser?> _registerWithEmailPasswordViaSDK(
      String email, String password, String? displayName) async {
    try {
      logger.i('使用 Firebase SDK 註冊用戶: $email');

      final credential =
          await FirebaseAuth.instance.createUserWithEmailAndPassword(
        email: email,
        password: password,
      );

      final firebaseUser = credential.user;
      if (firebaseUser == null) {
        throw Exception('Firebase SDK 註冊失敗：未返回用戶信息');
      }

      // 更新顯示名稱
      if (displayName != null && displayName.isNotEmpty) {
        await firebaseUser.updateDisplayName(displayName);
        await firebaseUser.reload();
      }

      // 創建 AppUser
      final user = AppUser(
        uid: firebaseUser.uid,
        email: firebaseUser.email,
        displayName: firebaseUser.displayName ?? displayName,
        photoURL: firebaseUser.photoURL,
        emailVerified: firebaseUser.emailVerified,
        isAnonymous: firebaseUser.isAnonymous,
        createdAt: firebaseUser.metadata.creationTime ?? DateTime.now(),
      );

      // 保存用戶會話（SDK 版本不需要手動管理 token）
      await _saveUserSessionSDK(user);

      // 初始化新用戶的完整數據
      await _initializeNewUserData(
        user.uid,
        email: email,
        displayName: displayName,
        isAnonymous: false,
      );

      logger.i('Firebase SDK 用戶註冊成功: ${user.uid}');
      return user;
    } on FirebaseAuthException catch (e) {
      logger.e('Firebase SDK 註冊失敗: ${e.code} - ${e.message}');
      throw Exception(_getFirebaseErrorMessage(e.code));
    } catch (e) {
      logger.e('Firebase SDK 註冊失敗: $e');
      rethrow;
    }
  }

  static Future<AppUser?> _signInWithEmailPasswordViaSDK(
      String email, String password) async {
    try {
      logger.i('使用 Firebase SDK 登入用戶: $email');

      final credential = await FirebaseAuth.instance.signInWithEmailAndPassword(
        email: email,
        password: password,
      );

      final firebaseUser = credential.user;
      if (firebaseUser == null) {
        throw Exception('Firebase SDK 登入失敗：未返回用戶信息');
      }

      // 創建 AppUser
      final user = AppUser(
        uid: firebaseUser.uid,
        email: firebaseUser.email,
        displayName: firebaseUser.displayName,
        photoURL: firebaseUser.photoURL,
        emailVerified: firebaseUser.emailVerified,
        isAnonymous: firebaseUser.isAnonymous,
        createdAt: firebaseUser.metadata.creationTime ?? DateTime.now(),
      );

      // 保存用戶會話（SDK 版本不需要手動管理 token）
      await _saveUserSessionSDK(user);

      // 初始化新用戶的免費試用記錄
      await _initializeNewUserData(user.uid);

      logger.i('Firebase SDK 用戶登入成功: ${user.uid}');
      return user;
    } on FirebaseAuthException catch (e) {
      logger.e('Firebase SDK 登入失敗: ${e.code} - ${e.message}');

      // 特殊處理 invalid-credential 錯誤
      if (e.code == 'invalid-credential') {
        logger.w('檢測到 invalid-credential 錯誤，執行診斷...');

        // 執行詳細診斷（在開發模式下）
        if (kDebugMode) {
          try {
            final EmailAuthDiagnostics diagnostics = EmailAuthDiagnostics();
            await diagnostics.diagnoseEmailAuth(
                email: email, password: password);
          } catch (diagError) {
            logger.e('診斷過程失敗: $diagError');
          }
        }

        // 嘗試自動修復
        try {
          final EmailAuthDiagnostics diagnostics = EmailAuthDiagnostics();
          final fixed = await diagnostics.attemptAutoFix();
          if (fixed) {
            logger.i('自動修復完成，建議用戶重新嘗試登入');
          }
        } catch (fixError) {
          logger.e('自動修復失敗: $fixError');
        }
      }

      throw Exception(_getFirebaseErrorMessage(e.code));
    } catch (e) {
      logger.e('Firebase SDK 登入失敗: $e');
      rethrow;
    }
  }

  static Future<AppUser?> _signInWithGoogleViaSDK(
      GoogleSignInAuthentication googleAuth) async {
    try {
      logger.i('使用 Firebase SDK 進行 Google 登入');

      final credential = GoogleAuthProvider.credential(
        accessToken: googleAuth.accessToken,
        idToken: googleAuth.idToken,
      );

      final userCredential =
          await FirebaseAuth.instance.signInWithCredential(credential);
      final firebaseUser = userCredential.user;

      if (firebaseUser == null) {
        throw Exception('Firebase SDK Google 登入失敗：未返回用戶信息');
      }

      // 創建 AppUser
      final user = AppUser(
        uid: firebaseUser.uid,
        email: firebaseUser.email,
        displayName: firebaseUser.displayName,
        photoURL: firebaseUser.photoURL,
        emailVerified: firebaseUser.emailVerified,
        isAnonymous: firebaseUser.isAnonymous,
        createdAt: firebaseUser.metadata.creationTime ?? DateTime.now(),
      );

      // 保存用戶會話（SDK 版本不需要手動管理 token）
      await _saveUserSessionSDK(user);

      // 初始化新用戶的免費試用記錄
      await _initializeNewUserData(user.uid);

      logger.i('Firebase SDK Google 登入成功: ${user.uid}');
      return user;
    } on FirebaseAuthException catch (e) {
      logger.e('Firebase SDK Google 登入失敗: ${e.code} - ${e.message}');
      throw Exception(_getFirebaseErrorMessage(e.code));
    } catch (e) {
      logger.e('Firebase SDK Google 登入失敗: $e');
      rethrow;
    }
  }

  static Future<AppUser?> _signInWithAppleViaSDK(
      AuthorizationCredentialAppleID credential) async {
    try {
      logger.i('使用 Firebase SDK 進行 Apple 登入');

      final oauthCredential = OAuthProvider("apple.com").credential(
        idToken: credential.identityToken,
        accessToken: credential.authorizationCode,
      );

      final userCredential =
          await FirebaseAuth.instance.signInWithCredential(oauthCredential);
      final firebaseUser = userCredential.user;

      if (firebaseUser == null) {
        throw Exception('Firebase SDK Apple 登入失敗：未返回用戶信息');
      }

      // 如果是新用戶且提供了姓名信息，更新顯示名稱
      if (userCredential.additionalUserInfo?.isNewUser == true) {
        String? displayName;
        if (credential.givenName != null || credential.familyName != null) {
          displayName =
              '${credential.givenName ?? ''} ${credential.familyName ?? ''}'
                  .trim();
          if (displayName.isNotEmpty) {
            await firebaseUser.updateDisplayName(displayName);
            await firebaseUser.reload();
          }
        }
      }

      // 創建 AppUser
      final user = AppUser(
        uid: firebaseUser.uid,
        email: firebaseUser.email ?? credential.email,
        displayName: firebaseUser.displayName,
        photoURL: firebaseUser.photoURL,
        emailVerified: firebaseUser.emailVerified,
        isAnonymous: firebaseUser.isAnonymous,
        createdAt: firebaseUser.metadata.creationTime ?? DateTime.now(),
      );

      // 保存用戶會話
      await _saveUserSessionSDK(user);

      logger.i('Firebase SDK Apple 登入成功: ${user.uid}');
      return user;
    } on FirebaseAuthException catch (e) {
      logger.e('Firebase SDK Apple 登入失敗: ${e.code} - ${e.message}');
      throw Exception(_getFirebaseErrorMessage(e.code));
    } catch (e) {
      logger.e('Firebase SDK Apple 登入失敗: $e');
      rethrow;
    }
  }

  static Future<AppUser?> _signInAnonymouslyViaSDK() async {
    try {
      logger.i('使用 Firebase SDK 進行匿名登入');

      // 首先檢查是否已經有匿名用戶在 Firebase Auth 中
      final existingFirebaseUser = FirebaseAuth.instance.currentUser;
      if (existingFirebaseUser != null && existingFirebaseUser.isAnonymous) {
        logger.i('發現現有的 Firebase 匿名用戶: ${existingFirebaseUser.uid}');

        // 使用現有的匿名用戶
        final user = AppUser(
          uid: existingFirebaseUser.uid,
          email: existingFirebaseUser.email,
          displayName: existingFirebaseUser.displayName,
          photoURL: existingFirebaseUser.photoURL,
          emailVerified: existingFirebaseUser.emailVerified,
          isAnonymous: existingFirebaseUser.isAnonymous,
          createdAt: existingFirebaseUser.metadata.creationTime ?? DateTime.now(),
        );

        // 保存用戶會話
        await _saveUserSessionSDK(user);
        logger.i('使用現有 Firebase 匿名用戶: ${user.uid}');
        return user;
      }

      // 如果沒有現有的匿名用戶，創建新的
      logger.i('創建新的 Firebase 匿名用戶');
      final userCredential = await FirebaseAuth.instance.signInAnonymously();
      final firebaseUser = userCredential.user;

      if (firebaseUser == null) {
        throw Exception('Firebase SDK 匿名登入失敗：未返回用戶信息');
      }

      // 創建 AppUser
      final user = AppUser(
        uid: firebaseUser.uid,
        email: firebaseUser.email,
        displayName: firebaseUser.displayName,
        photoURL: firebaseUser.photoURL,
        emailVerified: firebaseUser.emailVerified,
        isAnonymous: firebaseUser.isAnonymous,
        createdAt: firebaseUser.metadata.creationTime ?? DateTime.now(),
      );

      // 保存用戶會話
      await _saveUserSessionSDK(user);

      // 初始化新用戶的數據
      await _initializeNewUserData(
        user.uid,
        email: user.email,
        displayName: user.displayName,
        isAnonymous: true,
      );

      logger.i('Firebase SDK 匿名登入成功: ${user.uid}');
      return user;
    } on FirebaseAuthException catch (e) {
      logger.e('Firebase SDK 匿名登入失敗: ${e.code} - ${e.message}');
      throw Exception(_getFirebaseErrorMessage(e.code));
    } catch (e) {
      logger.e('Firebase SDK 匿名登入失敗: $e');
      rethrow;
    }
  }

  static Future<void> _sendPasswordResetEmailViaSDK(String email) async {
    try {
      logger.i('使用 Firebase SDK 發送密碼重置郵件: $email');

      await FirebaseAuth.instance.sendPasswordResetEmail(email: email);

      logger.i('Firebase SDK 密碼重置郵件發送成功');
    } on FirebaseAuthException catch (e) {
      logger.e('Firebase SDK 發送密碼重置郵件失敗: ${e.code} - ${e.message}');
      throw Exception(_getFirebaseErrorMessage(e.code));
    } catch (e) {
      logger.e('Firebase SDK 發送密碼重置郵件失敗: $e');
      rethrow;
    }
  }

  static Future<void> _sendEmailVerificationViaSDK() async {
    try {
      final user = FirebaseAuth.instance.currentUser;
      if (user == null) {
        throw Exception('沒有已登入的用戶');
      }

      logger.i('使用 Firebase SDK 發送電子郵件驗證');

      await user.sendEmailVerification();

      logger.i('Firebase SDK 電子郵件驗證發送成功');
    } on FirebaseAuthException catch (e) {
      logger.e('Firebase SDK 發送電子郵件驗證失敗: ${e.code} - ${e.message}');
      throw Exception(_getFirebaseErrorMessage(e.code));
    } catch (e) {
      logger.e('Firebase SDK 發送電子郵件驗證失敗: $e');
      rethrow;
    }
  }

  static Future<AppUser?> _updateUserProfileViaSDK(
      String? displayName, String? photoURL) async {
    try {
      final user = FirebaseAuth.instance.currentUser;
      if (user == null) {
        throw Exception('沒有已登入的用戶');
      }

      logger.i('使用 Firebase SDK 更新用戶資料');

      // 更新用戶資料
      if (displayName != null) {
        await user.updateDisplayName(displayName);
      }
      if (photoURL != null) {
        await user.updatePhotoURL(photoURL);
      }

      // 重新載入用戶資料
      await user.reload();
      final updatedUser = FirebaseAuth.instance.currentUser!;

      // 創建更新後的 AppUser
      final appUser = AppUser(
        uid: updatedUser.uid,
        email: updatedUser.email,
        displayName: updatedUser.displayName,
        photoURL: updatedUser.photoURL,
        emailVerified: updatedUser.emailVerified,
        isAnonymous: updatedUser.isAnonymous,
        createdAt: updatedUser.metadata.creationTime ?? DateTime.now(),
      );

      // 保存更新後的用戶會話
      await _saveUserSessionSDK(appUser);

      logger.i('Firebase SDK 用戶資料更新成功');
      return appUser;
    } on FirebaseAuthException catch (e) {
      logger.e('Firebase SDK 更新用戶資料失敗: ${e.code} - ${e.message}');
      throw Exception(_getFirebaseErrorMessage(e.code));
    } catch (e) {
      logger.e('Firebase SDK 更新用戶資料失敗: $e');
      rethrow;
    }
  }

  static Future<void> _deleteUserViaSDK() async {
    try {
      final user = FirebaseAuth.instance.currentUser;
      if (user == null) {
        throw Exception('沒有已登入的用戶');
      }

      final userId = user.uid;
      logger.i('使用 Firebase SDK 刪除用戶帳戶: $userId');

      // 先刪除 Firestore 中的用戶資料
      await _deleteUserDataFromFirestore(userId);

      // 然後刪除 Firebase Auth 用戶帳戶
      await user.delete();

      // 清除本地會話
      await _clearUserSession();

      logger.i('Firebase SDK 用戶帳戶及相關資料刪除成功');
    } on FirebaseAuthException catch (e) {
      logger.e('Firebase SDK 刪除用戶帳戶失敗: ${e.code} - ${e.message}');
      throw Exception(_getFirebaseErrorMessage(e.code));
    } catch (e) {
      logger.e('Firebase SDK 刪除用戶帳戶失敗: $e');
      rethrow;
    }
  }

  /// 刪除 Firestore 中的用戶資料
  static Future<void> _deleteUserDataFromFirestore(String userId) async {
    try {
      logger.i('開始刪除 Firestore 中的用戶資料: $userId');
      final firestore = FirebaseFirestore.instance;
      final batch = firestore.batch();

      // 定義需要刪除的集合和文檔
      final collectionsToDelete = FirebaseCollections.userMainCollections;

      // 刪除主要集合中的用戶文檔
      for (final collection in collectionsToDelete) {
        final docRef = firestore.collection(collection).doc(userId);
        batch.delete(docRef);
        logger.d('標記刪除: $collection/$userId');
      }

      // 刪除用戶專屬的子集合
      await _deleteUserSubcollections(firestore, userId);

      // 執行批次刪除
      await batch.commit();
      logger.i('成功刪除 Firestore 中的用戶主要資料: $userId');
    } catch (e) {
      logger.e('刪除 Firestore 用戶資料失敗: $e');
      // 不拋出異常，因為即使 Firestore 刪除失敗，我們仍然要刪除 Auth 帳戶
      // 可以考慮記錄到錯誤追蹤系統
    }
  }

  /// 刪除用戶的子集合資料
  static Future<void> _deleteUserSubcollections(
      FirebaseFirestore firestore, String userId) async {
    try {
      // 刪除用戶支付記錄子集合
      await _deleteSubcollection(
          firestore, 'user_payments', userId, 'payments');

      // 刪除用戶其他可能的子集合
      // 可以根據需要添加更多子集合的刪除邏輯

      logger.i('成功刪除用戶子集合資料: $userId');
    } catch (e) {
      logger.e('刪除用戶子集合資料失敗: $e');
    }
  }

  /// 刪除指定的子集合
  static Future<void> _deleteSubcollection(FirebaseFirestore firestore,
      String parentCollection, String documentId, String subcollection) async {
    try {
      final subcollectionRef = firestore
          .collection(parentCollection)
          .doc(documentId)
          .collection(subcollection);

      // 獲取子集合中的所有文檔
      final querySnapshot = await subcollectionRef.get();

      if (querySnapshot.docs.isNotEmpty) {
        final batch = firestore.batch();

        for (final doc in querySnapshot.docs) {
          batch.delete(doc.reference);
        }

        await batch.commit();
        logger.d(
            '刪除子集合: $parentCollection/$documentId/$subcollection (${querySnapshot.docs.length} 個文檔)');
      }
    } catch (e) {
      logger.e('刪除子集合失敗 $parentCollection/$documentId/$subcollection: $e');
    }
  }

  // ==================== 輔助方法 ====================

  /// 創建帶有管理者狀態的 AppUser
  static Future<AppUser> _createAppUserWithAdminCheck({
    required String uid,
    String? email,
    String? displayName,
    String? photoURL,
    bool emailVerified = false,
    bool isAnonymous = false,
    DateTime? createdAt,
    DateTime? lastSignInAt,
  }) async {
    // 檢查用戶是否為管理者
    bool isAdmin = false;
    try {
      isAdmin = await AdminService.isUserAdmin(uid);
    } catch (e) {
      logger.w('檢查管理者狀態失敗，默認為非管理者: $e');
    }

    return AppUser(
      uid: uid,
      email: email,
      displayName: displayName,
      photoURL: photoURL,
      emailVerified: emailVerified,
      isAnonymous: isAnonymous,
      isAdmin: isAdmin,
      createdAt: createdAt ?? DateTime.now(),
      lastSignInAt: lastSignInAt,
    );
  }

  /// 初始化新用戶的數據
  static Future<void> _initializeNewUserData(
    String userId, {
    String? email,
    String? displayName,
    bool isAnonymous = false,
  }) async {
    try {
      // 使用完整的用戶數據初始化服務
      await UserDataInitializationService.initializeNewUserData(
        userId,
        email: email,
        displayName: displayName,
        isAnonymous: isAnonymous,
      );
      logger.i('新用戶 $userId 完整數據初始化完成');
    } catch (e) {
      logger.e('初始化新用戶數據失敗: $e');
      // 不拋出異常，避免影響註冊流程
    }
  }

  /// 保存用戶會話到本地存儲（SDK 版本）
  static Future<void> _saveUserSessionSDK(AppUser user) async {
    try {
      final prefs = await SharedPreferences.getInstance();

      // 保存用戶信息
      await prefs.setString(_userKey, json.encode(user.toJson()));

      // SDK 版本不需要手動管理 token，Firebase SDK 會自動處理
      // 但我們仍然保存一個標記表示使用 SDK
      await prefs.setBool('_useFirebaseSDK', true);

      // 更新內存中的狀態
      _currentUser = user;

      // 通知認證狀態變化
      _notifyAuthStateChange(user);

      logger.i('用戶會話已保存 (SDK): ${user.email}');
    } catch (e) {
      logger.e('保存用戶會話失敗: $e');
    }
  }

  /// 清除用戶會話
  static Future<void> _clearUserSession({bool preserveAnonymous = false}) async {
    try {
      final prefs = await SharedPreferences.getInstance();

      // 如果需要保留匿名用戶信息，先備份
      String? deviceAnonymousUid;
      String? deviceAnonymousUserData;
      bool? isDeviceAnonymous;

      if (preserveAnonymous) {
        deviceAnonymousUid = prefs.getString(_deviceAnonymousUidKey);
        deviceAnonymousUserData = prefs.getString(_deviceAnonymousUserKey);
        isDeviceAnonymous = prefs.getBool(_isDeviceAnonymousKey);
        logger.i('備份設備級匿名帳號信息: $deviceAnonymousUid');
      }

      // 清除所有認證相關的本地數據
      await prefs.remove(_userKey);
      await prefs.remove(_tokenKey);
      await prefs.remove(_refreshTokenKey);
      await prefs.remove('_useFirebaseSDK');

      // 如果不是匿名用戶登出，也清除設備級匿名帳號信息
      if (!preserveAnonymous) {
        await prefs.remove(_deviceAnonymousUidKey);
        await prefs.remove(_deviceAnonymousUserKey);
        await prefs.remove(_isDeviceAnonymousKey);
      } else {
        // 恢復設備級匿名帳號信息
        if (deviceAnonymousUid != null) {
          await prefs.setString(_deviceAnonymousUidKey, deviceAnonymousUid);
        }
        if (deviceAnonymousUserData != null) {
          await prefs.setString(_deviceAnonymousUserKey, deviceAnonymousUserData);
        }
        if (isDeviceAnonymous != null) {
          await prefs.setBool(_isDeviceAnonymousKey, isDeviceAnonymous);
        }
        logger.i('已恢復設備級匿名帳號信息');
      }

      // 清除內存中的狀態
      _currentUser = null;
      _idToken = null;
      _refreshToken = null;

      // 通知認證狀態變化
      _notifyAuthStateChange(null);

      logger.i('用戶會話已清除${preserveAnonymous ? '（保留匿名帳號信息）' : ''}');
    } catch (e) {
      logger.e('清除用戶會話失敗: $e');
    }
  }

  /// 獲取 Firebase 錯誤訊息
  static String _getFirebaseErrorMessage(String errorCode) {
    switch (errorCode) {
      case 'EMAIL_EXISTS':
        return '此電子郵件已被註冊';
      case 'OPERATION_NOT_ALLOWED':
        return '此操作不被允許';
      case 'TOO_MANY_ATTEMPTS_TRY_LATER':
        return '嘗試次數過多，請稍後再試';
      case 'EMAIL_NOT_FOUND':
        return '找不到此電子郵件帳戶';
      case 'INVALID_PASSWORD':
        return '密碼錯誤';
      case 'USER_DISABLED':
        return '此帳戶已被停用';
      case 'WEAK_PASSWORD':
        return '密碼強度不足';
      case 'INVALID_EMAIL':
        return '無效的電子郵件格式';
      case 'invalid-credential':
        return '認證憑證無效或已過期，請重新登入';
      case 'credential-already-in-use':
        return '此認證憑證已被其他帳戶使用';
      case 'invalid-verification-code':
        return '驗證碼無效';
      case 'invalid-verification-id':
        return '驗證 ID 無效';
      case 'session-cookie-expired':
        return '登入會話已過期，請重新登入';
      case 'id-token-expired':
        return 'ID 令牌已過期，請重新登入';
      case 'id-token-revoked':
        return 'ID 令牌已被撤銷，請重新登入';
      case 'token-expired':
        return '認證令牌已過期，請重新登入';
      case 'user-token-expired':
        return '用戶令牌已過期，請重新登入';
      case 'requires-recent-login':
        return '此操作需要重新登入驗證身份';
      case 'account-exists-with-different-credential':
        return '此電子郵件已與其他登入方式關聯';
      case 'auth-domain-config-required':
        return 'Firebase 認證域配置錯誤';
      case 'cancelled-popup-request':
        return '登入請求已取消';
      case 'popup-blocked':
        return '瀏覽器阻止了登入彈窗';
      case 'popup-closed-by-user':
        return '用戶關閉了登入彈窗';
      case 'unauthorized-domain':
        return '此域名未被授權進行 Firebase 認證';
      default:
        return '認證失敗：$errorCode';
    }
  }

  // ==================== 設備級匿名帳號管理方法 ====================

  /// 獲取設備級匿名用戶
  static Future<AppUser?> _getDeviceAnonymousUser() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final deviceId = await getOrCreateDeviceId();

      // 檢查是否有設備級匿名帳號記錄
      final isDeviceAnonymous = prefs.getBool(_isDeviceAnonymousKey) ?? false;
      final deviceAnonymousUserData = prefs.getString(_deviceAnonymousUserKey);

      if (!isDeviceAnonymous || deviceAnonymousUserData == null) {
        logger.d('設備 $deviceId 沒有匿名帳號記錄');
        return null;
      }

      try {
        final userJson = json.decode(deviceAnonymousUserData);
        final user = AppUser.fromJson(userJson);
        logger.i('找到設備級匿名帳號: ${user.uid} (設備: $deviceId)');
        return user;
      } catch (e) {
        logger.e('解析設備級匿名用戶數據失敗: $e');
        // 清除無效數據
        await _clearDeviceAnonymousUser();
        return null;
      }
    } catch (e) {
      logger.e('獲取設備級匿名用戶失敗: $e');
      return null;
    }
  }

  /// 保存設備級匿名用戶
  static Future<void> _saveDeviceAnonymousUser(AppUser user) async {
    try {
      if (!user.isAnonymous) {
        logger.w('嘗試保存非匿名用戶為設備級匿名帳號，忽略');
        return;
      }

      final prefs = await SharedPreferences.getInstance();
      final deviceId = await getOrCreateDeviceId();

      // 保存匿名用戶信息
      await prefs.setString(_deviceAnonymousUidKey, user.uid);
      await prefs.setString(_deviceAnonymousUserKey, json.encode(user.toJson()));
      await prefs.setBool(_isDeviceAnonymousKey, true);

      logger.i('設備級匿名帳號已保存: ${user.uid} (設備: $deviceId)');
    } catch (e) {
      logger.e('保存設備級匿名用戶失敗: $e');
    }
  }

  /// 恢復設備級匿名用戶
  static Future<AppUser?> _restoreDeviceAnonymousUser(AppUser deviceUser) async {
    try {
      logger.i('嘗試恢復設備級匿名帳號: ${deviceUser.uid}');

      if (_shouldUseRestApi()) {
        // REST API 模式下，直接使用保存的用戶信息
        // 但需要驗證 token 是否仍然有效
        final prefs = await SharedPreferences.getInstance();
        final savedToken = prefs.getString(_tokenKey);

        if (savedToken != null) {
          // 嘗試驗證 token
          try {
            await _validateTokenForUser(savedToken);
            // Token 有效，恢復會話
            await _saveUserSession(deviceUser, savedToken);
            return deviceUser;
          } catch (e) {
            logger.w('設備級匿名帳號 token 無效: $e');
            return null;
          }
        } else {
          logger.w('設備級匿名帳號沒有保存的 token');
          return null;
        }
      } else {
        // SDK 模式下，檢查 Firebase Auth 中是否還有此用戶
        final currentFirebaseUser = FirebaseAuth.instance.currentUser;

        if (currentFirebaseUser != null &&
            currentFirebaseUser.uid == deviceUser.uid &&
            currentFirebaseUser.isAnonymous) {
          // Firebase Auth 中仍有此匿名用戶，直接恢復
          logger.i('Firebase Auth 中找到匹配的匿名用戶，直接恢復');
          final restoredUser = AppUser(
            uid: currentFirebaseUser.uid,
            email: currentFirebaseUser.email,
            displayName: currentFirebaseUser.displayName,
            photoURL: currentFirebaseUser.photoURL,
            emailVerified: currentFirebaseUser.emailVerified,
            isAnonymous: currentFirebaseUser.isAnonymous,
            createdAt: currentFirebaseUser.metadata.creationTime ?? DateTime.now(),
          );

          await _saveUserSessionSDK(restoredUser);
          return restoredUser;
        } else if (currentFirebaseUser == null) {
          // Firebase Auth 中沒有當前用戶，但我們有設備級記錄
          // 這種情況下，我們嘗試使用保存的用戶信息直接恢復
          logger.i('Firebase Auth 中沒有當前用戶，嘗試直接恢復設備級匿名帳號');

          // 直接使用設備級保存的用戶信息
          await _saveUserSessionSDK(deviceUser);

          // 更新內存中的 Firebase Auth 狀態（如果可能）
          // 注意：這裡我們不能直接設置 Firebase Auth 的狀態，
          // 但我們可以依賴我們自己的狀態管理

          return deviceUser;
        } else {
          logger.w('Firebase Auth 中的用戶與設備級匿名帳號不匹配');
          logger.w('當前 Firebase 用戶: ${currentFirebaseUser.uid}, 設備級用戶: ${deviceUser.uid}');
          return null;
        }
      }
    } catch (e) {
      logger.e('恢復設備級匿名用戶失敗: $e');
      return null;
    }
  }

  /// 清除設備級匿名用戶記錄
  static Future<void> _clearDeviceAnonymousUser() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_deviceAnonymousUidKey);
      await prefs.remove(_deviceAnonymousUserKey);
      await prefs.remove(_isDeviceAnonymousKey);

      final deviceId = await getOrCreateDeviceId();
      logger.i('已清除設備級匿名帳號記錄 (設備: $deviceId)');
    } catch (e) {
      logger.e('清除設備級匿名用戶記錄失敗: $e');
    }
  }

  /// 驗證指定 token 的有效性
  static Future<void> _validateTokenForUser(String token) async {
    try {
      final response = await http
          .post(
            Uri.parse(
                'https://identitytoolkit.googleapis.com/v1/accounts:lookup?key=${FirebaseConfigWindows.apiKey}'),
            headers: {'Content-Type': 'application/json'},
            body: json.encode({'idToken': token}),
          )
          .timeout(const Duration(seconds: 10));

      if (response.statusCode != 200) {
        throw Exception('Token 驗證失敗，狀態碼: ${response.statusCode}');
      }

      logger.d('Token 驗證成功');
    } catch (e) {
      logger.w('Token 驗證失敗: $e');
      rethrow;
    }
  }

  // ==================== 設備指紋和雲端存儲方法 ====================

  /// 生成設備指紋
  static Future<String> _generateDeviceFingerprint() async {
    try {
      final deviceInfo = DeviceInfoPlugin();
      String fingerprint = '';

      if (Platform.isAndroid) {
        final androidInfo = await deviceInfo.androidInfo;
        fingerprint = '${androidInfo.brand}_${androidInfo.model}_${androidInfo.id}';
      } else if (Platform.isIOS) {
        final iosInfo = await deviceInfo.iosInfo;
        fingerprint = '${iosInfo.name}_${iosInfo.model}_${iosInfo.identifierForVendor}';
      } else if (Platform.isMacOS) {
        final macInfo = await deviceInfo.macOsInfo;
        fingerprint = '${macInfo.computerName}_${macInfo.model}_${macInfo.systemGUID}';
      } else if (Platform.isWindows) {
        final windowsInfo = await deviceInfo.windowsInfo;
        fingerprint = '${windowsInfo.computerName}_${windowsInfo.productName}_${windowsInfo.deviceId}';
      } else if (Platform.isLinux) {
        final linuxInfo = await deviceInfo.linuxInfo;
        fingerprint = '${linuxInfo.name}_${linuxInfo.id}_${linuxInfo.machineId}';
      } else if (kIsWeb) {
        final webInfo = await deviceInfo.webBrowserInfo;
        fingerprint = '${webInfo.browserName}_${webInfo.platform}_${webInfo.userAgent?.hashCode}';
      }

      // 如果無法獲取設備信息，使用設備ID作為備用
      if (fingerprint.isEmpty) {
        fingerprint = await getOrCreateDeviceId();
      }

      // 生成SHA256哈希作為最終指紋
      final bytes = utf8.encode(fingerprint);
      final digest = sha256.convert(bytes);
      final finalFingerprint = digest.toString();

      logger.d('設備指紋生成成功: $finalFingerprint');
      return finalFingerprint;
    } catch (e) {
      logger.e('生成設備指紋失敗: $e');
      // 備用方案：使用設備ID
      return await getOrCreateDeviceId();
    }
  }

  /// 從雲端獲取設備匿名用戶
  static Future<AppUser?> _getCloudDeviceAnonymousUser() async {
    try {
      final deviceFingerprint = await _generateDeviceFingerprint();
      logger.i('查詢雲端設備匿名帳號，設備指紋: ${deviceFingerprint.substring(0, 8)}...');

      final firestore = FirebaseFirestore.instance;
      final docSnapshot = await firestore
          .collection(_deviceAnonymousCollection)
          .doc(deviceFingerprint)
          .get();

      if (docSnapshot.exists) {
        final data = docSnapshot.data()!;
        final user = AppUser(
          uid: data['uid'] as String,
          email: data['email'] as String?,
          displayName: data['displayName'] as String?,
          photoURL: data['photoURL'] as String?,
          emailVerified: data['emailVerified'] as bool? ?? false,
          isAnonymous: data['isAnonymous'] as bool? ?? true,
          createdAt: (data['createdAt'] as Timestamp).toDate(),
        );

        logger.i('從雲端找到設備匿名帳號: ${user.uid}');
        return user;
      } else {
        logger.d('雲端沒有找到設備匿名帳號記錄');
        return null;
      }
    } catch (e) {
      logger.e('從雲端獲取設備匿名用戶失敗: $e');
      return null;
    }
  }

  /// 保存設備匿名用戶到雲端
  static Future<void> _saveCloudDeviceAnonymousUser(AppUser user) async {
    try {
      if (!user.isAnonymous) {
        logger.w('嘗試保存非匿名用戶到雲端，忽略');
        return;
      }

      final deviceFingerprint = await _generateDeviceFingerprint();
      logger.i('保存設備匿名帳號到雲端: ${user.uid}');

      final firestore = FirebaseFirestore.instance;
      final docRef = firestore
          .collection(_deviceAnonymousCollection)
          .doc(deviceFingerprint);

      // 檢查是否已存在記錄
      final docSnapshot = await docRef.get();

      if (docSnapshot.exists) {
        // 更新現有記錄，保持原始創建時間
        await docRef.update({
          'uid': user.uid,
          'email': user.email,
          'displayName': user.displayName,
          'photoURL': user.photoURL,
          'emailVerified': user.emailVerified,
          'isAnonymous': user.isAnonymous,
          'updatedAt': FieldValue.serverTimestamp(),
          'lastUsedAt': FieldValue.serverTimestamp(),
        });
        logger.i('設備匿名帳號已更新到雲端: ${user.uid}');
      } else {
        // 創建新記錄
        await docRef.set({
          'uid': user.uid,
          'email': user.email,
          'displayName': user.displayName,
          'photoURL': user.photoURL,
          'emailVerified': user.emailVerified,
          'isAnonymous': user.isAnonymous,
          'createdAt': Timestamp.fromDate(user.createdAt ?? DateTime.now()),
          'updatedAt': FieldValue.serverTimestamp(),
          'lastUsedAt': FieldValue.serverTimestamp(),
          'deviceFingerprint': deviceFingerprint,
        });
        logger.i('設備匿名帳號已保存到雲端: ${user.uid}');
      }
    } catch (e) {
      logger.e('保存設備匿名用戶到雲端失敗: $e');
      // 不拋出異常，因為雲端保存失敗不應該影響登入流程
    }
  }

  /// 恢復雲端匿名用戶
  static Future<AppUser?> _restoreCloudAnonymousUser(AppUser cloudUser) async {
    try {
      logger.i('嘗試恢復雲端匿名帳號: ${cloudUser.uid}');

      if (_shouldUseRestApi()) {
        // REST API 模式下，直接使用雲端記錄的用戶信息
        logger.i('REST API 模式下直接使用雲端匿名帳號資料');
        return await _forceRestoreCloudAnonymousUser(cloudUser);
      } else {
        // SDK 模式下，檢查 Firebase Auth 中是否有匹配的用戶
        final currentFirebaseUser = FirebaseAuth.instance.currentUser;

        if (currentFirebaseUser != null &&
            currentFirebaseUser.uid == cloudUser.uid &&
            currentFirebaseUser.isAnonymous) {
          // Firebase Auth 中有匹配的用戶，直接恢復
          logger.i('Firebase Auth 中找到匹配的雲端匿名用戶');
          await _saveUserSessionSDK(cloudUser);
          return cloudUser;
        } else {
          // Firebase Auth 中沒有匹配的用戶
          // 直接取原本 device_anonymous_accounts 內的資料，回查 Cloud Firestore，恢復原本匿名登入
          logger.w('Firebase Auth 中找不到匹配的雲端匿名用戶，嘗試強制恢復');
          return await _forceRestoreCloudAnonymousUser(cloudUser);
        }
      }
    } catch (e) {
      logger.e('恢復雲端匿名用戶失敗: $e');
      return null;
    }
  }

  /// 清除雲端設備匿名用戶記錄
  static Future<void> _clearCloudDeviceAnonymousUser() async {
    try {
      final deviceFingerprint = await _generateDeviceFingerprint();
      logger.i('清除雲端設備匿名帳號記錄');

      final firestore = FirebaseFirestore.instance;
      await firestore
          .collection(_deviceAnonymousCollection)
          .doc(deviceFingerprint)
          .delete();

      logger.i('已清除雲端設備匿名帳號記錄');
    } catch (e) {
      logger.e('清除雲端設備匿名用戶記錄失敗: $e');
    }
  }

  /// 在其他帳號登入前保存匿名用戶狀態
  static Future<void> _preserveAnonymousUserBeforeOtherSignIn() async {
    try {
      final currentUser = FirebaseAuth.instance.currentUser;

      // 檢查當前是否有匿名用戶
      if (currentUser != null && currentUser.isAnonymous) {
        logger.i('檢測到當前有匿名用戶，在其他帳號登入前保存狀態: ${currentUser.uid}');

        // 創建 AppUser 對象
        final anonymousUser = AppUser(
          uid: currentUser.uid,
          email: currentUser.email,
          displayName: currentUser.displayName,
          photoURL: currentUser.photoURL,
          emailVerified: currentUser.emailVerified,
          isAnonymous: currentUser.isAnonymous,
          createdAt: currentUser.metadata.creationTime ?? DateTime.now(),
        );

        // 保存到本地和雲端
        await _saveDeviceAnonymousUser(anonymousUser);
        await _saveCloudDeviceAnonymousUser(anonymousUser);

        logger.i('匿名用戶狀態已保存，UID: ${anonymousUser.uid}');
      } else {
        logger.d('當前沒有匿名用戶，無需保存狀態');
      }
    } catch (e) {
      logger.e('保存匿名用戶狀態失敗: $e');
      // 不拋出異常，因為這不應該阻止其他帳號的登入
    }
  }

  /// 強制恢復雲端匿名用戶
  /// 直接取原本 device_anonymous_accounts 內的資料，回查 Cloud Firestore，恢復原本匿名登入
  static Future<AppUser?> _forceRestoreCloudAnonymousUser(AppUser cloudUser) async {
    try {
      logger.i('開始強制恢復雲端匿名帳號: ${cloudUser.uid}');

      // 1. 驗證雲端記錄是否仍然存在
      final deviceFingerprint = await _generateDeviceFingerprint();
      final firestore = FirebaseFirestore.instance;

      final docSnapshot = await firestore
          .collection(_deviceAnonymousCollection)
          .doc(deviceFingerprint)
          .get();

      if (!docSnapshot.exists) {
        logger.w('雲端設備匿名帳號記錄已不存在，無法強制恢復');
        return null;
      }

      // 2. 確認雲端記錄與傳入的用戶資料一致
      final cloudData = docSnapshot.data()!;
      if (cloudData['uid'] != cloudUser.uid) {
        logger.w('雲端記錄 UID 不匹配，無法強制恢復');
        logger.w('雲端 UID: ${cloudData['uid']}, 傳入 UID: ${cloudUser.uid}');
        return null;
      }

      // 3. 直接使用雲端記錄的用戶資料
      final restoredUser = AppUser(
        uid: cloudData['uid'] as String,
        email: cloudData['email'] as String?,
        displayName: cloudData['displayName'] as String?,
        photoURL: cloudData['photoURL'] as String?,
        emailVerified: cloudData['emailVerified'] as bool? ?? false,
        isAnonymous: cloudData['isAnonymous'] as bool? ?? true,
        createdAt: (cloudData['createdAt'] as Timestamp).toDate(),
      );

      logger.i('成功從雲端恢復匿名用戶資料: ${restoredUser.uid}');

      // 4. 保存到本地設備級匿名帳號
      await _saveDeviceAnonymousUser(restoredUser);

      // 5. 建立用戶會話
      if (_shouldUseRestApi()) {
        // REST API 模式下，我們無法直接恢復 Firebase Auth 狀態
        // 但我們可以嘗試創建新的匿名用戶並使用雲端資料
        logger.i('REST API 模式下創建新匿名用戶並使用雲端資料');

        // 創建新的匿名用戶
        final newAnonymousUser = await _signInAnonymouslyViaRestApi();
        if (newAnonymousUser != null) {
          // 使用雲端資料更新新用戶的資訊（保持原始創建時間）
          final inheritedUser = AppUser(
            uid: newAnonymousUser.uid, // 使用新的 Firebase UID
            email: restoredUser.email, // 繼承雲端記錄的信息
            displayName: restoredUser.displayName,
            photoURL: restoredUser.photoURL,
            emailVerified: restoredUser.emailVerified,
            isAnonymous: true,
            createdAt: restoredUser.createdAt, // 保持原始創建時間
          );

          // 更新雲端記錄為新的 UID
          await firestore
              .collection(_deviceAnonymousCollection)
              .doc(deviceFingerprint)
              .update({
            'uid': inheritedUser.uid,
            'updatedAt': FieldValue.serverTimestamp(),
          });

          logger.i('REST API 模式強制恢復成功，新 UID: ${inheritedUser.uid}');
          return inheritedUser;
        } else {
          logger.e('REST API 模式創建新匿名用戶失敗');
          return null;
        }
      } else {
        // SDK 模式下，建立會話
        await _saveUserSessionSDK(restoredUser);
      }

      // 6. 更新雲端記錄的最後使用時間
      await firestore
          .collection(_deviceAnonymousCollection)
          .doc(deviceFingerprint)
          .update({
        'lastUsedAt': FieldValue.serverTimestamp(),
        'updatedAt': FieldValue.serverTimestamp(),
      });

      logger.i('強制恢復雲端匿名帳號成功: ${restoredUser.uid}');
      return restoredUser;

    } catch (e) {
      logger.e('強制恢復雲端匿名用戶失敗: $e');
      return null;
    }
  }
}

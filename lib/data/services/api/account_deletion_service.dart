import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:firebase_storage/firebase_storage.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../../../core/constants/firebase_collections.dart';
import '../../../core/utils/logger_utils.dart';
import 'birth_data_service.dart';

/// 帳戶刪除服務
/// 負責完整刪除用戶帳戶及所有相關資料
class AccountDeletionService {
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  static final FirebaseStorage _storage = FirebaseStorage.instance;

  /// 完整刪除用戶帳戶及所有資料
  /// 包括：Firebase Auth、Firestore 資料、Firebase Storage 檔案、本地資料
  static Future<void> deleteUserAccount([String? userId]) async {
    final currentUser = FirebaseAuth.instance.currentUser;
    String? targetUserId = userId;
    User? userToDelete = currentUser;

    // 如果沒有提供 userId，嘗試從 currentUser 獲取
    if (targetUserId == null) {
      if (currentUser != null) {
        targetUserId = currentUser.uid;
      } else {
        throw Exception('用戶未登入且未提供用戶ID，無法刪除帳戶');
      }
    }

    // 如果沒有 currentUser 但有 userId，嘗試從其他來源獲取用戶資訊
    if (currentUser == null) {
      logger.i('沒有 Firebase Auth currentUser，使用提供的用戶ID進行刪除: $targetUserId');

      // 嘗試從 Firestore 獲取用戶資訊以確認用戶存在
      try {
        final userDoc = await _firestore.collection('user_profiles').doc(targetUserId).get();
        if (userDoc.exists) {
          logger.i('在 Firestore 中找到用戶資料，繼續刪除流程');
        } else {
          logger.w('在 Firestore 中未找到用戶資料，但仍繼續刪除流程');
        }
      } catch (e) {
        logger.w('查詢 Firestore 用戶資料時出錯: $e，但仍繼續刪除流程');
      }

      userToDelete = null; // 設為 null，表示沒有 Firebase Auth 用戶需要刪除
    }

    logger.i('開始刪除用戶帳戶: $targetUserId');

    try {
      // 1. 刪除 Firebase Storage 中的所有用戶檔案
      await _deleteUserStorageFiles(targetUserId);

      // 2. 刪除 Firestore 中的所有用戶資料（強制刪除）
      await _forceDeleteUserFirestoreData(targetUserId);

      // 3. 刪除本地資料
      await _deleteLocalData();

      // 4. 最後刪除 Firebase Auth 用戶帳戶（如果存在）
      if (userToDelete != null) {
        await _deleteFirebaseAuthUser(userToDelete);
      } else {
        logger.i('沒有 Firebase Auth 用戶需要刪除，跳過此步驟');
      }

      logger.i('用戶帳戶刪除完成: $targetUserId');

    } catch (e) {
      logger.e('刪除用戶帳戶時發生錯誤: $e');
      rethrow;
    }
  }

  /// 根據用戶ID刪除用戶帳戶（不依賴 Firebase Auth currentUser）
  /// 這個方法適用於匿名用戶登出後或 Firebase Auth 狀態不一致的情況
  static Future<void> deleteUserAccountById(String userId) async {
    logger.i('根據用戶ID刪除帳戶: $userId');
    await deleteUserAccount(userId);
  }

  /// 強制刪除 Firestore 中的用戶資料（不拋出異常）
  static Future<void> _forceDeleteUserFirestoreData(String userId) async {
    logger.i('開始強制刪除 Firestore 用戶資料: $userId');

    // 定義所有可能的集合
    final allCollections = FirebaseCollections.allUserDataCollections;

    // 強制刪除每個集合中的用戶文檔
    for (final collection in allCollections) {
      try {
        await _firestore.collection(collection).doc(userId).delete();
        logger.d('已刪除: $collection/$userId');
      } catch (e) {
        logger.d('刪除 $collection/$userId 時出錯（可能不存在）: $e');
        // 不拋出異常，繼續刪除其他資料
      }
    }

    // 強制刪除支付相關資料
    await _forceDeletePaymentData(userId);

    // 強制刪除其他可能的用戶資料
    await _forceDeleteOtherUserData(userId);

    // 強制刪除設備匿名帳號資料
    await _forceDeleteDeviceAnonymousAccounts(userId);

    logger.i('強制刪除 Firestore 用戶資料完成: $userId');
  }

  /// 強制刪除支付相關資料
  static Future<void> _forceDeletePaymentData(String userId) async {
    try {
      // 刪除 payments 集合中的用戶記錄
      final paymentsQuery = await _firestore
          .collection(FirebaseCollections.payments)
          .where('user_id', isEqualTo: userId)
          .limit(100)  // 限制批次大小
          .get();

      if (paymentsQuery.docs.isNotEmpty) {
        final batch = _firestore.batch();
        for (final doc in paymentsQuery.docs) {
          batch.delete(doc.reference);
        }
        await batch.commit();
        logger.d('已刪除 payments 集合中的用戶記錄 (${paymentsQuery.docs.length} 筆)');
      }

      // 刪除 user_payments 集合下的用戶文檔和子集合
      await _forceDeleteUserPaymentsCollection(userId);

    } catch (e) {
      logger.w('強制刪除支付資料時出錯: $e');
    }
  }

  /// 強制刪除 user_payments 集合
  static Future<void> _forceDeleteUserPaymentsCollection(String userId) async {
    try {
      // 刪除 user_payments/{userId}/payments 子集合
      final paymentsSubcollection = _firestore
          .collection(FirebaseCollections.userPayments)
          .doc(userId)
          .collection('payments');

      final paymentsQuery = await paymentsSubcollection.limit(100).get();

      if (paymentsQuery.docs.isNotEmpty) {
        final batch = _firestore.batch();
        for (final doc in paymentsQuery.docs) {
          batch.delete(doc.reference);
        }
        await batch.commit();
        logger.d('已刪除 user_payments/$userId/payments 子集合 (${paymentsQuery.docs.length} 筆)');
      }

      // 刪除 user_payments/{userId} 主文檔
      await _firestore.collection(FirebaseCollections.userPayments).doc(userId).delete();
      logger.d('已刪除 user_payments/$userId 主文檔');

    } catch (e) {
      logger.w('強制刪除 user_payments 集合時出錯: $e');
    }
  }

  /// 強制刪除其他可能的用戶資料
  static Future<void> _forceDeleteOtherUserData(String userId) async {
    // 定義其他可能包含用戶資料的集合
    final otherCollections = [
      FirebaseCollections.birthData,
      FirebaseCollections.usageLogs,
      FirebaseCollections.userAnalytics,
      FirebaseCollections.userFeedback,
      FirebaseCollections.userNotifications,
      FirebaseCollections.divinationRecords,
    ];

    for (final collection in otherCollections) {
      try {
        final query = await _firestore
            .collection(collection)
            .where('userId', isEqualTo: userId)
            .limit(100)
            .get();

        if (query.docs.isNotEmpty) {
          final batch = _firestore.batch();
          for (final doc in query.docs) {
            batch.delete(doc.reference);
          }
          await batch.commit();
          logger.d('已刪除 $collection 中的用戶資料 (${query.docs.length} 筆)');
        }
      } catch (e) {
        logger.w('強制刪除 $collection 中的用戶資料時出錯: $e');
      }
    }
  }

  /// 刪除 Firebase Storage 中的用戶檔案
  static Future<void> _deleteUserStorageFiles(String userId) async {
    try {
      logger.i('開始刪除 Firebase Storage 用戶檔案: $userId');

      // 刪除備份檔案
      try {
        final backupRef = _storage.ref().child('user_backups/$userId');
        await _deleteStorageFolder(backupRef);
        logger.i('已刪除用戶備份檔案');
      } catch (e) {
        logger.w('刪除備份檔案時出錯（可能不存在）: $e');
      }

      // 刪除其他可能的用戶檔案（如頭像、附件等）
      try {
        final userFilesRef = _storage.ref().child('user_files/$userId');
        await _deleteStorageFolder(userFilesRef);
        logger.i('已刪除用戶檔案');
      } catch (e) {
        logger.w('刪除用戶檔案時出錯（可能不存在）: $e');
      }

      // 刪除用戶頭像
      try {
        final avatarRef = _storage.ref().child('avatars/$userId');
        await _deleteStorageFolder(avatarRef);
        logger.i('已刪除用戶頭像');
      } catch (e) {
        logger.w('刪除用戶頭像時出錯（可能不存在）: $e');
      }

    } catch (e) {
      logger.e('刪除 Firebase Storage 用戶檔案失敗: $e');
      throw Exception('刪除雲端檔案失敗: $e');
    }
  }

  /// 遞迴刪除 Storage 資料夾
  static Future<void> _deleteStorageFolder(Reference folderRef) async {
    try {
      // 列出資料夾中的所有項目
      final listResult = await folderRef.listAll();

      // 刪除所有檔案
      for (final item in listResult.items) {
        await item.delete();
        logger.d('已刪除檔案: ${item.fullPath}');
      }

      // 遞迴刪除子資料夾
      for (final prefix in listResult.prefixes) {
        await _deleteStorageFolder(prefix);
      }

    } catch (e) {
      logger.w('刪除 Storage 資料夾時出錯: ${folderRef.fullPath}, 錯誤: $e');
      // 不拋出異常，因為資料夾可能不存在
    }
  }

  /// 刪除用戶的子集合
  static Future<void> _deleteUserSubcollections(String userId) async {
    try {
      // 刪除用戶專屬的子集合（如果存在）
      // 這些是以 userId 為文檔 ID 的集合下的子集合

      logger.i('開始刪除用戶子集合資料: $userId');

      // 目前專案中主要的子集合已在主要方法中處理
      // 這裡可以添加其他特殊的子集合處理邏輯

      logger.i('成功刪除用戶子集合資料: $userId');
    } catch (e) {
      logger.w('刪除用戶子集合時出錯: $e');
    }
  }

  /// 刪除指定集合下的子集合
  static Future<void> _deleteSubcollection(
    FirebaseFirestore firestore,
    String parentCollection,
    String documentId,
    String subcollection,
  ) async {
    try {
      final subcollectionRef = firestore
          .collection(parentCollection)
          .doc(documentId)
          .collection(subcollection);

      final query = await subcollectionRef.get();

      if (query.docs.isNotEmpty) {
        final batch = firestore.batch();
        for (final doc in query.docs) {
          batch.delete(doc.reference);
        }
        await batch.commit();
        logger.i('已刪除子集合 $parentCollection/$documentId/$subcollection (${query.docs.length} 筆)');
      }
    } catch (e) {
      logger.w('刪除子集合 $parentCollection/$documentId/$subcollection 時出錯: $e');
    }
  }

  /// 刪除本地資料
  static Future<void> _deleteLocalData() async {
    try {
      logger.i('開始刪除本地資料');

      // 刪除本地出生資料
      final birthDataService = BirthDataService();
      await birthDataService.clearAllBirthData();
      logger.i('已清除本地出生資料');

      // 清除 SharedPreferences 中的設備級匿名帳號記錄
      await _clearDeviceAnonymousAccountsFromLocal();

      // 清除其他 SharedPreferences 中的用戶相關資料
      await _clearOtherUserDataFromLocal();

    } catch (e) {
      logger.e('刪除本地資料失敗: $e');
      throw Exception('刪除本地資料失敗: $e');
    }
  }

  /// 清除本地設備級匿名帳號記錄
  static Future<void> _clearDeviceAnonymousAccountsFromLocal() async {
    try {
      logger.i('開始清除本地設備級匿名帳號記錄');

      final prefs = await SharedPreferences.getInstance();

      // 清除設備級匿名帳號相關的所有鍵值
      // 這些鍵值來自 FirebaseAuthService
      const deviceAnonymousKeys = [
        'device_anonymous_uid',           // _deviceAnonymousUidKey
        'device_anonymous_user_data',     // _deviceAnonymousUserKey
        'is_device_anonymous',            // _isDeviceAnonymousKey
        'device_fingerprint',             // _deviceFingerprintKey
      ];

      int clearedCount = 0;
      for (final key in deviceAnonymousKeys) {
        if (prefs.containsKey(key)) {
          await prefs.remove(key);
          clearedCount++;
          logger.d('已清除本地鍵值: $key');
        }
      }

      logger.i('已清除 $clearedCount 個本地設備級匿名帳號記錄');

    } catch (e) {
      logger.w('清除本地設備級匿名帳號記錄時出錯: $e');
      // 不拋出異常，繼續其他清理操作
    }
  }

  /// 清除認證相關的本地資料
  static Future<void> _clearOtherUserDataFromLocal() async {
    try {
      logger.i('開始清除認證相關的本地資料');

      final prefs = await SharedPreferences.getInstance();

      // 只清除明確的認證相關鍵值，避免影響應用正常運作
      const authKeysToRemove = [
        'firebase_auth_token',      // _tokenKey
        'firebase_user_data',       // _userKey
        'firebase_refresh_token',   // _refreshTokenKey
        '_useFirebaseSDK',          // SDK 模式標記
      ];

      int clearedCount = 0;
      for (final key in authKeysToRemove) {
        if (prefs.containsKey(key)) {
          await prefs.remove(key);
          clearedCount++;
          logger.d('已清除認證相關鍵值: $key');
        }
      }

      logger.i('已清除 $clearedCount 個認證相關的本地資料');

      // 記錄保留的重要設定（用於除錯）
      const importantKeys = [
        'has_selected_mode',        // 導航：是否已選擇模式
        'user_mode',                // 導航：用戶模式
        'theme_mode',               // 主題：主題模式
        'theme_preference',         // 主題：主題偏好
      ];

      logger.d('保留的重要設定:');
      for (final key in importantKeys) {
        if (prefs.containsKey(key)) {
          final value = prefs.get(key);
          logger.d('  $key: $value');
        } else {
          logger.d('  $key: (不存在)');
        }
      }

    } catch (e) {
      logger.w('清除認證相關本地資料時出錯: $e');
      // 不拋出異常，繼續其他清理操作
    }
  }

  /// 刪除 Firebase Auth 用戶帳戶
  static Future<void> _deleteFirebaseAuthUser(User user) async {
    try {
      logger.i('開始刪除 Firebase Auth 用戶帳戶');

      // 檢查是否為匿名用戶且當前沒有 Firebase Auth 用戶
      if (user.isAnonymous) {
        final currentUser = FirebaseAuth.instance.currentUser;
        if (currentUser == null) {
          logger.i('匿名用戶且沒有 FirebaseAuth.instance.currentUser，跳過 Firebase Auth 刪除');
          return;
        }

        // 確認當前用戶與要刪除的用戶是同一個
        if (currentUser.uid != user.uid) {
          logger.w('當前 Firebase Auth 用戶與要刪除的用戶不匹配');
          logger.w('當前用戶 UID: ${currentUser.uid}, 要刪除的用戶 UID: ${user.uid}');
          logger.i('跳過 Firebase Auth 刪除');
          return;
        }
      }

      // 刪除 Firebase Auth 用戶
      await user.delete();
      logger.i('已刪除 Firebase Auth 用戶帳戶');

    } catch (e) {
      logger.e('刪除 Firebase Auth 用戶帳戶失敗: $e');
      throw Exception('刪除用戶帳戶失敗: $e');
    }
  }

  /// 強制刪除設備匿名帳號資料
  static Future<void> _forceDeleteDeviceAnonymousAccounts(String userId) async {
    try {
      logger.i('開始刪除設備匿名帳號資料: $userId');

      // device_anonymous_accounts 集合使用設備指紋作為文檔ID，而不是用戶ID
      // 我們需要查詢所有包含此用戶ID的文檔
      final deviceAnonymousQuery = await _firestore
          .collection('device_anonymous_accounts')
          .where('uid', isEqualTo: userId)
          .limit(100)  // 限制批次大小，理論上一個用戶應該只有一個設備記錄
          .get();

      if (deviceAnonymousQuery.docs.isNotEmpty) {
        final batch = _firestore.batch();
        for (final doc in deviceAnonymousQuery.docs) {
          batch.delete(doc.reference);
          logger.d('標記刪除設備匿名帳號記錄: ${doc.id}');
        }
        await batch.commit();
        logger.i('已刪除 ${deviceAnonymousQuery.docs.length} 筆設備匿名帳號記錄');
      } else {
        logger.d('沒有找到用戶的設備匿名帳號記錄: $userId');
      }

    } catch (e) {
      logger.w('強制刪除設備匿名帳號資料時出錯: $e');
      // 不拋出異常，繼續其他刪除操作
    }
  }

  /// 檢查用戶是否可以刪除帳戶
  /// 某些情況下可能需要額外驗證（如重新驗證密碼）
  static Future<bool> canDeleteAccount() async {
    final currentUser = FirebaseAuth.instance.currentUser;
    if (currentUser == null) {
      return false;
    }

    // 檢查用戶最近是否重新驗證過
    // Firebase 要求在刪除帳戶前進行重新驗證
    try {
      // 這裡可以添加重新驗證的邏輯
      return true;
    } catch (e) {
      logger.w('檢查帳戶刪除權限時出錯: $e');
      return false;
    }
  }

  /// 重新驗證用戶（刪除帳戶前可能需要）
  static Future<void> reauthenticateUser(String password) async {
    final currentUser = FirebaseAuth.instance.currentUser;
    if (currentUser == null) {
      throw Exception('用戶未登入');
    }

    try {
      final credential = EmailAuthProvider.credential(
        email: currentUser.email!,
        password: password,
      );

      await currentUser.reauthenticateWithCredential(credential);
      logger.i('用戶重新驗證成功');
    } catch (e) {
      logger.e('用戶重新驗證失敗: $e');
      throw Exception('重新驗證失敗: $e');
    }
  }
}

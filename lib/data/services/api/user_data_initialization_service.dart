import 'dart:async';
import 'dart:convert';
import 'dart:io';

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/foundation.dart' as foundation;
import 'package:http/http.dart' as http;

import '../../../core/config/firebase_config_windows.dart';
import '../../../core/constants/firebase_collections.dart';
import '../../../core/utils/logger_utils.dart';
import '../../models/user/user_profile.dart';

/// 用戶數據初始化服務
/// 負責在用戶註冊或匿名登入時初始化所有必要的用戶數據
class UserDataInitializationService {

  /// 初始化新用戶的完整數據
  static Future<void> initializeNewUserData(String userId, {
    String? email,
    String? displayName,
    bool isAnonymous = false,
  }) async {
    try {
      logger.i('開始初始化用戶 $userId 的完整數據');

      // 並行初始化各種數據，提高效率
      final futures = <Future<void>>[
        // 1. 初始化用戶檔案
        _initializeUserProfile(userId, email: email, displayName: displayName, isAnonymous: isAnonymous),

        // 2. 初始化用戶設定
        // _initializeUserSettings(userId),

        // 3. 初始化用戶偏好
        // _initializeUserPreferences(userId),

        // 4. 初始化支付相關數據
        // _initializePaymentData(userId),

        // 注意：免費試用記錄初始化已移除，因為免費試用功能已不再使用
      ];

      // 等待所有初始化完成
      await Future.wait(futures);

      logger.i('用戶 $userId 的完整數據初始化成功');
    } catch (e) {
      logger.e('初始化用戶 $userId 的數據失敗: $e');
      // 不拋出異常，避免影響註冊/登入流程
    }
  }

  /// 初始化用戶檔案
  static Future<void> _initializeUserProfile(String userId, {
    String? email,
    String? displayName,
    bool isAnonymous = false,
  }) async {
    try {
      // 檢查是否已存在用戶檔案
      final existingProfile = await _getUserProfile(userId);
      if (existingProfile != null) {
        logger.d('用戶 $userId 檔案已存在，跳過初始化');
        return;
      }

      // 創建用戶檔案
      final now = DateTime.now();
      final userProfile = UserProfile(
        userId: userId,
        email: email,
        displayName: displayName ?? (isAnonymous ? '匿名用戶' : '新用戶'),
        isAnonymous: isAnonymous,
        createdAt: now,
        updatedAt: now,
        profileCompleted: false,
        lastLoginAt: now,
        loginCount: 1,
        interpretationCredits: 0,  // 可用解讀次數
        creditsLastUpdated: now,
      );

      final success = await _setUserProfile(userId, userProfile.toFirestoreJson());
      if (success) {
        logger.d('用戶 $userId 檔案初始化完成');
      } else {
        logger.e('用戶 $userId 檔案初始化失敗');
      }
    } catch (e) {
      logger.e('初始化用戶 $userId 檔案失敗: $e');
    }
  }

  // ==================== 輔助方法 ====================

  /// 獲取用戶檔案
  static Future<UserProfile?> _getUserProfile(String userId) async {
    try {
      Map<String, dynamic>? data;
      if (Platform.isWindows) {
        data = await _getUserProfileViaRestApi(userId);
      } else {
        data = await _getUserProfileViaSDK(userId);
      }

      if (data != null) {
        return UserProfile.fromJson(data);
      }
      return null;
    } catch (e) {
      logger.e('獲取用戶檔案失敗: $e');
      return null;
    }
  }

  /// 通過 REST API 獲取用戶檔案
  static Future<Map<String, dynamic>?> _getUserProfileViaRestApi(String userId) async {
    try {
      final url = FirebaseConfigWindows.getDocumentUrl(FirebaseCollections.userProfiles, userId);

      final response = await http.get(Uri.parse(url));

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        if (data != null && data is Map<String, dynamic>) {
          logger.d('通過 REST API 獲取用戶檔案成功: $userId');
          return data;
        }
      }

      logger.d('用戶檔案不存在: $userId');
      return null;
    } catch (e) {
      logger.e('通過 REST API 獲取用戶檔案失敗: $e');
      return null;
    }
  }

  /// 通過 SDK 獲取用戶檔案
  static Future<Map<String, dynamic>?> _getUserProfileViaSDK(String userId) async {
    try {
      // 使用 Firestore SDK 獲取用戶檔案
      final firestore = FirebaseFirestore.instance;
      final docRef = firestore.collection(FirebaseCollections.userProfiles).doc(userId);
      final docSnapshot = await docRef.get();

      if (docSnapshot.exists) {
        final data = docSnapshot.data();
        if (data != null) {
          logger.d('通過 SDK 獲取用戶檔案成功: $userId');
          return data;
        }
      }

      logger.d('用戶檔案不存在: $userId');
      return null;
    } on FirebaseException catch (e) {
      logger.e('Firestore 錯誤 - 獲取用戶檔案失敗: ${e.code} - ${e.message}');
      return null;
    } catch (e) {
      logger.e('通過 SDK 獲取用戶檔案失敗: $e');
      return null;
    }
  }

  /// 設置用戶檔案
  static Future<bool> _setUserProfile(String userId, Map<String, dynamic> data) async {
    try {
      if (foundation.kIsWeb) {
        return await _setUserProfileViaSDK(userId, data);
      } else if (Platform.isIOS) {
        return await _setUserProfileViaSDK(userId, data);
      } else if (Platform.isAndroid) {
        return await _setUserProfileViaSDK(userId, data);
      } else if (Platform.isMacOS) {
        return await _setUserProfileViaSDK(userId, data);
      } else if (Platform.isWindows) {
        return await _setUserProfileViaRestApi(userId, data);
      } else if (Platform.isLinux) {
        return await _setUserProfileViaSDK(userId, data);
      } else {
        return await _setUserProfileViaSDK(userId, data);
      }
    } catch (e) {
      logger.e('設置用戶檔案失敗: $e');
      return false;
    }
  }

  /// 通過 REST API 設置用戶檔案
  static Future<bool> _setUserProfileViaRestApi(String userId, Map<String, dynamic> data) async {
    try {
      final url = FirebaseConfigWindows.getDocumentUrl(FirebaseCollections.userProfiles, userId);

      final response = await http.put(
        Uri.parse(url),
        headers: {'Content-Type': 'application/json'},
        body: json.encode(data),
      );

      if (response.statusCode == 200) {
        logger.i('通過 REST API 設置用戶檔案成功: $userId');
        return true;
      } else {
        logger.e('通過 REST API 設置用戶檔案失敗: ${response.statusCode} - ${response.body}');
        return false;
      }
    } catch (e) {
      logger.e('通過 REST API 設置用戶檔案失敗: $e');
      return false;
    }
  }

  /// 通過 SDK 設置用戶檔案
  static Future<bool> _setUserProfileViaSDK(String userId, Map<String, dynamic> data) async {
    try {
      // 使用 Firestore SDK 設置用戶檔案
      final firestore = FirebaseFirestore.instance;
      final docRef = firestore.collection(FirebaseCollections.userProfiles).doc(userId);

      // 添加伺服器時間戳
      data['updated_at'] = FieldValue.serverTimestamp();
      if (!data.containsKey('created_at')) {
        data['created_at'] = FieldValue.serverTimestamp();
      }

      // 使用 merge 選項保留現有欄位
      await docRef.set(data, SetOptions(merge: true));
      logger.i('通過 SDK 設置用戶檔案成功: $userId');
      return true;
    } on FirebaseException catch (e) {
      logger.e('Firestore 錯誤 - 設置用戶檔案失敗: ${e.code} - ${e.message}');
      return false;
    } catch (e) {
      logger.e('通過 SDK 設置用戶檔案失敗: $e');
      return false;
    }
  }

  /// 獲取用戶設定
  static Future<Map<String, dynamic>?> _getUserSettings(String userId) async {
    try {
      if (Platform.isWindows) {
        return await _getUserSettingsViaRestApi(userId);
      } else {
        return await _getUserSettingsViaSDK(userId);
      }
    } catch (e) {
      logger.e('獲取用戶設定失敗: $e');
      return null;
    }
  }

  /// 通過 REST API 獲取用戶設定
  static Future<Map<String, dynamic>?> _getUserSettingsViaRestApi(String userId) async {
    try {
      final url = FirebaseConfigWindows.getDocumentUrl('user_settings', userId);

      final response = await http.get(Uri.parse(url));

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        if (data != null && data is Map<String, dynamic>) {
          logger.d('通過 REST API 獲取用戶設定成功: $userId');
          return data;
        }
      }

      logger.d('用戶設定不存在: $userId');
      return null;
    } catch (e) {
      logger.e('通過 REST API 獲取用戶設定失敗: $e');
      return null;
    }
  }

  /// 通過 SDK 獲取用戶設定
  static Future<Map<String, dynamic>?> _getUserSettingsViaSDK(String userId) async {
    try {
      // 使用 Firestore SDK 獲取用戶設定
      // final firestore = FirebaseFirestore.instance;
      // final docRef = firestore.collection('user_settings').doc(userId);
      // final docSnapshot = await docRef.get();

      // if (docSnapshot.exists) {
      //   final data = docSnapshot.data() as Map<String, dynamic>?;
      //   if (data != null) {
      //     logger.d('通過 SDK 獲取用戶設定成功: $userId');
      //     return data;
      //   }
      // }

      // logger.d('用戶設定不存在: $userId');
      // return null;

      // 暫時實現：由於 cloud_firestore 被註釋，返回 null
      logger.w('SDK 版本需要 cloud_firestore 依賴，暫時返回 null');
      return null;
    } catch (e) {
      logger.e('通過 SDK 獲取用戶設定失敗: $e');
      return null;
    }
  }

  /// 設置用戶設定
  static Future<bool> _setUserSettings(String userId, Map<String, dynamic> data) async {
    try {
      if (Platform.isWindows) {
        return await _setUserSettingsViaRestApi(userId, data);
      } else {
        return await _setUserSettingsViaSDK(userId, data);
      }
    } catch (e) {
      logger.e('設置用戶設定失敗: $e');
      return false;
    }
  }

  /// 通過 REST API 設置用戶設定
  static Future<bool> _setUserSettingsViaRestApi(String userId, Map<String, dynamic> data) async {
    try {
      final url = FirebaseConfigWindows.getDocumentUrl('user_settings', userId);

      final response = await http.put(
        Uri.parse(url),
        headers: {'Content-Type': 'application/json'},
        body: json.encode(data),
      );

      if (response.statusCode == 200) {
        logger.i('通過 REST API 設置用戶設定成功: $userId');
        return true;
      } else {
        logger.e('通過 REST API 設置用戶設定失敗: ${response.statusCode} - ${response.body}');
        return false;
      }
    } catch (e) {
      logger.e('通過 REST API 設置用戶設定失敗: $e');
      return false;
    }
  }

  /// 通過 SDK 設置用戶設定
  static Future<bool> _setUserSettingsViaSDK(String userId, Map<String, dynamic> data) async {
    try {
      // 使用 Firestore SDK 設置用戶設定
      // final firestore = FirebaseFirestore.instance;
      // final docRef = firestore.collection('user_settings').doc(userId);

      // // 添加伺服器時間戳
      // data['updated_at'] = FieldValue.serverTimestamp();
      // if (!data.containsKey('created_at')) {
      //   data['created_at'] = FieldValue.serverTimestamp();
      // }

      // await docRef.set(data, SetOptions(merge: true));
      // logger.i('通過 SDK 設置用戶設定成功: $userId');
      // return true;

      // 暫時實現：由於 cloud_firestore 被註釋，返回 false
      logger.w('SDK 版本需要 cloud_firestore 依賴，暫時返回 false');
      return false;
    } catch (e) {
      logger.e('通過 SDK 設置用戶設定失敗: $e');
      return false;
    }
  }

  /// 獲取用戶偏好設定
  static Future<Map<String, dynamic>?> _getUserPreferences(String userId) async {
    try {
      if (Platform.isWindows) {
        return await _getUserPreferencesViaRestApi(userId);
      } else {
        return await _getUserPreferencesViaSDK(userId);
      }
    } catch (e) {
      logger.e('獲取用戶偏好設定失敗: $e');
      return null;
    }
  }

  /// 通過 REST API 獲取用戶偏好設定
  static Future<Map<String, dynamic>?> _getUserPreferencesViaRestApi(String userId) async {
    try {
      final url = FirebaseConfigWindows.getDocumentUrl(FirebaseCollections.userPreferences, userId);

      final response = await http.get(Uri.parse(url));

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        if (data != null && data is Map<String, dynamic>) {
          logger.d('通過 REST API 獲取用戶偏好設定成功: $userId');
          return data;
        }
      }

      logger.d('用戶偏好設定不存在: $userId');
      return null;
    } catch (e) {
      logger.e('通過 REST API 獲取用戶偏好設定失敗: $e');
      return null;
    }
  }

  /// 通過 SDK 獲取用戶偏好設定
  static Future<Map<String, dynamic>?> _getUserPreferencesViaSDK(String userId) async {
    try {
      // 使用 Firestore SDK 獲取用戶偏好設定
      // final firestore = FirebaseFirestore.instance;
      // final docRef = firestore.collection('user_preferences').doc(userId);
      // final docSnapshot = await docRef.get();

      // if (docSnapshot.exists) {
      //   final data = docSnapshot.data() as Map<String, dynamic>?;
      //   if (data != null) {
      //     logger.d('通過 SDK 獲取用戶偏好設定成功: $userId');
      //     return data;
      //   }
      // }

      // logger.d('用戶偏好設定不存在: $userId');
      // return null;

      // 暫時實現：由於 cloud_firestore 被註釋，返回 null
      logger.w('SDK 版本需要 cloud_firestore 依賴，暫時返回 null');
      return null;
    } catch (e) {
      logger.e('通過 SDK 獲取用戶偏好設定失敗: $e');
      return null;
    }
  }
}
